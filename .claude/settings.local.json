{"permissions": {"allow": ["Bash(npm install:*)", "Bash(find:*)", "Bash(npm run dev:fast:*)", "Bash(npm run dev:*)", "Bash(NODE_OPTIONS=--openssl-legacy-provider npm run dev)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "Bash(defaults read:*)", "<PERSON><PERSON>(open:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(brew install:*)", "Bash(/opt/homebrew/bin/python3.12:*)", "<PERSON><PERSON>(sudo:*)", "Bash(/opt/homebrew/bin/pip3.12 install black)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "Bash(cp:*)", "Bash(system_profiler:*)", "<PERSON><PERSON>(docker:*)", "Bash(rm:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}