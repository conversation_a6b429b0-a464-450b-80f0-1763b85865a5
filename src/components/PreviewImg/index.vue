<template>
  <!-- 图片暂时及预览 -->
  <el-image :class="{ hoverStyle: isHover }" :style="imgStyle" :src="previewImgList(1)" :fit="fit"
    scroll-container=".el-table__body-wrapper" :preview-src-list="previewImgList()">
    <div slot="placeholder" class="image-slot loading-placeholder" :style="imgStyle">
      <i class="el-icon-loading loading-icon"></i>
    </div>
    <div slot="error" class="image-slot error-placeholder" :style="imgStyle">
      No
    </div>
  </el-image>
</template>

<script>
import { VUE_BASE_UPLOAD } from "@/api/config"
export default {
  name: "PreviewImg",
  props: {
    width: {
      type: String,
      default: "80px"
    },
    height: {
      type: String,
      default: "80px"
    },
    radius: {
      type: String,
      default: "5px"
    },
    bg: {
      type: String,
      default: "#f5f7fa"
    },
    imgUrl: {
      required: true
    },
    fit: {
      type: String,
      default: "contain"
    },
    isHover: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      host: VUE_BASE_UPLOAD
    };
  },
  computed: {
    previewImgList() {
      return num => {
        if (this.imgUrl) {
          let imgUrl = this.imgUrl;
          const imgList = imgUrl.split(",").map(url => this.formatImageUrl(url.trim()));
          if (num === 1) {
            return imgList[0];
          } else {
            return imgList;
          }
        } else {
          return num === 1 ? "" : [];
        }
      };
    },
    imgStyle() {
      return {
        width: this.width,
        height: this.height,
        borderRadius: this.radius,
        background: this.bg
      };
    }
  },
  methods: {
    /**
     * 格式化图片URL，自动拼接上传服务器地址
     * 根据环境自动选择：
     * - test环境: http://api.binyo.net + url
     * - 正式环境: https://storeapi.addmotor.com + url
     * @param {string} url - 原始URL
     * @returns {string} - 格式化后的完整URL
     */
    formatImageUrl(url) {
      if (!url) {
        return '';
      }

      // 如果已经是完整的URL（包含http或https），直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 确保 host 存在
      const baseUrl = this.host || VUE_BASE_UPLOAD;

      // 如果是相对路径，拼接上传服务器地址
      if (url.startsWith('/')) {
        return baseUrl + url;
      }

      // 如果不是以/开头的相对路径，添加/再拼接
      return baseUrl + '/' + url;
    }
  }
};
</script>

<style lang="scss" scoped>
.hoverStyle {
  transition: 0.3s ease-in-out;

  &:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  }
}

// 图片占位符样式
.image-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 14px;
  width: 100% !important;
  height: 100% !important;
}

// loading状态样式
.loading-placeholder {
  .loading-icon {
    font-size: 20px;
    color: #409eff;
    animation: rotating 2s linear infinite;
  }
}

// 错误状态样式
.error-placeholder {
  color: #909399;
  font-size: 12px;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
