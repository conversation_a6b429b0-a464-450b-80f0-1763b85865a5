<template>
  <div class="image-cover-upload" :style="{
    'width': '100%',
    'height': '100%',
    '--upload-width': typeof width === 'number' ? width + 'px' : width,
    '--upload-height': typeof height === 'number' ? height + 'px' : height
  }">
    <el-upload class="upload-demo" :class="{ 'hide-upload': isLimitReached }" :action="uploadAction"
      list-type="picture-card" :auto-upload="true" :style="{
        'height': typeof height === 'number' ? height + 'px' : height
      }" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :file-list="fileList" :limit="limit"
      :on-exceed="handleExceed" :before-upload="handleChange" :on-success="handleUploadSuccess"
      :on-error="handleUploadError" :http-request="handleCustomUpload">
      <i class="el-icon-plus"></i>
    </el-upload>

    <el-dialog :visible.sync="dialogVisible" append-to-body show-close close-on-click-modal modal-append-to-body
      style="z-index: 9999;">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { VUE_BASE_UPLOAD } from "@/api/config";

export default {
  name: 'ImageCoverUpload',
  props: {
    value: {
      type: [Array, String],
      default: () => []
    },
    limit: {
      type: Number,
      default: 5
    },
    width: {
      type: [Number, String],
      default: 140
    },
    height: {
      type: [Number, String],
      default: 140
    },
    // 上传方式：使用分段上传
    uploadMode: {
      type: String,
      default: 'chunk'
    }
  },
  data() {
    return {
      VUE_BASE_UPLOAD,  // 添加到data中以便模板使用
      fileList: this.formatFileList(this.value),
      dialogImageUrl: '',
      dialogVisible: false,
      uploading: false,
      uploadProgress: 0,
      currentFile: null,
      chunks: 0,
      currentChunk: 0,
      fileMd5: '',
      CHUNK_SIZE: 5 * 1024 * 1024 // 5MB
    };
  },
  computed: {
    uploadAction() {
      // return 'http://api.binyo.net/app-store/upload-fd?do_action=action.segmentFd';
      return VUE_BASE_UPLOAD + '/app-store/upload-path';
    },

    // 检查是否达到限制
    isLimitReached() {
      return this.fileList.length >= this.limit;
    },

    // 剩余可上传数量
    remainingCount() {
      return Math.max(0, this.limit - this.fileList.length);
    }
  },
  watch: {
    value(newVal) {
      this.fileList = this.formatFileList(newVal);
    }
  },
  mounted() {
    // 动态加载 SparkMD5 库
    this.loadSparkMD5();

    // 初始化时确保fileList正确设置
    this.fileList = this.formatFileList(this.value);
    console.log("🚀 ~ 组件初始化，value:", this.value, "fileList:", this.fileList);
  },
  methods: {
    // 加载 SparkMD5 库
    loadSparkMD5() {
      if (window.SparkMD5) {
        return Promise.resolve();
      }

      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/spark-md5/3.0.2/spark-md5.min.js';
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load SparkMD5'));
        document.head.appendChild(script);
      });
    },

    // 格式化文件列表，支持字符串和数组
    formatFileList(value) {
      if (!value) {
        return [];
      }

      if (typeof value === 'string') {
        const urls = value.split(',').filter(url => url.trim());
        return urls.map((url, index) => {
          const trimmedUrl = url.trim();
          // 如果URL不是完整的HTTP链接，则拼接域名
          const fullUrl = trimmedUrl.startsWith('http') ? trimmedUrl : VUE_BASE_UPLOAD + trimmedUrl;
          return {
            url: fullUrl,
            name: `cover_${index}`,
            uid: `uid_${index}`
          };
        });
      }

      if (Array.isArray(value)) {
        return value.map((url, index) => {
          // 如果URL不是完整的HTTP链接，则拼接域名
          const fullUrl = url.startsWith('http') ? url : VUE_BASE_UPLOAD + url;
          return {
            url: fullUrl,
            name: `cover_${index}`,
            uid: `uid_${index}`
          };
        });
      }

      return [];
    },

    // 格式化输出，根据原始value类型返回对应格式
    formatOutput(urls) {
      // 去掉域名部分，只保存路径
      const pathUrls = urls.map(url => {
        if (url.startsWith(VUE_BASE_UPLOAD)) {
          return url.replace(VUE_BASE_UPLOAD, '');
        }
        return url;
      });

      if (this.limit === 1) {
        return pathUrls[0];
      }
      if (typeof this.value === 'string') {
        return pathUrls.join(',');
      }
      return pathUrls;
    },

    handleRemove(file, fileList) {
      console.log("🚀 ~ 删除文件:", file, fileList);
      // 从fileList中提取所有有效的URL
      const urls = fileList
        .filter(item => {
          // 检查是否有有效的URL
          if (item.response && item.response.url) {
            return true;
          }
          // 使用item.url，但排除blob URL
          if (item.url && !item.url.startsWith('blob:')) {
            return true;
          }
          return false;
        })
        .map(item => {
          // 优先返回服务器URL，如果是完整URL则直接使用，否则拼接域名
          if (item.response && item.response.url) {
            const serverUrl = String(item.response.url);
            return serverUrl.startsWith('http') ? serverUrl : VUE_BASE_UPLOAD + serverUrl;
          }
          return item.url;
        });

      console.log("🚀 ~ 更新后的URLs:", urls);
      this.$emit('input', this.formatOutput(urls));
      this.$emit('change', this.formatOutput(urls));
    },

    handleUploadSuccess(response, file, fileList) {
      console.log("🚀 ~ 上传成功:", response, file, fileList);
      // 从fileList中提取所有有效的URL
      const urls = fileList
        .filter(item => {
          // 检查是否有有效的URL
          if (item.response && item.response.url) {
            return true;
          }
          // 使用item.url，但排除blob URL
          if (item.url && !item.url.startsWith('blob:')) {
            return true;
          }
          return false;
        })
        .map(item => {
          // 优先返回服务器URL，如果是完整URL则直接使用，否则拼接域名
          if (item.response && item.response.url) {
            const serverUrl = String(item.response.url);
            return serverUrl.startsWith('http') ? serverUrl : VUE_BASE_UPLOAD + serverUrl;
          }
          return item.url;
        });

      console.log("🚀 ~ 更新后的URLs:", urls);
      this.$emit('input', this.formatOutput(urls));
      this.$emit('change', this.formatOutput(urls));
      this.$emit('upload-success', response, file, fileList);
    },

    handleUploadError(error, file, fileList) {
      console.error("🚀 ~ 上传失败:", error, file, fileList);
      this.$message.error(`上传失败: ${error.message || error}`);
      this.$emit('upload-error', error, file, fileList);
    },

    handlePictureCardPreview(file) {
      console.log("🚀 ~ file: index.vue:56 ~ file:", file)
      // file.url 现在已经包含完整URL了，无需再拼接
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    handleExceed(files, fileList) {
      console.log("🚀 ~ 超出限制:", files, fileList, "当前限制:", this.limit);
      this.$message.warning(`当前限制选择 ${this.limit} 张图片，已选择 ${fileList.length} 张，本次选择了 ${files.length} 张，超出限制 ${files.length} 张`);
    },

    // 使用 http-request 来完全自定义上传
    async handleCustomUpload(uploadRequest) {
      console.log("🚀 ~ 自定义上传请求:", uploadRequest, this.limit, this.fileList);
      const file = uploadRequest.file;

      // 检查是否超过限制
      if (this.isLimitReached) {
        this.$message.warning(`当前限制选择 ${this.limit} 张图片，已选择 ${this.fileList.length} 张，无法继续上传`);
        uploadRequest.onError(new Error(`已达到最大上传数量限制: ${this.limit}`));
        return;
      }

      // 检查文件类型和大小
      const isImage = file.type.startsWith('image/');
      const isLt100M = file.size / 1024 / 1024 < 500;

      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        uploadRequest.onError(new Error('只能上传图片文件!'));
        return;
      }
      if (!isLt100M) {
        this.$message.error('上传图片大小不能超过 100MB!');
        uploadRequest.onError(new Error('上传图片大小不能超过 100MB!'));
        return;
      }

      try {
        // 开始分段上传
        const result = await this.startChunkUpload(file, uploadRequest.onProgress);

        console.log("🚀 ~ 上传完成，返回结果:", result);

        // 通知 Element UI 上传成功
        uploadRequest.onSuccess({
          status: 200,
          message: '上传成功',
          url: result.url,
          name: result.name
        });

      } catch (error) {
        console.error('上传失败:', error);
        uploadRequest.onError(error);
      }
    },

    handleChange(file, fileList) {
      console.log("🚀 ~ 文件状态变化:", file, fileList);
      // 更新文件列表
      if (file.status === 'success') {
        // 从fileList中提取所有有效的URL
        const urls = fileList
          .filter(item => item.url || (item.response && item.response.url))
          .map(item => item.url || (item.response && item.response.url));

        console.log("🚀 ~ 文件状态变化后的URLs:", urls);
        this.$emit('input', this.formatOutput(urls));
        this.$emit('change', this.formatOutput(urls));
      }
    },

    // 开始分段上传 - 和HTML完全一样的逻辑
    async startChunkUpload(file, onProgress) {
      if (!file) {
        throw new Error('请先选择文件');
      }

      this.uploading = true;
      this.uploadProgress = 0;
      this.currentFile = file;

      try {
        // 确保 SparkMD5 已加载
        await this.loadSparkMD5();

        const chunks = Math.ceil(file.size / this.CHUNK_SIZE);
        const lastChunkSize = file.size - (chunks - 1) * this.CHUNK_SIZE;

        this.chunks = chunks;
        this.currentChunk = 0;

        console.log(`文件名: ${file.name}`);
        console.log(`文件大小: ${this.formatBytes(file.size)}`);
        console.log(`分块数量: ${chunks}`);
        console.log(`最后分块大小: ${this.formatBytes(lastChunkSize)}`);

        // 计算文件MD5 - 使用真实的SparkMD5库
        console.log('计算文件MD5哈希值...');
        const fileMd5 = await this.calculateFileMD5(file);
        this.fileMd5 = fileMd5;
        console.log(`文件MD5: ${fileMd5}`);

        // 上传所有分块
        let uploadResult = null;
        for (let i = 0; i < chunks; i++) {
          const start = i * this.CHUNK_SIZE;
          const end = Math.min(start + this.CHUNK_SIZE, file.size);
          const chunk = file.slice(start, end);

          this.currentChunk = i + 1;
          console.log(`上传分块 ${i + 1}/${chunks} (${this.formatBytes(chunk.size)})`);

          // 计算分块MD5
          const chunkMd5 = await this.calculateChunkMD5(chunk);
          console.log(`分块 ${i + 1} MD5: ${chunkMd5}`);

          // 上传分块
          const result = await this.uploadChunk(file, chunk, i, chunks, fileMd5, chunkMd5);
          if (!result.success) {
            throw new Error(`分块 ${i + 1} 上传失败`);
          }

          // 保存最后一个分块的返回结果
          if (result.data) {
            uploadResult = result.data;
          }

          // 更新进度
          const percent = Math.round(((i + 1) / chunks) * 100);
          this.uploadProgress = percent;

          // 通知 Element UI 进度
          if (onProgress) {
            onProgress({
              percent: percent,
              loaded: (i + 1) * this.CHUNK_SIZE,
              total: file.size
            });
          }
        }

        this.$message.success('文件上传成功!');

        // 拼接完整的URL路径
        let finalPath = '';
        if (uploadResult && uploadResult.path) {
          finalPath = uploadResult.path;
          console.log('🚀 ~ 使用的path:', finalPath);
        } else {
          finalPath = `/u_file/uploads_fd/${file.name}`;
          console.log('🚀 ~ 使用默认path:', finalPath);
        }

        return {
          url: finalPath, // 只返回path部分
          name: file.name,
          status: 'success'
        };

      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
        this.currentFile = null;
        this.chunks = 0;
        this.currentChunk = 0;
        this.fileMd5 = '';
      }
    },

    // 使用SparkMD5计算文件MD5 - 和HTML完全一样
    calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const chunkSize = 2 * 1024 * 1024; // 2MB chunks for hashing
        const chunks = Math.ceil(file.size / chunkSize);
        const spark = new window.SparkMD5.ArrayBuffer();
        let currentChunk = 0;

        const loadNext = () => {
          const start = currentChunk * chunkSize;
          const end = Math.min(start + chunkSize, file.size);
          const reader = new FileReader();

          reader.onload = (e) => {
            spark.append(e.target.result);
            currentChunk++;

            if (currentChunk < chunks) {
              loadNext();
            } else {
              resolve(spark.end());
            }
          };

          reader.onerror = () => {
            reject(new Error('文件读取失败'));
          };

          reader.readAsArrayBuffer(file.slice(start, end));
        };

        loadNext();
      });
    },

    // 计算分块MD5 - 和HTML完全一样
    calculateChunkMD5(chunk) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const spark = new window.SparkMD5.ArrayBuffer();
          spark.append(e.target.result);
          resolve(spark.end());
        };
        reader.onerror = () => {
          reject(new Error('分块读取失败'));
        };
        reader.readAsArrayBuffer(chunk);
      });
    },

    // 上传分块 - 和HTML完全一样，增加重试机制
    async uploadChunk(file, chunk, chunkIndex, chunks, fileMd5, chunkMd5) {
      const MAX_RETRIES = 3;
      let attempt = 0;

      while (attempt < MAX_RETRIES) {
        try {
          const formData = new FormData();
          formData.append('Path_0[]', chunk);
          formData.append('name', file.name);
          formData.append('md5', fileMd5);
          formData.append('chunk', chunkIndex);
          formData.append('chunks', chunks);
          formData.append('totalSize', file.size);
          formData.append('chunk_md5', chunkMd5);

          const response = await fetch(this.uploadAction, {
            method: 'POST',
            body: formData
          });

          const result = await response.json();
          console.log("🚀 ~ 分块上传响应:", result);

          if (result.status !== 200) {
            throw new Error(result.message || '上传失败');
          }

          // 如果是最后一个分块，返回完整的URL信息
          if (chunkIndex === chunks - 1) {
            return {
              success: true,
              data: result.data
            };
          }

          return { success: true };

        } catch (error) {
          attempt++;
          if (attempt >= MAX_RETRIES) {
            throw error;
          }

          // 指数退避
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * Math.pow(2, attempt - 1))
          );
        }
      }
    },

    // 格式化字节显示 - 和HTML完全一样
    formatBytes(bytes, decimals = 2) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    // 备用方法：读取文件为 ArrayBuffer
    readFileAsArrayBuffer(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsArrayBuffer(file);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.image-cover-upload {
  position: relative;
  overflow: hidden;
  width: fit-content !important;
  height: fit-content !important;
  padding: 5px 0;
}

.upload-demo ::v-deep .el-upload--picture-card {
  width: var(--upload-width, 140px);
  height: var(--upload-height, 140px);
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}


.upload-demo .el-upload--picture-card i {
  font-size: 28px;
  color: #8c939d;
  transition: color 0.3s ease;
}

.upload-demo .el-upload--picture-card:hover i {
  color: #409eff;
}

/* 上传提示 */
.upload-demo .el-upload--picture-card .upload-tip {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #909399;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

/* 隐藏上传按钮 */
.upload-demo.hide-upload .el-upload--picture-card {
  display: none !important;
}

/* 上传进度显示 */
.upload-progress {
  margin-top: 10px;
}

.upload-info {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.upload-info p {
  margin: 2px 0;
}

/* 美化上传列表 */
::v-deep .el-upload-list--picture-card {
  .el-upload-list__item {
    width: var(--upload-width, 140px);
    height: var(--upload-height, 140px);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;

    .el-upload-list__item-thumbnail {
      border-radius: 6px;
    }

    .el-upload-list__item-actions {
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      border-radius: 6px;

      .el-upload-list__item-preview,
      .el-upload-list__item-delete {
        color: #fff;
        font-size: 16px;
        transition: all 0.2s ease;

        &:hover {
          color: #409eff;
          transform: scale(1.2);
        }
      }

      .el-upload-list__item-delete:hover {
        color: #f56c6c;
      }
    }
  }
}

/* 美化预览对话框 */
::v-deep .el-dialog {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  .el-dialog__body {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;

    img {
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      max-height: 70vh;
      object-fit: contain;
    }
  }
}

.hide-upload {
  ::v-deep .el-upload--picture-card {
    display: none !important;
  }
}
</style>