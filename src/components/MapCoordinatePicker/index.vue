<template>
  <div class="map-coordinate-picker">
    <!-- 地图容器 -->
    <div :id="mapContainerId" class="map-container"></div>

    <!-- 搜索框 -->
    <div class="map-search">
      <el-autocomplete v-model="searchQuery" :fetch-suggestions="searchPlaces"
        :placeholder="$t('mapCoordinatePicker.searchPlaceholder')" class="search-input" :trigger-on-focus="false"
        @select="onPlaceSelected" @keyup.enter.native="onSearchEnter" clearable :loading="searchLoading">
        <template slot="prepend">
          <i class="el-icon-search"></i>
        </template>
        <template slot-scope="{ item }">
          <div class="search-suggestion-item">
            <div class="place-name">{{ item.value }}</div>
            <div class="place-address">{{ item.address }}</div>
          </div>
        </template>
      </el-autocomplete>
    </div>

    <!-- 控制面板 -->
    <div class="map-controls">
      <el-button type="info" size="small" icon="el-icon-refresh" @click="resetMap" :disabled="!isMapLoaded">
        {{ $t('mapCoordinatePicker.resetMap') }}
      </el-button>
    </div>

    <!-- 坐标信息面板 -->
    <div class="coordinate-panel" v-if="selectedCoordinate">
      <div class="panel-header">
        <h4>{{ $t('mapCoordinatePicker.selectedCoordinate') }}</h4>
      </div>
      <div class="coordinate-info">
        <div class="coordinate-item">
          <label>{{ $t('mapCoordinatePicker.latitude') }}:</label>
          <span>{{ selectedCoordinate.lat.toFixed(6) }}</span>
        </div>
        <div class="coordinate-item">
          <label>{{ $t('mapCoordinatePicker.longitude') }}:</label>
          <span>{{ selectedCoordinate.lng.toFixed(6) }}</span>
        </div>
        <div class="coordinate-item" v-if="selectedAddress">
          <label>{{ $t('mapCoordinatePicker.address') }}:</label>
          <span>{{ selectedAddress }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MapCoordinatePicker',
  props: {
    // 初始坐标
    initialCoordinate: {
      type: Object,
      default: () => ({ lng: 116.3974, lat: 39.9093 }) // 默认北京
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      map: null,
      mapboxgl: null,
      mapContainerId: 'map-picker-' + Math.random().toString(36).substr(2, 9),
      isMapLoaded: false,
      selectedCoordinate: null,
      selectedAddress: '',
      currentMarker: null,
      searchQuery: '',
      searchLoading: false,
      // Mapbox配置
      mapboxConfig: {
        accessToken: process.env.VUE_APP_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYWRkbW90b3IiLCJhIjoiY2x6dGltaDFzMmgybzJtb2NtNmsxYTIxaCJ9.b3yBd9nkM3EDNzebk_gVDA',
        style: 'mapbox://styles/mapbox/streets-v11'
      }
    }
  },
  mounted() {
    this.loadMapboxGL()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
    if (this.currentMarker) {
      this.currentMarker.remove()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      if (this.map && this.isMapLoaded) {
        setTimeout(() => {
          this.map.resize()
        }, 100)
      }
    },

    // 动态加载 Mapbox GL JS
    loadMapboxGL() {
      if (window.mapboxgl) {
        this.mapboxgl = window.mapboxgl
        this.initMap()
        return
      }

      // 加载 CSS
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
      document.head.appendChild(link)

      // 加载 JS
      const script = document.createElement('script')
      script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'
      script.onload = () => {
        this.mapboxgl = window.mapboxgl
        this.initMap()
      }
      script.onerror = () => {
        this.$message.error(this.$t('mapCoordinatePicker.mapLoadError'))
      }
      document.head.appendChild(script)
    },

    // 初始化地图
    initMap() {
      try {
        // 验证坐标
        const center = this.validateCoordinate(this.initialCoordinate)

        this.mapboxgl.accessToken = this.mapboxConfig.accessToken

        this.map = new this.mapboxgl.Map({
          container: this.mapContainerId,
          style: this.mapboxConfig.style,
          center: [center.lng, center.lat],
          zoom: this.zoom,
          pitchWithRotate: false,
          dragRotate: false,
          touchZoomRotate: false,
          attributionControl: false
        })

        // 添加地图控件
        this.map.addControl(new this.mapboxgl.NavigationControl(), 'top-right')
        this.map.addControl(new this.mapboxgl.FullscreenControl(), 'top-right')

        // 地图加载完成
        this.map.on('load', () => {
          this.isMapLoaded = true
          this.$emit('map-loaded')

          // 如果有初始坐标且不是默认值，添加标记
          if (this.initialCoordinate.lng !== 116.3974 || this.initialCoordinate.lat !== 39.9093) {
            this.addMarkerAtLocation(center)
          }

          setTimeout(() => {
            this.map.resize()
          }, 100)
        })

        // 点击地图添加/移动标记
        this.map.on('click', (e) => {
          if (this.isMapLoaded) {
            this.addMarkerAtLocation(e.lngLat)
          }
        })

        // 地图错误处理
        this.map.on('error', (e) => {
          console.error('地图加载错误:', e)
          this.$message.error(this.$t('mapCoordinatePicker.mapLoadError'))
        })

      } catch (error) {
        console.error('地图初始化失败:', error)
        this.$message.error(this.$t('mapCoordinatePicker.mapInitError') + ': ' + error.message)
      }
    },

    // 验证坐标
    validateCoordinate(coord) {
      const lng = parseFloat(coord.lng)
      const lat = parseFloat(coord.lat)

      // 检查经纬度范围
      if (isNaN(lng) || lng < -180 || lng > 180) {
        console.warn('无效的经度值:', coord.lng, '使用默认值')
        return { lng: 116.3974, lat: 39.9093 }
      }

      if (isNaN(lat) || lat < -90 || lat > 90) {
        console.warn('无效的纬度值:', coord.lat, '使用默认值')
        return { lng: 116.3974, lat: 39.9093 }
      }

      return { lng, lat }
    },

    // 在指定位置添加标记
    async addMarkerAtLocation(lngLat) {
      // 移除之前的标记
      if (this.currentMarker) {
        this.currentMarker.remove()
      }

      // 验证坐标
      const coord = this.validateCoordinate(lngLat)

      // 创建标记元素
      const el = document.createElement('div')
      el.style.cssText = `
        width: 30px; 
        height: 30px; 
        background: #ff4d4f; 
        color: white; 
        border-radius: 50%; 
        display: flex; 
        align-items: center; 
        justify-content: center; 
        font-weight: bold; 
        font-size: 16px; 
        cursor: pointer; 
        border: 3px solid white; 
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        transition: all 0.2s ease;
      `
      el.innerHTML = '📍'

      // 创建标记
      this.currentMarker = new this.mapboxgl.Marker({
        element: el,
        draggable: true,
        anchor: 'center'
      })
        .setLngLat([coord.lng, coord.lat])
        .addTo(this.map)

      // 监听拖拽事件
      this.currentMarker.on('dragend', () => {
        const newCoord = this.currentMarker.getLngLat()
        this.updateSelectedCoordinate(newCoord)
      })

      // 更新选中坐标
      this.updateSelectedCoordinate(coord)

      // 获取地址信息
      try {
        const address = await this.getAddressFromCoordinate(coord.lng, coord.lat)
        this.selectedAddress = address || ''
      } catch (error) {
        console.warn('获取地址失败:', error)
        this.selectedAddress = ''
      }
    },

    // 更新选中坐标
    updateSelectedCoordinate(coord) {
      this.selectedCoordinate = {
        lng: coord.lng,
        lat: coord.lat
      }

      // 发送坐标变化事件
      this.$emit('coordinate-change', {
        longitude: coord.lng,
        latitude: coord.lat,
        address: this.selectedAddress
      })
    },

    // 通过坐标获取地址
    async getAddressFromCoordinate(lng, lat) {
      try {
        const accessToken = this.mapboxConfig.accessToken
        const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${accessToken}&language=zh-CN`

        const response = await fetch(url)
        const data = await response.json()

        if (data.features && data.features.length > 0) {
          const feature = data.features[0]
          return feature.place_name_zh || feature.place_name || feature.text || ''
        }

        return null
      } catch (error) {
        console.error('获取地址失败:', error)
        return null
      }
    },

    // 搜索地点
    searchPlaces(queryString, callback) {
      if (!queryString || queryString.length < 2) {
        callback([])
        return
      }

      this.searchLoading = true
      const accessToken = this.mapboxConfig.accessToken
      const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(queryString)}.json?access_token=${accessToken}&language=zh-CN&country=CN&types=poi,address,place&limit=10`

      fetch(url)
        .then(response => response.json())
        .then(data => {
          this.searchLoading = false
          if (data.features && data.features.length > 0) {
            const suggestions = data.features.map(feature => ({
              value: this.formatSearchResultName(feature),
              address: this.formatSearchResultAddress(feature),
              longitude: feature.center[0],
              latitude: feature.center[1]
            }))
            callback(suggestions)
          } else {
            callback([])
          }
        })
        .catch(() => {
          this.searchLoading = false
          callback([])
        })
    },

    // 格式化搜索结果名称
    formatSearchResultName(feature) {
      if (feature.text_zh) return feature.text_zh
      if (feature.text) return feature.text
      const placeName = feature.place_name || ''
      const parts = placeName.split(',')
      return parts[0] || placeName
    },

    // 格式化搜索结果地址
    formatSearchResultAddress(feature) {
      const placeName = feature.place_name || ''
      const parts = placeName.split(',')
      return parts.filter(part =>
        !part.includes('中国') &&
        !part.includes('China') &&
        part.trim().length > 0
      ).join(', ')
    },

    // 处理选择地点
    onPlaceSelected(item) {
      if (!item || !item.longitude || !item.latitude) {
        this.$message.warning(this.$t('mapCoordinatePicker.invalidPlace'))
        return
      }

      // 移动地图到选中位置
      this.map.flyTo({
        center: [item.longitude, item.latitude],
        zoom: 15,
        duration: 1000
      })

      // 添加标记
      this.addMarkerAtLocation({
        lng: item.longitude,
        lat: item.latitude
      })

      // 清空搜索框
      this.searchQuery = ''
    },

    // 处理搜索框回车
    onSearchEnter() {
      if (this.searchQuery.trim()) {
        this.searchPlaces(this.searchQuery, (suggestions) => {
          if (suggestions.length > 0) {
            this.onPlaceSelected(suggestions[0])
          } else {
            this.$message.warning(this.$t('mapCoordinatePicker.placeNotFound'))
          }
        })
      }
    },

    // 重置地图
    resetMap() {
      const defaultCenter = { lng: 116.3974, lat: 39.9093 }
      this.map.flyTo({
        center: [defaultCenter.lng, defaultCenter.lat],
        zoom: this.zoom,
        duration: 1000
      })

      // 移除标记
      if (this.currentMarker) {
        this.currentMarker.remove()
        this.currentMarker = null
      }

      this.selectedCoordinate = null
      this.selectedAddress = ''

      // 发送重置事件
      this.$emit('coordinate-change', null)
    },

    // 获取当前选中的坐标
    getSelectedCoordinate() {
      return this.selectedCoordinate ? {
        longitude: this.selectedCoordinate.lng,
        latitude: this.selectedCoordinate.lat,
        address: this.selectedAddress
      } : null
    }
  }
}
</script>

<style lang="scss" scoped>
.map-coordinate-picker {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.map-search {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .search-input {
    width: 100%;

    ::v-deep .el-input__inner {
      border: none;
      background: transparent;
      padding: 12px 16px;
      font-size: 14px;

      &:focus {
        box-shadow: none;
      }
    }

    ::v-deep .el-input-group__prepend {
      background: transparent;
      border: none;
      padding: 0 12px;

      i {
        color: #909399;
        font-size: 16px;
      }
    }
  }
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.coordinate-panel {
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;

  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #f5f7fa;
    border-radius: 8px 8px 0 0;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .coordinate-info {
    padding: 12px 16px;

    .coordinate-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 13px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        font-weight: 500;
        color: #606266;
      }

      span {
        color: #303133;
        font-family: monospace;
      }
    }
  }
}

.search-suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f5f7fa;
  }

  .place-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .place-address {
    font-size: 12px;
    color: #909399;
  }
}

@media (max-width: 768px) {
  .map-search {
    width: calc(100% - 20px);
    right: 10px;
  }

  .coordinate-panel {
    width: calc(100% - 20px);
    right: 10px;
  }
}
</style>