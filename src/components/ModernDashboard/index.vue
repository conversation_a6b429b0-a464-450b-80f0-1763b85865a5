<template>
  <div class="modern-dashboard">
    <!-- Header Section with Glassmorphism -->
    <div class="dashboard-header">
      <div class="header-backdrop"></div>
      <div class="header-content">
        <div class="header-main">
          <div class="welcome-section">
            <h1 class="welcome-title">{{ $t('dashboard.welcome') }}</h1>
            <p class="welcome-subtitle">{{ $t('dashboard.overview') }}</p>
          </div>
          <div class="header-actions">
            <el-button type="primary" round icon="el-icon-plus" size="medium">
              {{ $t('common.create') }}
            </el-button>
            <el-button type="default" round icon="el-icon-refresh" size="medium" @click="refreshData">
              {{ $t('common.refresh') }}
            </el-button>
          </div>
        </div>
        
        <!-- Quick Stats Grid -->
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in quickStats" :key="stat.id">
            <div class="stat-icon" :class="stat.iconClass">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-trend" :class="stat.trendClass">
                <i :class="stat.trendIcon"></i>
                <span>{{ stat.trend }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="dashboard-content">
      <!-- Chart Section -->
      <div class="content-section chart-section">
        <div class="section-card large">
          <div class="card-header">
            <div class="header-info">
              <h3 class="card-title">{{ $t('dashboard.analytics') }}</h3>
              <p class="card-subtitle">{{ $t('dashboard.last30Days') }}</p>
            </div>
            <div class="header-controls">
              <el-select v-model="chartPeriod" size="small" style="width: 120px">
                <el-option label="7天" value="7d"></el-option>
                <el-option label="30天" value="30d"></el-option>
                <el-option label="90天" value="90d"></el-option>
              </el-select>
            </div>
          </div>
          <div class="card-content">
            <div class="chart-placeholder">
              <div class="chart-visual">
                <div class="chart-bars">
                  <div class="bar" v-for="i in 12" :key="i" :style="{ height: Math.random() * 100 + 20 + '%' }"></div>
                </div>
                <div class="chart-overlay">
                  <div class="chart-label">{{ $t('dashboard.viewChart') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Feed -->
      <div class="content-section activity-section">
        <div class="section-card">
          <div class="card-header">
            <div class="header-info">
              <h3 class="card-title">{{ $t('dashboard.recentActivity') }}</h3>
              <p class="card-subtitle">{{ $t('dashboard.todaysUpdates') }}</p>
            </div>
            <el-button type="text" size="small">{{ $t('common.viewAll') }}</el-button>
          </div>
          <div class="card-content">
            <div class="activity-list">
              <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-avatar" :class="activity.avatarClass">
                  <i :class="activity.icon"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
                <div class="activity-status" :class="activity.statusClass">
                  {{ activity.status }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="content-section actions-section">
        <div class="section-card">
          <div class="card-header">
            <div class="header-info">
              <h3 class="card-title">{{ $t('dashboard.quickActions') }}</h3>
              <p class="card-subtitle">{{ $t('dashboard.commonTasks') }}</p>
            </div>
          </div>
          <div class="card-content">
            <div class="actions-grid">
              <div class="action-card" v-for="action in quickActions" :key="action.id" @click="handleAction(action)">
                <div class="action-icon" :class="action.iconClass">
                  <i :class="action.icon"></i>
                </div>
                <div class="action-content">
                  <div class="action-title">{{ action.title }}</div>
                  <div class="action-description">{{ action.description }}</div>
                </div>
                <div class="action-arrow">
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="content-section metrics-section">
        <div class="section-card">
          <div class="card-header">
            <div class="header-info">
              <h3 class="card-title">{{ $t('dashboard.performance') }}</h3>
              <p class="card-subtitle">{{ $t('dashboard.systemMetrics') }}</p>
            </div>
          </div>
          <div class="card-content">
            <div class="metrics-list">
              <div class="metric-item" v-for="metric in performanceMetrics" :key="metric.id">
                <div class="metric-info">
                  <div class="metric-label">{{ metric.label }}</div>
                  <div class="metric-value">{{ metric.value }}{{ metric.unit }}</div>
                </div>
                <div class="metric-progress">
                  <div class="progress-track">
                    <div class="progress-bar" :class="metric.statusClass" :style="{ width: metric.percentage + '%' }"></div>
                  </div>
                  <div class="progress-text">{{ metric.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Overview -->
      <div class="content-section users-section">
        <div class="section-card">
          <div class="card-header">
            <div class="header-info">
              <h3 class="card-title">{{ $t('dashboard.users') }}</h3>
              <p class="card-subtitle">{{ $t('dashboard.activeUsers') }}</p>
            </div>
            <el-button type="text" size="small">{{ $t('common.manage') }}</el-button>
          </div>
          <div class="card-content">
            <div class="users-stats">
              <div class="user-stat" v-for="userStat in userStats" :key="userStat.id">
                <div class="user-avatar-group">
                  <div class="user-avatar" v-for="i in Math.min(userStat.count, 4)" :key="i">
                    <div class="avatar-image" :style="{ background: getRandomGradient() }">
                      {{ getRandomInitial() }}
                    </div>
                  </div>
                  <div class="user-avatar more" v-if="userStat.count > 4">
                    +{{ userStat.count - 4 }}
                  </div>
                </div>
                <div class="user-info">
                  <div class="user-type">{{ userStat.type }}</div>
                  <div class="user-count">{{ userStat.count }} {{ $t('dashboard.users') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernDashboard',
  data() {
    return {
      chartPeriod: '30d',
      quickStats: [
        {
          id: 1,
          icon: 'el-icon-user',
          iconClass: 'users',
          value: '2,847',
          label: this.$t('dashboard.totalUsers'),
          trend: '+12.5%',
          trendIcon: 'el-icon-arrow-up',
          trendClass: 'positive'
        },
        {
          id: 2,
          icon: 'el-icon-bicycle',
          iconClass: 'activities',
          value: '1,234',
          label: this.$t('dashboard.activeRides'),
          trend: '+8.2%',
          trendIcon: 'el-icon-arrow-up',
          trendClass: 'positive'
        },
        {
          id: 3,
          icon: 'el-icon-money',
          iconClass: 'revenue',
          value: '¥45,892',
          label: this.$t('dashboard.revenue'),
          trend: '+15.3%',
          trendIcon: 'el-icon-arrow-up',
          trendClass: 'positive'
        },
        {
          id: 4,
          icon: 'el-icon-data-analysis',
          iconClass: 'conversion',
          value: '68.5%',
          label: this.$t('dashboard.conversion'),
          trend: '-2.1%',
          trendIcon: 'el-icon-arrow-down',
          trendClass: 'negative'
        }
      ],
      recentActivities: [
        {
          id: 1,
          icon: 'el-icon-user',
          avatarClass: 'user-activity',
          title: '新用户注册',
          description: '用户 张三 完成了账号注册',
          time: '2分钟前',
          status: '完成',
          statusClass: 'completed'
        },
        {
          id: 2,
          icon: 'el-icon-bicycle',
          avatarClass: 'ride-activity',
          title: '骑行活动',
          description: '环城骑行活动已开始',
          time: '15分钟前',
          status: '进行中',
          statusClass: 'in-progress'
        },
        {
          id: 3,
          icon: 'el-icon-warning',
          avatarClass: 'warning-activity',
          title: '系统告警',
          description: '服务器CPU使用率过高',
          time: '1小时前',
          status: '待处理',
          statusClass: 'pending'
        }
      ],
      quickActions: [
        {
          id: 1,
          icon: 'el-icon-plus',
          iconClass: 'create',
          title: '创建活动',
          description: '发起新的骑行活动'
        },
        {
          id: 2,
          icon: 'el-icon-setting',
          iconClass: 'settings',
          title: '系统设置',
          description: '配置系统参数'
        },
        {
          id: 3,
          icon: 'el-icon-data-analysis',
          iconClass: 'analytics',
          title: '数据分析',
          description: '查看详细报表'
        },
        {
          id: 4,
          icon: 'el-icon-user-solid',
          iconClass: 'users',
          title: '用户管理',
          description: '管理系统用户'
        }
      ],
      performanceMetrics: [
        {
          id: 1,
          label: 'CPU使用率',
          value: '45',
          unit: '%',
          percentage: 45,
          statusClass: 'good'
        },
        {
          id: 2,
          label: '内存使用率',
          value: '68',
          unit: '%',
          percentage: 68,
          statusClass: 'warning'
        },
        {
          id: 3,
          label: '磁盘使用率',
          value: '32',
          unit: '%',
          percentage: 32,
          statusClass: 'good'
        },
        {
          id: 4,
          label: '网络带宽',
          value: '156',
          unit: 'Mbps',
          percentage: 78,
          statusClass: 'warning'
        }
      ],
      userStats: [
        {
          id: 1,
          type: '管理员',
          count: 8
        },
        {
          id: 2,
          type: '普通用户',
          count: 1456
        },
        {
          id: 3,
          type: 'VIP用户',
          count: 234
        }
      ]
    }
  },
  methods: {
    refreshData() {
      this.$message.success('数据刷新成功')
    },
    handleAction(action) {
      this.$message.info(`执行操作: ${action.title}`)
    },
    getRandomGradient() {
      const gradients = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      ]
      return gradients[Math.floor(Math.random() * gradients.length)]
    },
    getRandomInitial() {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters.charAt(Math.floor(Math.random() * letters.length))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.modern-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow-x: hidden;

  // Dashboard Header with Glassmorphism
  .dashboard-header {
    position: relative;
    padding: 40px 32px;
    margin-bottom: 32px;
    overflow: hidden;

    .header-backdrop {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255,255,255,0.18);
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
      }
    }

    .header-content {
      position: relative;
      z-index: 2;

      .header-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;

        .welcome-section {
          .welcome-title {
            font-size: 32px;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, #2563eb, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .welcome-subtitle {
            font-size: 16px;
            color: #64748b;
            margin: 0;
            font-weight: 500;
          }
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 24px;

        .stat-card {
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 20px;
          padding: 28px;
          display: flex;
          align-items: center;
          gap: 20px;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
          }

          .stat-icon {
            width: 64px;
            height: 64px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #ffffff;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: -4px;
              left: -4px;
              right: -4px;
              bottom: -4px;
              border-radius: 22px;
              background: inherit;
              opacity: 0.2;
              z-index: -1;
            }

            &.users {
              background: linear-gradient(135deg, #3b82f6, #2563eb);
            }

            &.activities {
              background: linear-gradient(135deg, #10b981, #059669);
            }

            &.revenue {
              background: linear-gradient(135deg, #f59e0b, #d97706);
            }

            &.conversion {
              background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            }
          }

          .stat-content {
            flex: 1;

            .stat-value {
              font-size: 28px;
              font-weight: 800;
              color: #1e293b;
              line-height: 1;
              margin-bottom: 6px;
            }

            .stat-label {
              font-size: 14px;
              color: #64748b;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .stat-trend {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              font-weight: 600;

              &.positive {
                color: #10b981;
              }

              &.negative {
                color: #ef4444;
              }
            }
          }
        }
      }
    }
  }

  // Main Content Grid
  .dashboard-content {
    padding: 0 32px;
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 24px;
    grid-template-areas: 
      "chart activity"
      "actions metrics"
      "users users";

    .chart-section {
      grid-area: chart;
    }

    .activity-section {
      grid-area: activity;
    }

    .actions-section {
      grid-area: actions;
    }

    .metrics-section {
      grid-area: metrics;
    }

    .users-section {
      grid-area: users;
    }

    .section-card {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
        border-color: rgba(59, 130, 246, 0.2);
      }

      &.large {
        min-height: 400px;
      }

      .card-header {
        padding: 24px 28px 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;

        .header-info {
          .card-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 6px 0;
          }

          .card-subtitle {
            font-size: 14px;
            color: #64748b;
            margin: 0;
            font-weight: 500;
          }
        }
      }

      .card-content {
        padding: 0 28px 28px;
      }
    }
  }

  // Chart Placeholder
  .chart-placeholder {
    height: 320px;
    border-radius: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;

    .chart-visual {
      height: 100%;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      padding: 40px;
      position: relative;

      .chart-bars {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        height: 200px;

        .bar {
          width: 24px;
          background: linear-gradient(to top, #3b82f6, #60a5fa);
          border-radius: 4px 4px 0 0;
          animation: chartGrow 1s ease-out;
        }
      }

      .chart-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .chart-label {
          background: rgba(59, 130, 246, 0.9);
          color: #ffffff;
          padding: 12px 24px;
          border-radius: 12px;
          font-weight: 600;
          font-size: 14px;
          backdrop-filter: blur(10px);
        }
      }
    }
  }

  // Activity List
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);

      &:last-child {
        border-bottom: none;
      }

      .activity-avatar {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #ffffff;

        &.user-activity {
          background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        &.ride-activity {
          background: linear-gradient(135deg, #10b981, #059669);
        }

        &.warning-activity {
          background: linear-gradient(135deg, #f59e0b, #d97706);
        }
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-size: 15px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .activity-description {
          font-size: 13px;
          color: #64748b;
          margin-bottom: 4px;
        }

        .activity-time {
          font-size: 12px;
          color: #94a3b8;
        }
      }

      .activity-status {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;

        &.completed {
          background: rgba(16, 185, 129, 0.1);
          color: #059669;
        }

        &.in-progress {
          background: rgba(59, 130, 246, 0.1);
          color: #2563eb;
        }

        &.pending {
          background: rgba(245, 158, 11, 0.1);
          color: #d97706;
        }
      }
    }
  }

  // Actions Grid
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .action-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      border-radius: 16px;
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
      border: 1px solid rgba(59, 130, 246, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        border-color: rgba(59, 130, 246, 0.2);
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.1);
      }

      .action-icon {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #ffffff;

        &.create {
          background: linear-gradient(135deg, #10b981, #059669);
        }

        &.settings {
          background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        &.analytics {
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        &.users {
          background: linear-gradient(135deg, #3b82f6, #2563eb);
        }
      }

      .action-content {
        flex: 1;

        .action-title {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .action-description {
          font-size: 12px;
          color: #64748b;
        }
      }

      .action-arrow {
        color: #94a3b8;
        font-size: 16px;
        transition: transform 0.3s ease;
      }

      &:hover .action-arrow {
        transform: translateX(4px);
      }
    }
  }

  // Performance Metrics
  .metrics-list {
    .metric-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);

      &:last-child {
        border-bottom: none;
      }

      .metric-info {
        .metric-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .metric-value {
          font-size: 18px;
          font-weight: 700;
          color: #1e293b;
        }
      }

      .metric-progress {
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 120px;

        .progress-track {
          width: 80px;
          height: 8px;
          background: rgba(226, 232, 240, 0.5);
          border-radius: 4px;
          overflow: hidden;

          .progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;

            &.good {
              background: linear-gradient(to right, #10b981, #22c55e);
            }

            &.warning {
              background: linear-gradient(to right, #f59e0b, #facc15);
            }

            &.danger {
              background: linear-gradient(to right, #ef4444, #f87171);
            }
          }
        }

        .progress-text {
          font-size: 12px;
          color: #64748b;
          font-weight: 600;
          min-width: 32px;
        }
      }
    }
  }

  // Users Stats
  .users-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .user-stat {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      border-radius: 12px;
      background: linear-gradient(135deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0.2) 100%);
      border: 1px solid rgba(255,255,255,0.3);

      .user-avatar-group {
        display: flex;
        align-items: center;

        .user-avatar {
          margin-right: -8px;

          .avatar-image {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            border: 2px solid #ffffff;
          }

          &.more {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            color: #6b7280;
            border: 2px solid #ffffff;
          }
        }
      }

      .user-info {
        flex: 1;

        .user-type {
          font-size: 15px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .user-count {
          font-size: 13px;
          color: #64748b;
        }
      }
    }
  }
}

// Modern Animations
@keyframes chartGrow {
  from {
    height: 0;
  }
  to {
    height: var(--height);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .modern-dashboard {
    .dashboard-content {
      grid-template-columns: 1fr;
      grid-template-areas: 
        "chart"
        "activity"
        "actions"
        "metrics"
        "users";
    }

    .header-main {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
    }

    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    .dashboard-header {
      padding: 24px 20px;
    }

    .dashboard-content {
      padding: 0 20px;
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .actions-grid {
      grid-template-columns: 1fr;
    }

    .section-card {
      .card-header,
      .card-content {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
  }
}
</style>