<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('system.config.configName')" prop="configName">
        <el-input v-model="queryParams.configName" :placeholder="$t('system.config.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.config.configKey')" prop="configKey">
        <el-input v-model="queryParams.configKey" :placeholder="$t('system.config.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.config.configType')" prop="configType">
        <el-select v-model="queryParams.configType" :placeholder="$t('system.config.select')" clearable>
          <el-option v-for="dict in typeOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.config.createTime')">
        <el-date-picker v-model="dateRange" style="width: 190px" value-format="yyyy-MM-dd" type="datetimerange"
          range-separator="-" :start-placeholder="$t('acc.medal.beginTime')"
          :end-placeholder="$t('acc.medal.endTime')"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t("queryParams.search")
          }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("queryParams.reset")
          }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          hasPerminone="['system:config:add']">{{ $t("queryParams.add") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          hasPerminone="['system:config:edit']">{{ $t("queryParams.update") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          hasPerminone="['system:config:remove']">{{ $t("queryParams.delete") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
          hasPerminone="['system:config:export']">{{ $t("queryParams.export") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-refresh" size="mini" @click="handleClearCache"
          hasPerminone="['system:config:remove']">{{ $t("system.dict.clearCache") }}</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column :label="$t('system.config.configId')" align="center" prop="configId" />
      <el-table-column :label="$t('system.config.configName')" align="center" prop="configName"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.config.configKey')" align="center" prop="configKey"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.config.configValue')" align="center" prop="configValue" />
      <el-table-column :label="$t('system.config.configType')" align="center" prop="configType"
        :formatter="typeFormat" />
      <el-table-column :label="$t('system.config.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.config.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.config.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            hasPerminone="['system:config:edit']">{{ $t("queryParams.update") }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            hasPerminone="['system:config:remove']">{{ $t("queryParams.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.config.configName')" prop="configName">
          <el-input v-model="form.configName" :placeholder="$t('system.config.input')" />
        </el-form-item>
        <el-form-item :label="$t('system.config.configKey')" prop="configKey">
          <el-input v-model="form.configKey" :placeholder="$t('system.config.input')" />
        </el-form-item>
        <el-form-item :label="$t('system.config.configValue')" prop="configValue">
          <el-input v-model="form.configValue" :placeholder="$t('system.config.input')" />
        </el-form-item>
        <el-form-item :label="$t('system.config.configType')" prop="configType">
          <el-radio-group v-model="form.configType">
            <el-radio v-for="dict in typeOptions" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('system.config.remark')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="$t('system.config.input')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("dialog.confirm")
          }}</el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listConfig,
  getConfig,
  delConfig,
  addConfig,
  updateConfig,
  exportConfig,
  clearCache
} from "@/api/system/config";

export default {
  name: "Config",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      typeOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        configName: undefined,
        configKey: undefined,
        configType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          {
            required: true,
            message: this.$t("system.config.configName"),
            trigger: "blur"
          }
        ],
        configKey: [
          {
            required: true,
            message: this.$t("system.config.configKey"),
            trigger: "blur"
          }
        ],
        configValue: [
          {
            required: true,
            message: this.$t("system.config.configValue"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response => {
      this.typeOptions = response.data;
    });
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      listConfig(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.configList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 参数系统内置字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.configType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configName: undefined,
        configKey: undefined,
        configValue: undefined,
        configType: "Y",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("system.config.handleAdd");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids;
      getConfig(configId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("system.config.handleUpdate");
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != undefined) {
            updateConfig(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.updateSuccess"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addConfig(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.addSuccess"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$confirm(
        this.$t("system.config.handleDelete.text1") +
        dictIds +
        this.$t("system.config.handleDelete.text2"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return delConfig(configIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
        })
        .catch(function () { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("system.config.handleExport.text"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return exportConfig(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        })
        .catch(function () { });
    },
    /** 清理缓存按钮操作 */
    handleClearCache() {
      clearCache().then(response => {
        if (response.code === 200) {
          this.msgSuccess(this.$t("system.dict.clearCacheSuccess"));
        }
      });
    }
  }
};
</script>
