<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('system.dict.dictName')" prop="dictName" :label-width="formlabelW(110)">
        <el-input v-model="queryParams.dictName" :placeholder="$t('system.dict.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.dict.dictType')" prop="dictType">
        <el-input v-model="queryParams.dictType" :placeholder="$t('system.dict.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('system.dict.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('system.dict.select')" clearable>
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.dict.createTime')">
        <el-date-picker v-model="dateRange" size="small" style="width: 190px" value-format="yyyy-MM-dd"
          type="datetimerange" range-separator="-" :start-placeholder="$t('acc.medal.beginTime')"
          :end-placeholder="$t('acc.medal.endTime')"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("queryParams.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("queryParams.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd" hasPerminone="['system:dict:add']">
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-edit" :disabled="single" @click="handleUpdate"
          hasPerminone="['system:dict:edit']">
          {{ $t("queryParams.update") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" :disabled="multiple" @click="handleDelete"
          hasPerminone="['system:dict:remove']">
          {{ $t("queryParams.delete") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-download" @click="handleExport" hasPerminone="['system:dict:export']">
          {{ $t("queryParams.export") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-refresh" @click="handleClearCache" hasPerminone="['system:dict:remove']">
          {{ $t("system.dict.clearCache") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column :label="$t('system.dict.dictId')" align="center" prop="dictId" />
      <el-table-column :label="$t('system.dict.dictName')" align="center" prop="dictName"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.dict.dictType')" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <router-link :to="'/dict/type/data/' + scope.row.dictId" class="link-type">
            <span>{{ scope.row.dictType }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.dict.status')" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column :label="$t('system.dict.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.dict.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.dict.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            hasPerminone="['system:dict:edit']">{{ $t("queryParams.update") }}</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            hasPerminone="['system:dict:remove']">{{ $t("queryParams.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.dict.dictName')" prop="dictName">
          <el-input v-model="form.dictName" :placeholder="$t('system.dict.input')" />
        </el-form-item>
        <el-form-item :label="$t('system.dict.dictType')" prop="dictType">
          <el-input v-model="form.dictType" :placeholder="$t('system.dict.input')" />
        </el-form-item>
        <el-form-item :label="$t('system.dict.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('system.dict.remark')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="$t('system.dict.input')"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("dialog.confirm")
          }}</el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listType,
  getType,
  delType,
  addType,
  updateType,
  exportType,
  clearCache
} from "@/api/system/dict/type";

export default {
  name: "Dict",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        dictName: undefined,
        dictType: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dictName: [
          {
            required: true,
            message: this.$t("system.dict.rules.dictName"),
            trigger: "blur"
          }
        ],
        dictType: [
          {
            required: true,
            message: this.$t("system.dict.rules.dictType"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listType(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.typeList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dictId: undefined,
        dictName: undefined,
        dictType: undefined,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("system.dict.handleAdd");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dictId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dictId = row.dictId || this.ids;
      getType(dictId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("system.dict.handleUpdate");
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dictId != undefined) {
            updateType(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.updateSuccess"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addType(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.addSuccess"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictIds = row.dictId || this.ids;
      this.$confirm(
        this.$t("system.dict.handleDelete.text1") +
        dictIds +
        this.$t("system.dict.handleDelete.text2"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return delType(dictIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
        })
        .catch(function () { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("system.dict.handleExport.text"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return exportType(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        })
        .catch(function () { });
    },
    /** 清理缓存按钮操作 */
    handleClearCache() {
      clearCache().then(response => {
        if (response.code === 200) {
          this.msgSuccess(this.$t("system.dict.clearCacheSuccess"));
        }
      });
    }
  }
};
</script>
