<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" :placeholder="$t('system.user.deptName')" clearable size="small"
            prefix-icon="el-icon-search" style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" default-expand-all @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item :label="$t('system.user.userName')" prop="userName">
            <el-input v-model="queryParams.userName" :placeholder="$t('system.user.userNamePla')" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item :label="$t('system.user.phonenumber')" prop="phonenumber">
            <el-input v-model="queryParams.phonenumber" :placeholder="$t('system.user.phonenumberPla')" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item :label="$t('system.user.status')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="$t('system.user.statusPla')" clearable>
              <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="dict.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('system.user.dateRange')">
            <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="datetimerange"
              range-separator="-" :start-placeholder="$t('acc.medal.beginTime')"
              :end-placeholder="$t('acc.medal.endTime')" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              {{ $t("acc.medal.search") }}
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">
              {{ $t("acc.medal.reset") }}
            </el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd" v-hasPermi="['system:user:add']">
              {{ $t("system.user.add") }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="el-icon-edit" :disabled="single" @click="handleUpdate"
              hasPerminone="['system:user:edit']">{{ $t("system.user.edit") }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" :disabled="multiple" @click="handleDelete"
              hasPerminone="['system:user:remove']">{{ $t("system.user.delete") }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" icon="el-icon-upload2" @click="handleImport" hasPerminone="['system:user:import']">{{
              $t("system.user.leading") }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" icon="el-icon-download" @click="handleExport"
              hasPerminone="['system:user:export']">{{ $t("system.user.export") }}</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="$t('system.user.userId')" align="center" prop="userId" />
          <el-table-column :label="$t('system.user.userName')" align="center" prop="userName"
            :show-overflow-tooltip="true" />
          <el-table-column :label="$t('system.user.nickName')" align="center" prop="nickName"
            :show-overflow-tooltip="true" />
          <el-table-column :label="$t('system.user.dept')" align="center" prop="dept.deptName"
            :show-overflow-tooltip="true" />
          <el-table-column :label="$t('system.user.phonenumber')" align="center" prop="phonenumber" width="120" />
          <el-table-column :label="$t('system.user.status')" align="center">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column :label="$t('system.user.createTime')" align="center" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('system.user.handle')" align="center" width="180"
            class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                hasPerminone="['system:user:edit']">{{ $t("system.user.edit") }}</el-button>
              <el-button v-if="scope.row.userId !== 1" type="text" icon="el-icon-delete"
                @click="handleDelete(scope.row)" hasPerminone="['system:user:remove']">{{ $t("system.user.delete")
                }}</el-button>
              <el-button type="text" icon="el-icon-key" @click="handleResetPwd(scope.row)"
                hasPerminone="['system:user:resetPwd']">{{ $t("system.user.resetting") }}</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
          @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.nickName')" prop="nickName">
              <el-input v-model="form.nickName" :placeholder="$t('system.user.input')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.deptId')" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :disable-branch-nodes="true" :show-count="true"
                :placeholder="$t('system.user.select')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.phonenumber')" prop="phonenumber">
              <el-input v-model="form.phonenumber" :placeholder="$t('system.user.input')" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.email')" prop="email">
              <el-input v-model="form.email" :placeholder="$t('system.user.input')" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="$t('system.user.userName')" prop="userName">
              <el-input v-model="form.userName" :placeholder="$t('system.user.input')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="$t('system.user.password')" prop="password">
              <el-input v-model="form.password" :placeholder="$t('system.user.input')" type="password" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.sex')">
              <el-select v-model="form.sex" :placeholder="$t('system.user.select')">
                <el-option v-for="dict in sexOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.status')">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.postIds')">
              <el-select v-model="form.postIds" multiple :placeholder="$t('system.user.select')">
                <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName" :value="item.postId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.user.roleIds')">
              <el-select v-model="form.roleIds" multiple :placeholder="$t('system.user.select')">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.user.remark')">
              <el-input v-model="form.remark" type="textarea" :placeholder="$t('system.user.input')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :close-on-click-modal="false" :title="upload.title" :visible.sync="upload.open" width="400px"
      append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">{{
          $t("dialog.confirm")
        }}</el-button>
        <el-button @click="upload.open = false">{{
          $t("dialog.cancel")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  exportUser,
  resetUserPwd,
  changeUserStatus,
  importTemplate
} from "@/api/system/user";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import ModalDragMixins from "./ModalDragMixins";
import ADialog from "./a-model.vue";

export default {
  name: "User",
  mixins: [ModalDragMixins],
  components: { Treeselect, ADialog },
  data() {
    return {
      mixinsName: "marry",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      // 表单校验
      rules: {
        userName: [
          {
            required: true,
            message: this.$t("system.user.userNameRules"),
            trigger: "blur"
          }
        ],
        nickName: [
          {
            required: true,
            message: this.$t("system.user.nickNameRules"),
            trigger: "blur"
          }
        ],
        deptId: [
          {
            required: true,
            message: this.$t("system.user.deptIdRules"),
            trigger: "blur"
          }
        ],
        password: [
          {
            required: true,
            message: this.$t("system.user.passwordRules"),
            trigger: "blur"
          }
        ],
        email: [
          {
            required: true,
            message: this.$t("system.user.emailRules"),
            trigger: "blur"
          },
          {
            type: "email",
            message: this.$t("system.user.emailRulesProper"),
            trigger: ["blur", "change"]
          }
        ],
        phonenumber: [
          {
            required: true,
            message: this.$t("system.user.phonenumberRules"),
            trigger: "blur"
          },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: this.$t("system.user.phonenumberRulesProper"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getTreeselect();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then(response => {
      this.sexOptions = response.data;
    });
    this.getConfigKey("sys.user.initPassword").then(response => {
      this.initPassword = response.msg;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.getList();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === "0"
          ? this.$t("system.user.statusChange1")
          : this.$t("system.user.statusChange2");
      this.$confirm(
        this.$t("system.user.statusChange3") +
        text +
        '""' +
        row.userName +
        this.$t("system.user.statusChange4"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.msgSuccess(this.$t("dialog.updateSuccess"));
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect();
      getUser().then(response => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = this.$t("system.user.handleAdd");
        this.form.password = this.initPassword;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      const userId = row.userId || this.ids;
      getUser(userId).then(response => {
        this.$set(this, 'form', response.data);
        this.$set(this, 'postOptions', response.posts);
        this.$set(this, 'roleOptions', response.roles);
        this.$set(this.form, 'postIds', response.postIds);
        this.$set(this.form, 'roleIds', response.roleIds);
        this.open = true;
        this.title = this.$t("system.user.handleUpdate");
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt(
        this.$t("system.role.input") +
        row.userName +
        this.$t("system.role.handleResetPwd1"),
        this.$t("system.role.handleResetPwd2"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel")
        }
      )
        .then(({ value }) => {
          resetUserPwd(row.userId, value).then(response => {
            if (response.code === 200) {
              this.msgSuccess(this.$t("dialog.updateSuccess"));
            }
          });
        })
        .catch(() => { });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.updateSuccess"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addUser(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("dialog.addSuccess"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId || this.ids;
      this.$confirm(
        this.$t("system.role.deletePoint"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
        })
        .catch(function () { });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("system.user.handleExport"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(function () {
          return exportUser(queryParams);
        })
        .then(response => {
          this.download(response.msg);
        })
        .catch(function () { });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = this.$t("system.user.handleImport");
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, this.$t("system.user.handleFileSuccess"), {
        dangerouslyUseHTMLString: true
      });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
