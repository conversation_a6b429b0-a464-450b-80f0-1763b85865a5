<template>
  <el-dialog :close-on-click-modal="false" :title="$t('system.computer.history')" :visible.sync="historyOpen"
    width="80%" center>
    <div>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-plus" v-if="checkRolesAdd('dev')" @click="handleAdd">
            {{ $t("queryParams.add") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-check" @click="handleAuth(0)">
            {{ $t("system.computer.auth") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" @click="handleAuth(1)">
            {{ $t("system.computer.disabled") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-refresh" v-debounce-click="handleQuery">
            {{ $t("system.computer.refresh") }}
          </el-button>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="softUpdateList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="65" align="center" />
        <el-table-column type="index" width="65" align="center" :label="$t('base.carexception.customerName')">
          <template slot-scope="scope">
            {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('bike.model.DeviceKind')" prop="type" align="center">
          <template slot-scope="{ row }">
            {{ typeList[row.type] }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('acc.log.deviceModel')" align="center">
          {{ updateObj.modelName }}
        </el-table-column>
        <el-table-column :label="$t('system.app.versionNum')" prop="versionName" align="center" />
        <el-table-column :label="$t('system.app.versionSn')" prop="versionCode" align="center" />
        <el-table-column :label="$t('system.computer.desc')" prop="desc" align="center" show-overflow-tooltip>
          <span slot-scope="scope" v-NoData="scope.row.desc"></span>
        </el-table-column>
        <el-table-column :label="$t('system.computer.descEn')" prop="descEn" align="center" show-overflow-tooltip>
          <span slot-scope="scope" v-NoData="scope.row.descEn"></span>
        </el-table-column>
        <el-table-column :label="$t('system.computer.downLink')" prop="downLink" align="center">
          <template v-slot="{ row }">
            <el-image v-if="row.downLink" style="width: 30px; height: 30px; cursor: pointer"
              :src="require('@/assets/image/xiazai.png')" v-debounce-click="() => urlDownload(row.downLink)" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.computer.status')" align="center">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
              @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.computer.auditStatus')" align="center" prop="state">
          <template slot-scope="scope">
            {{ setAuditStatus(scope.row.state) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.computer.createBy')" align="center" prop="createBy">
          <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
        </el-table-column>
        <el-table-column :label="$t('system.computer.createTime')" align="center" prop="createTime" width="140">
          <template slot-scope="scope">
            {{ parseTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.computer.handle')" align="center" class-name="small-padding fixed-width"
          width="130">
          <template slot-scope="scope">
            <el-button type="text" @click="handleUpdate(scope.row, 1)">
              {{ $t("bike.computer.update") }}
            </el-button>
            <el-button type="text" v-if="auditAuth(scope.row)" @click="handleAudit(scope.row)">
              {{ $t("system.computer.audit") }}
            </el-button>
            <el-button v-if="reviewAuth(scope.row)" type="text" @click="handleAudit(scope.row)">
              {{ $t("system.computer.release") }}
            </el-button>
            <el-button type="text" class="text-red" @click="handleDel(scope.row)">
              {{ $t("bike.info.del") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
        @pagination="getList" />

      <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body center :close-on-click-modal="false">
        <el-form ref="form" :model="form" :rules="rules" label-position="top">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="$t('bike.model.DeviceKind')" prop="type">
                <el-select v-model="form.type" clearable style="width: 100%" disabled
                  :placeholder="$t('form.select') + $t('bike.model.DeviceKind')">
                  <el-option v-for="(value, key) in typeList" :key="key" :label="value" :value="+key" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="isModelName" prop="modelName">
                <selectLoadMore v-model="form.modelName" :data="modelData.data" :page="modelData.page"
                  :hasMore="modelData.more" :request="getModelList" disabled dictLabel="code" :moreParams="true"
                  @getChange="getModelInfo" :placeholder="$t('form.select') + isModelName" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('system.app.versionNum')" prop="versionName">
                <el-input v-model.trim="form.versionName" clearable
                  :placeholder="$t('form.input') + $t('system.app.versionNum')" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="$t('system.app.versionSn')" prop="versionCode">
                <el-input v-model.trim="form.versionCode" clearable oninput="value=value.replace(/[^\d]/g,'')"
                  :placeholder="$t('form.input') + $t('system.app.versionSn')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('system.computer.desc')" prop="desc">
                <el-input v-model.trim="form.desc" type="textarea" clearable :minlength="0" :maxlength="100"
                  :placeholder="$t('system.computer.input') + $t('system.computer.desc')
                    " />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('system.computer.descEn')" prop="descEn">
                <el-input v-model.trim="form.descEn" type="textarea" clearable :minlength="0" :maxlength="100"
                  :placeholder="$t('system.computer.input') + $t('system.computer.descEn')
                    " />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item :label="$t('system.computer.downLink2')" prop="downLink">
            <DrUpload v-model="form.downLink" :limit="1" :isOnePic="1" :css="{ width: '100%' }"
              class="flex-direction align-start">
              <el-button size="small" type="primary">
                {{ $t("queryParams.upload") }}
              </el-button>
            </DrUpload>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
            {{ $t("dialog.confirm") }}
          </el-button>
          <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
        </div>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  listDeviceGroup,
  authDeviceUpdate,
  updateSave,
  editDeviceUpdate,
  editDeviceDelete,
  changeDeviceState
} from "@/api/system/control";
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { commonJs } from "@/mixinFile/common";
import deviceUpdateJs from "../mixins";

export default {
  mixins: [commonJs, deviceUpdateJs],
  props: ["content", "updateObj"],
  data() {
    return {
      historyOpen: false,
      // 车型
      modelNameList: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      edit: false,
      modelOptions: [],
      brandOptions: [],
      deviceOptions: [
        {
          key: this.$t("system.computer.stopwatch"),
          value: 1
        }
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      isAdd: false,
      // 总条数
      total: 0,
      // 用户表格数据
      softUpdateList: [],
      // 码表类型
      computerMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      genderOptions: [],
      type: null,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: 1
      }
    };
  },
  watch: {
    historyOpen: {
      handler(bool) {
        if (bool) {
          this.getList();
        } else {
          this.$parent.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productModelList = [];
        this.productCodeList = [];
        this.form.productModel = "";
        this.form.code = "";
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.form.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productCodeList = [];
        this.form.code = "";
      }
    },
    /**
     * 审核状态回写
     */
    setAuditStatus(stata) {
      const maps = {
        0: this.$t("system.computer.toAudit"),
        1: this.$t("system.computer.toReview"),
        2: this.$t("system.computer.done")
      };
      return maps[stata];
    },
    /**
     * 审核
     */
    async handleAudit(row) {
      await changeDeviceState({
        id: row.id,
        state: Number(row.state) + 1 // 当前状态 + 1 到下个状态
      });
      this.getList();
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listDeviceGroup({
        modelId: this.updateObj.modelId
      }).then(response => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 0
          ? this.$t("system.computer.auth")
          : this.$t("system.computer.disabled");
      this.$confirm(
        this.$t("system.computer.handleStatusChange.text1") +
        text +
        this.$t("system.computer.handleStatusChange.text2"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: "warning"
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = {
            id: row.id,
            status: row.status
          };
          data.push(authData);
          authDeviceUpdate(data).then(() => {
            this.msgSuccess(text + this.$t("system.computer.success"));
            this.loading = false;
          });
        })
        .catch(() => {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.form.type = this.updateObj.type;
      this.form.modelId = this.updateObj.modelId;
      this.form.modelName = this.updateObj.modelName;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleAuth(status) {
      if (this.tabSelection(this.ids)) {
        if (status === 0)
          return this.warningMessage(`${this.$t("form.enableData")}`, 1);
        else return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
      }
      let text =
        status === 0
          ? this.$t("system.computer.auth")
          : this.$t("system.computer.disabled");
      this.$confirm(
        this.$t("system.computer.handleStatusChange.text1") +
        text +
        this.$t("system.computer.handleStatusChange.text2"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let authData = {
              id: this.ids[i],
              status: status
            };
            data.push(authData);
          }
          this.loading = false;
          authDeviceUpdate(data).then(() => {
            this.msgSuccess(text + this.$t("system.computer.success"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(() => {
          status = status === 0 ? 1 : 0;
        });
    },
    handleAdd() {
      this.reset();
      this.isAdd = true;
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    handleUpdate(row, type) {
      this.reset();
      this.type = type;
      this.isAdd = false;
      this.handleChange(row.modelId);
      this.form = Object.assign({}, row);
      this.form.modelId = this.updateObj.modelId;
      this.form.modelName = this.updateObj.modelName;
      this.open = true;
      this.title = this.$t("system.computer.handleUpdate");
    },
    handleChange(val) {
      for (let i = 0; i < this.modelOptions.length; i++) {
        if (this.modelOptions[i].id == val) {
          this.brandOptions = this.modelOptions[i].modelList;
        }
      }
    },
    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        editDeviceDelete([row.id]).then(() => {
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
          this.loading = false;
          this.getList();
        });
      });
    },
    /**
     * 审核权限
     */
    auditAuth(row) {
      if (row.state === 0) {
        return this.checkRoles("test");
      }
      return false;
    },
    /**
     * 复核权限
     */
    reviewAuth(row) {
      if (row.state === 1) {
        return this.checkRoles("dev_manager");
      }
      return false;
    },
    /**
     * 新增权限判断
     */
    checkRolesAdd(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      return this.$store.getters.roles.includes(role);
    },
    /**
     * 角色判断
     * admin 默认有所有权限
     */
    checkRoles(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      let res = false;
      if (Array.isArray(role)) {
        for (var i = 0; i < role.length; i++) {
          let item = role[i];
          let flag = this.$store.getters.roles.includes(item);
          if (flag) {
            res = true;
            break;
          }
        }
      } else {
        res = this.$store.getters.roles.includes(role);
      }
      return res;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.selectVal && this.form.selectVal.length) {
            this.form.brandId = this.form.selectVal[0];
            this.form.modelId = this.form.selectVal[1];
          }
          let params = Object.assign({}, this.form);
          params.computerModel = params.computerModel == "default" ? "" : params.computerModel;
          if (!this.isAdd) {
            editDeviceUpdate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            updateSave(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
