<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('bike.bike.brandKey')" prop="key">
        <el-input v-model.trim="queryParams.key" :placeholder="$t('bike.bike.brandKeyInput')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("bike.model.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("bike.model.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd" hasPerminone="['bike:model:add']">
          {{ $t("bike.model.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(1)"
          hasPerminone="['bike:model:auth']">
          {{ $t("bike.model.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(0)"
          hasPerminone="['bike:model:auth']">
          {{ $t("bike.model.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table ref="multipleTableRef" row-key="id" v-loading="loading" :data="brandList" :height="tableHeight()"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column type="index" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.computer.model')" prop="code" align="center" />
      <el-table-column :label="$t('bike.computer.modelName')" prop="name" align="center" />
      <el-table-column :label="$t('bike.model.TopImage')" prop="topImg" width="140px" align="center">
        <template v-slot="{ row }">
          <PreviewImg :imgUrl="row.topImg" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.createBy')" align="center" prop="createBy" />
      <el-table-column :label="$t('bike.model.createTime')" align="center" sortable prop="createTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('bike.computer.instructionBook')" align="center" prop="modelType">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="onDownLoadLink(row.id)">
            {{ $t("system.computer.downLink") }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.operation')" align="center" width="80"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row, false)" hasPerminone="['bike:computer:edit']">
            {{ $t("bike.model.update") }}
          </el-button>
          <!-- <el-button type="text" @click="handleUpdate(scope.row, true)">
            {{ $t("bike.model.particulars") }}
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
    <!-- 新增  -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="700px" append-to-body center>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('bike.computer.model')" prop="code">
              <el-input v-model.trim="form.code" clearable :readonly="type"
                :placeholder="$t('form.input') + $t('bike.computer.model')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('bike.computer.modelName')" prop="name">
              <el-input v-model.trim="form.name" clearable :readonly="type"
                :placeholder="$t('form.input') + $t('bike.computer.modelName')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('bike.model.TopImage')" prop="topImg" style="width: 100%">
              <el-upload-sortable v-model="form.topImg" :imgW="98" :imgH="98" :isLimit="1" :max="1" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('bike.model.elseImg')" prop="imgs" style="width: 100%">
              <el-upload-sortable v-model="form.imgs" :imgW="98" :imgH="98" />
            </el-form-item>
          </el-col>

          <el-card shadow="nerver">
            <div slot="header" class="flex justify-between align-center">
              <span class="text-blue">
                {{ $t("bike.computer.instructionBook") }}
              </span>
              <transition name="fade-right-transform">
                <el-button icon="el-icon-plus" type="primary" size="mini" v-show="isAddBook" @click="onAddIntro" />
              </transition>
            </div>
            <el-col :span="24">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="$t('bike.computer.country')">
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item :label="$t('bike.computer.instructionBookName')"></el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item :label="$t('bike.computer.instructionLink')"></el-form-item>
                </el-col>
                <el-col :span="4" v-show="!type">
                  <el-form-item :label="$t('bike.customer.operation')"></el-form-item>
                </el-col>
              </el-row>
              <el-form-item v-for="(item, index) in form.list" :key="index">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-select v-model="item.countryId" :placeholder="$t('form.input') + $t('acc.user.country')"
                      clearable style="width: 100%" size="mini" :disabled="type">
                      <el-option v-for="(item, index) in countryList" :key="index" :label="item.dictLabel"
                        :value="item.dictCode" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-input v-model="item.name" size="mini" :readonly="type"></el-input>
                  </el-col>
                  <el-col :span="!type ? 10 : 14">
                    <el-input v-model="item.link" size="mini" readonly></el-input>
                  </el-col>
                  <el-col :span="4" v-if="!type">
                    <div class="flex align-center">
                      <DrUpload v-model="item.link" :limit="1" class="avatar-uploader-intro"
                        :css="{ textAlign: 'center' }" :isDisabled="type" :showFileList="false" />

                      <el-button @click="removeIntro(item)" class="margin-left-xs text-red" type="text" :disabled="type"
                        v-show="form.list.length !== 1">
                        {{ $t("queryParams.delete") }}
                      </el-button>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.model._confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("bike.model._cancel") }}</el-button>
      </div>
    </el-dialog>

    <DownLoadlink ref="downLoadlink" />
  </div>
</template>

<script>
import { listDictCustomer } from "@/api/base/dict";
import { groupDict } from "@/api/model/gear";
import { VUE_BASE_UPLOAD } from "@/api/config";
import {
  helmetModelList,
  helmetModelAuth,
  helmetModelCreate,
  helmetModelEdit
} from "@/api/dvrModel";
import { instrCountry, instrSearch } from "@/api/system/config";
import DownLoadlink from "./downloadLink";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    DownLoadlink
  },
  data() {
    let validateImage = (rule, value, callback) => {
      if (this.form.topImg) {
        return callback();
      } else {
        return callback(new Error(this.$t("bike.model.carTopImage")));
      }
    };
    return {
      type: false,
      countryList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      brandList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      showGroup: false,
      // 日期范围
      dateRange: [],
      customerOptions: [],
      groupOptions: [],
      wheelOptions: [],
      fileList: [],
      fileListCover: [],
      allOptions: [],
      actionUrl: VUE_BASE_UPLOAD,
      unitTypeOptions: [
        {
          key: "km",
          value: 1
        },
        {
          key: "miles",
          value: 2
        }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      },
      // 表单参数
      form: {
        code: "",
        name: "",
        topImg: "",
        imgs: "",
        email: "",
        list: [
          {
            countryId: "",
            link: ""
          }
        ]
      },
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("bike.model.nameNotNull"),
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.model.codeNotNull"),
            trigger: "blur"
          }
        ],
        topImg: [
          {
            required: true,
            validator: validateImage,
            trigger: "change"
          }
        ]
      }
    };
  },
  computed: {
    isAddBook() {
      return (
        this.countryList.length !== (this.form.list && this.form.list.length)
      );
    }
  },
  created() {
    listDictCustomer().then(response => {
      this.customerOptions = response.data;
    });

    this.getDicts("base_bike_wheelsize").then(response => {
      this.wheelOptions = response.data;
    });

    groupDict().then(res => {
      this.groupOptions = res.data;
    });
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      helmetModelList(this.queryParams).then(response => {
        this.brandList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    onAddIntro() {
      this.form.list.push({
        countryId: "",
        link: ""
      });
    },
    removeIntro(item) {
      var index = this.form.list.indexOf(item);
      if (index !== -1) {
        this.form.list.splice(index, 1);
      }
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 1
          ? this.$t("bike.model.startUsing")
          : this.$t("bike.model.blockUp");
      this.$confirm(this.$t("bike.model.sure"), this.$t("bike.model.warn"), {
        confirmButtonText: this.$t("bike.model.confirm"),
        cancelButtonText: this.$t("bike.model.cancel"),
        type: this.$t("bike.model.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = {
            id: row.id,
            status: row.status
          };
          data.push(authData);

          helmetModelAuth(data).then(response => {
            this.msgSuccess(text + this.$t("bike.model.succeed"));
            this.loading = false;
          });
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
    },
    // 下载说明书
    onDownLoadLink(id) {
      this.$refs.downLoadlink.dialogTableVisible = true;
      this.$refs.downLoadlink.getList(id);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleAuth(status) {
      if (this.tabSelection(this.ids)) {
        if (status === 1)
          return this.warningMessage(`${this.$t("form.enableData")}`, 1);
        else return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
      }
      let text =
        status === 1
          ? this.$t("bike.model.startUsing")
          : this.$t("bike.model.blockUp");
      this.$confirm(this.$t("bike.model.sure"), this.$t("bike.model.warn"), {
        confirmButtonText: this.$t("bike.model.confirm"),
        cancelButtonText: this.$t("bike.model.cancel"),
        type: this.$t("bike.model.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let authData = {
              id: this.ids[i],
              status: status
            };
            data.push(authData);
          }
          this.loading = false;
          helmetModelAuth(data).then(response => {
            this.msgSuccess(text + this.$t("bike.model.succeed"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(function () {
          status = status === "0" ? "1" : "0";
        });
    },
    handleAdd() {
      this.reset();
      this.fileList = [];
      this.fileListCover = [];
      this.open = true;
      this.title = this.$t("bike.model.addRecl");
      this.getInstrCountry();
    },
    getInstrCountry() {
      instrCountry().then(res => {
        this.countryList = res.data;
      });
    },
    handleDetail(item) {
      this.open = true;
      this.title = this.$t("bike.model.updateCarModel");
    },
    changeModelConf(val) {
      if (val) {
        this.showGroup = val.indexOf("5") > -1;
      }
    },
    // 表单重置
    reset() {
      this.form = {
        list: [
          {
            countryId: "",
            link: ""
          }
        ]
      };
      this.resetForm("form");
    },
    handleUpdate(row, type) {
      this.reset();
      this.type = type;
      this.open = true;
      this.title = type
        ? this.$t("route.Info")
        : this.$t("bike.model.resetRecl");
      this.form = {
        ...row
      };
      this.fileListCover = row.topImg
        ? [
          {
            name: row.topImg,
            url: row.topImg
          }
        ]
        : [];
      if (row.imgs) {
        let imgArr = row.imgs.split(",");
        let boxes = [];
        for (let key of imgArr) {
          boxes.push({
            url: key,
            name: key
          });
        }
        this.fileList = boxes;
      } else {
        this.fileList = [];
      }
      this.getInstrCountry();
      this.onInstrSearch(row.id);
    },
    onInstrSearch(modelId) {
      instrSearch({
        modelId
      }).then(res => {
        const data = res.data;
        data.forEach(item => {
          item.countryId = +item.countryId;
        });
        this.$set(this.form, "list", data);
      });
    },
    uploadSuccess(res) {
      this.form.topImg = res.data[0].url;
      this.clearValidateItem("form", "topImg");
    },
    uploadSuccessTwo(res) {
      let imgs = res.data.map(item => item.url);
      let old = this.form.imgs;
      let catImg = old ? old.split(",").concat(imgs) : imgs;
      this.form.imgs = catImg.toString();
    },
    handleRemove(file, fileList) {
      this.form.topImg = null;
    },
    handleRemoveTwo(file, fileList) {
      let url = fileList.map(item => {
        if (item.response) {
          return item.response.data[0].url;
        } else {
          return item.url;
        }
      });
      this.form.imgs = url.toString();
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        this.$t("bike.model.astrict1") +
        files.length +
        this.$t("bike.model.astrict1") +
        files.length +
        fileList.length +
        this.$t("bike.model.astrict3")
      );
    },
    fnAgreementVerName(row, column, cellValue, index) {
      if (cellValue == 1) {
        return this.$t("bike.model.oldVersion");
      } else if (cellValue == 2) {
        return this.$t("bike.model.newVersion");
      }
    },
    spImgs(val) {
      if (val) {
        return val.split(",");
      }
      return [];
    },
    sImgs(val) {
      if (val) {
        return val.split(",")[0];
      }
      return "";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = Object.assign({}, this.form);
          if (params.id !== undefined) {
            helmetModelEdit(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.updateSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .catch(() => {
                this.isBtnLoading = false;
              });
          } else {
            helmetModelCreate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.addSucceed"));
                  this.isBtnLoading = false;
                  this.open = false;
                  this.getList();
                }
              })
              .catch(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss">
.upload-demo {
  .el-upload {
    width: 100px;
    height: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .el-upload-list__item {
    width: 100px;
    height: 100px;
  }
}

.avatar-uploader-intro {
  .el-upload {
    width: auto;
    height: auto;
    line-height: 36px !important;
  }
}
</style>
