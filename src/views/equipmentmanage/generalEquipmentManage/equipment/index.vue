<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="`${$t('acc.log.email')}`" prop="email">
        <el-input v-model.trim="queryParams.email" :placeholder="`${$t('acc.msg.msg.pleaseInput')}${$t('acc.log.email')}`
          " @keyup.enter.native="handleQuery"></el-input>
      </el-form-item>
      <el-form-item :label="$t('acc.log.equipmentType')" prop="type">
        <el-select v-model="queryParams.type" clearable @change="handleQuery">
          <el-option v-for="dict in typeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("bike.bike.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("bike.bike.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="bikeList" :height="tableHeight()">
      <el-table-column type="index" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.riding.have.email')" prop="email" align="center" />
      <el-table-column :label="$t('acc.log.nickName')" prop="nickName" align="center" />
      <el-table-column :label="$t('acc.log.equipmentSn')" prop="sn" align="center" />
      <el-table-column align="center" :label="$t('acc.log.equipmentType')" prop="type">
        <template slot-scope="{ row }">
          <el-tag v-show="row.type === 1">
            {{ $t("acc.log.smartCarLock") }}
          </el-tag>
          <el-tag v-show="row.type === 2">
            {{ $t("acc.log.electronicShif") }}
          </el-tag>
          <el-tag v-show="row.type === 3">
            {{ $t("acc.log.smartTurnSignal") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.log.deviceModel')" prop="modelName" />
      <el-table-column :label="$t('bike.bike.bindTime')" align="center" sortable prop="registerTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.bindTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.operation')" class-name="small-padding fixed-width" header-align="center"
        width="120">
        <template slot-scope="scope">
          <div class="flex flex-wrap justify-around padding-lr-xs">
            <el-button type="text" @click="handleDetail(scope.row)">
              {{ $t("bike.bike.particulars") }}
            </el-button>
            <el-button type="text" class="text-red" v-if="scope.row.bikeId" @click="handleUntie(scope.row)">
              {{ $t("bike.bike.untie") }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :close-on-click-modal="false" :title="$t('bike.bike.particulars')" :visible.sync="dialogTableVisible">
      <el-card>
        <el-form class="form-content-detail" :model="queryParams" ref="queryForm" :inline="true" label-position="left">
          <el-form-item :label="$t('bike.bike.nickName') + ':'" prop="modelKey">
            <div>{{ detailData.nickName }}</div>
          </el-form-item>
          <el-form-item :label="$t('acc.riding.have.email') + ':'" prop="email">
            <div>{{ detailData.email }}</div>
          </el-form-item>
          <el-form-item :label="$t('acc.log.equipmentSn') + ':'" prop="sn">
            <div>{{ detailData.sn }}</div>
          </el-form-item>
          <el-form-item :label="$t('acc.log.equipmentType') + ':'" prop="type">
            <el-tag v-show="detailData.type === 1">
              {{ $t("acc.log.smartCarLock") }}
            </el-tag>
            <el-tag v-show="detailData.type === 2">
              {{ $t("acc.log.electronicShif") }}
            </el-tag>
            <el-tag v-show="detailData.type === 3">
              {{ $t("acc.log.smartTurnSignal") }}
            </el-tag>
          </el-form-item>
          <el-form-item :label="$t('acc.log.deviceModel') + ':'" prop="modelKey">
            {{ detailData.modelName }}
          </el-form-item>
          <el-form-item :label="$t('bike.bike.bindTime') + ':'" prop="bindTime">
            {{ parseTime(detailData.bindTime) }}
          </el-form-item>
          <el-form-item :label="$t('bike.bike.bluetooth') + ':'" prop="modelKey">
            {{ detailData.bluetooth }}
          </el-form-item>
        </el-form>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { equipList, equipDelBind, equipInfo } from "@/api/acc/log";

export default {
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      bikeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      customerOptions: [],
      computerOptions: [],
      bindStatusOptions: [
        { dictValue: "0", dictLabel: this.$t("bike.bike.bind") },
        { dictValue: "1", dictLabel: this.$t("bike.bike.notBind") }
      ],
      typeList: [
        {
          dictLabel: this.$t("acc.log.smartCarLock"),
          dictValue: 1
        },
        {
          dictLabel: this.$t("acc.log.electronicShif"),
          dictValue: 2
        },
        {
          dictLabel: this.$t("acc.log.smartTurnSignal"),
          dictValue: 3
        }
      ],
      brandOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      },
      // 表单参数
      form: {},
      // 表单校验
      detailData: {},
      dialogTableVisible: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      equipList(this.queryParams).then(response => {
        this.bikeList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      equipInfo({ id: row.bikeId, type: row.type }).then(response => {
        this.detailData = Object.assign(row, response.data);
      });
    },
    handleUntie({ id, bikeId, type }) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        equipDelBind({ id, bikeId, type }).then(response => {
          this.msgSuccess(
            this.$t("bike.bike.untie") + this.$t("acc.user.succeed")
          );
          this.getList();
        });
      });
    }
  }
};
</script>
