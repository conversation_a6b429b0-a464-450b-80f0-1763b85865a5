<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="isSearchTitle" prop="key">
        <el-input v-model="queryParams.key" :placeholder="isSearchHolder" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("bike.model.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("bike.model.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="brandList" :height="tableHeight()">
      <el-table-column :label="$t('acc.log.nickName')" prop="userName" align="center">
        <span slot-scope="scope" v-NoData="scope.row.userName" />
      </el-table-column>
      <el-table-column :label="$t('acc.user.bikeName')" prop="bikeName" align="center">
        <span slot-scope="scope" v-NoData="scope.row.bikeName" />
      </el-table-column>
      <el-table-column :label="$t('bike.computer.model')" prop="modelCode" align="center" v-if="isType === 3">
        <span slot-scope="scope" v-NoData="scope.row.modelCode" />
      </el-table-column>
      <el-table-column :label="$t('bike.computer.modelName')" prop="modelName" align="center">
        <span slot-scope="scope" v-NoData="scope.row.modelName" />
      </el-table-column>
      <el-table-column v-if="isType !== 3" :label="$t('bike.computer.equipmentName')" prop="name" align="center">
        <span slot-scope="scope" v-NoData="scope.row.name" />
      </el-table-column>
      <!-- 运动相机 -->
      <template v-if="isType === 5">
        <el-table-column :label="$t('bike.computer.wifiName')" prop="wifiName" align="center">
          <span slot-scope="scope" v-NoData="scope.row.wifiName" />
        </el-table-column>
        <el-table-column :label="$t('bike.computer.wifiPass')" prop="wifiPass" align="center">
          <span slot-scope="scope" v-NoData="scope.row.wifiPass" />
        </el-table-column>
      </template>
      <el-table-column :label="$t('acc.log.equipmentSn')" prop="sn" align="center">
        <span slot-scope="scope" v-NoData="scope.row.sn" />
      </el-table-column>
      <el-table-column :label="$t('bike.bike.bluetooth')" prop="bluetooth" align="center">
        <span slot-scope="scope" v-NoData="scope.row.bluetooth" />
      </el-table-column>
      <el-table-column :label="$t('bike.bike.bindTime')" align="center" sortable prop="createTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.bindTime)" />
      </el-table-column>
      <el-table-column :label="$t('bike.model.operation')" align="center" width="80">
        <template slot-scope="scope">
          <el-button :disabled="!scope.row.bindTime" type="text" class="text-red" @click="handleUntie(scope.row)">
            {{ $t("bike.bike.untie") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  helmetList,
  helmetDelBind,
  radarList,
  radarDelBind,
  cameraList,
  cameraDelBind,
  wireLessList,
  wireLessDelBind
} from "@/api/dvrModel";

export default {
  data() {
    return {
      isType: null,
      apiFn: null,
      apiAuthFn: null,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 用户表格数据
      brandList: [],
      // 弹出层标题
      title: "",
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      }
    };
  },
  computed: {
    isSearchTitle() {
      return (
        this.$t("acc.log.nickName") +
        "/" +
        this.$t("bike.computer.modelName") +
        "/" +
        this.$t("acc.user.bikeName")
      );
    },
    isSearchHolder() {
      return this.$t("form.input") + this.isSearchTitle;
    }
  },
  created() {
    this.getConfig();
    this.getList();
  },
  methods: {
    getConfig() {
      switch (this.$route.name) {
        // IOT
        // case "IotManage":
        //   this.apiFn = typeList;
        //   this.apiAuthFn = typeAuth;
        //   this.isType = 2;
        //   this.title = this.$t("route.IotManage");
        //   this.isIot = true;
        //   this.isBook = false;
        //   break;
        // 头盔
        case "SmartHeadManage":
          this.apiFn = helmetList;
          this.apiAuthFn = helmetDelBind;
          this.isType = 3;
          this.title = this.$t("route.HeadManage");
          break;
        // 雷达
        case "RadarManage":
          this.apiFn = radarList;
          this.apiAuthFn = radarDelBind;
          this.isType = 4;
          this.title = this.$t("route.RadarManage");
          break;
        // 运动相机
        case "SportCamearManage":
          this.apiFn = cameraList;
          this.apiAuthFn = cameraDelBind;
          this.isType = 5;
          this.title = this.$t("route.SportCamearManage");
          break;
        // 按键管理
        case "KeyManage":
          this.apiFn = wireLessList;
          this.apiAuthFn = wireLessDelBind;
          this.isType = 6;
          this.title = this.$t("route.KeyManage");
          break;
      }
    },
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      this.apiFn(this.queryParams).then(response => {
        this.brandList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 解绑
    handleUntie({ id, bikeId }) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        this.apiAuthFn({
          id,
          bikeId
        }).then(() => {
          this.msgSuccess(
            this.$t("bike.bike.untie") + this.$t("acc.user.succeed")
          );
          this.getList();
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>
