<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent v-show="showSearch">
        <el-form-item :label="$t('bike.bike.brandKey')" prop="key">
          <el-input v-model="queryParams.key" :placeholder="$t('bike.bike.brandKeyInput')" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
            {{ $t("bike.model.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.model.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("bike.model.newAdd") }}
        </el-button>
      </el-col>
      <template v-if="isType === 2">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0)">
            {{ $t("bike.model.startUsing") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1)">
            {{ $t("bike.model.forbidden") }}
          </el-button>
        </el-col>
      </template>
      <template v-else>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(1)">
            {{ $t("bike.model.startUsing") }}
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(0)">
            {{ $t("bike.model.forbidden") }}
          </el-button>
        </el-col>
      </template>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table ref="multipleTableRef" row-key="id" v-loading="loading" :height="tableHeight()" :data="brandList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column type="index" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.computer.model')" prop="code" align="center" />
      <el-table-column :label="$t('bike.computer.modelName')" prop="name" align="center" />
      <template v-if="isIot">
        <el-table-column :label="$t('bike.model.DeviceKind')" prop="kind" align="center">
          <template slot-scope="{ row }">
            {{ kindList[row.kind] }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('bike.model.DeviceAttribute')" prop="property" align="center" />
      </template>
      <el-table-column :label="isIot ? $t('bike.model.DeviceIcon') : $t('bike.model.TopImage')" align="center">
        <template slot-scope="{ row }">
          <PreviewImg :imgUrl="isIot ? row.icon : row.topImg" />
        </template>
      </el-table-column>
      <template v-if="isIot">
        <el-table-column :label="$t('bike.model.BasicParamerter')" prop="parmas" align="center" />
        <el-table-column :label="$t('bike.model.SaveTime')" prop="warrantyPeriod" align="center" />
      </template>
      <el-table-column :label="$t('bike.model.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch v-if="isType === 2" v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row)" />
          <el-switch v-else v-model="scope.row.status" :active-value="1" :inactive-value="0"
            @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.createBy')" align="center" prop="createBy" />
      <el-table-column :label="$t('bike.model.createTime')" align="center" sortable prop="createTime">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.computer.instructionBook')" align="center" prop="modelType" v-if="isBook">
        <template slot-scope="{ row }">
          <el-button type="primary" @click="onDownLoadLink(row.id)">
            {{ $t("system.computer.downLink") }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.model.operation')" align="center" width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row, false)" hasPerminone="['bike:computer:edit']">
            {{ $t("bike.model.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 新增  -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1200px" append-to-body center>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('bike.computer.model')" prop="code">
              <el-input v-model.trim="form.code" clearable :readonly="type"
                :placeholder="$t('form.input') + $t('bike.computer.model')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('bike.computer.modelName')" prop="name">
              <el-input v-model.trim="form.name" clearable :readonly="type"
                :placeholder="$t('form.input') + $t('bike.computer.modelName')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('route.Configure')" prop="modelConfList">
              <el-select v-model="form.modelConfList" multiple collapse-tags :placeholder="$t('route.Configure')"
                style="width: 100%">
                <el-option v-for="item in confOptions" :key="item.key" :label="item.value" :value="+item.key">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="isIot">
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.DeviceKind')" prop="kind">
                <el-radio-group v-model="form.kind" class="kindStyle">
                  <el-radio v-for="(value, key) in kindList" :label="+key" :key="key" border>
                    {{ value }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.DeviceAttribute')" prop="property">
                <el-input v-model="form.property" clearable :placeholder="$t('form.input') + $t('bike.model.DeviceAttribute')
                  " />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.BasicParamerter')" prop="parmas">
                <el-input v-model="form.parmas" clearable :placeholder="$t('form.input') + $t('bike.model.BasicParamerter')
                  " />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.model.SaveTime')" prop="warrantyPeriod">
                <el-input-number v-model="form.warrantyPeriod" :min="1" clearable style="width: 100%;"
                  :placeholder="$t('form.input') + $t('bike.model.SaveTime')" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('bike.model.DeviceIcon')" prop="icon" style="width: 100%">
                <el-upload-sortable v-model="form.icon" :imgW="98" :imgH="98" :isLimit="1" :max="1" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="!isIot">
            <el-col :span="24">
              <el-form-item :label="$t('bike.model.TopImage')" prop="topImg" style="width: 100%">
                <el-upload-sortable v-model="form.topImg" :imgW="98" :imgH="98" :isLimit="1" :max="1" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('bike.model.elseImg')" prop="imgs" style="width: 100%">
                <el-upload-sortable v-model="form.imgs" :imgW="98" :imgH="98" />
              </el-form-item>
            </el-col>
          </template>
        </el-row>

        <!-- 说明书配置卡片 - 独立于网格系统 -->
        <el-card shadow="never" v-if="isBook" style="margin-top: 20px;">
          <div slot="header" class="flex justify-between align-center">
            <span class="text-blue">
              {{ $t("bike.computer.instructionBook") }}
            </span>
            <transition name="fade-right-transform">
              <el-button icon="el-icon-plus" type="primary" size="mini" v-show="isAddBook" @click="onAddIntro" />
            </transition>
          </div>

          <!-- 表头 -->
          <el-row :gutter="10">
            <el-col :span="3">
              <div class="table-header">{{ $t('bike.computer.country') }}</div>
            </el-col>
            <el-col :span="7">
              <div class="table-header">{{ $t('bike.computer.instructionBookName') }}</div>
            </el-col>
            <el-col :span="8">
              <div class="table-header">{{ $t('bike.computer.instructionLink') }}</div>
            </el-col>
            <el-col :span="6">
              <div class="table-header">{{ $t('bike.customer.operation') }}</div>
            </el-col>
          </el-row>

          <!-- 动态行 -->
          <div v-for="(item, index) in form.list" :key="index">
            <el-row :gutter="10" type="flex" align="middle" style="margin-top: 15px;">
              <el-col :span="3">
                <el-form-item :prop="`list[${index}].countryId`" :rules="rules.countryId">
                  <el-select v-model="item.countryId" :placeholder="$t('form.select') + $t('acc.user.country')"
                    clearable style="width: 100%" size="small">
                    <el-option v-for="(cItem, cIndex) in countryList" :key="cIndex" :label="cItem.dictLabel"
                      :value="cItem.dictCode" :disabled="cItem.disabled" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="7">
                <el-form-item :prop="`list[${index}].name`" :rules="rules.name">
                  <el-input v-model="item.name" size="small" clearable maxlength="15" show-word-limit
                    :placeholder="$t('form.input') + $t('bike.computer.instructionBookName')" />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item :prop="`list[${index}].link`" :rules="rules.link">
                  <DrUpload v-model="item.link" :limit="1"
                    class="avatar-uploader-intro narrow-container flex justify-center align-center flex-direction"
                    :css="{ textAlign: 'center' }" :isDisabled="type" :showFileList="false" />
                  <!-- 
                  <el-input v-model="item.link" size="small" readonly
                    :placeholder="$t('form.upload') + $t('bike.computer.instructionBook')" /> -->
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <div class="operation-column">

                  <el-button @click="removeIntro(item)" class="delete-btn" type="text" :disabled="type"
                    v-show="form.list.length !== 1" size="small">
                    {{ $t("queryParams.delete") }}
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <el-divider v-if="index < form.list.length - 1"></el-divider>
          </div>
        </el-card>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.model.confirm") }}
        </el-button>
        <el-button @click="open = false">
          {{ $t("bike.model.cancel") }}
        </el-button>
      </div>
    </el-dialog>

    <DownLoadlink ref="downLoadlink" />
  </div>
</template>

<script>
import {
  helmetModelList,
  helmetModelAuth,
  helmetModelCreate,
  helmetModelEdit,
  radarList,
  radarAuth,
  radarSave,
  radarUpdate,
  cameraList,
  cameraAuth,
  cameraSave,
  cameraUpdate,
  keyList,
  keyAuth,
  keySave,
  keyUpdate,
  typeList,
  typeAuth,
  typeSave,
  typeUpdate
} from "@/api/regulation";
import { instrCountry, instrSearch } from "@/api/system/config";
import { confDict } from "@/api/bike/conf";
import DownLoadlink from "./downloadLink";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    DownLoadlink
  },
  data() {
    return {
      type: false,
      apiFn: null,
      apiSaveFn: null,
      apiUpdateFn: null,
      apiAuthFn: null,
      isType: null,
      isIot: false,
      isBook: true,
      showSearch: true,
      countryList: [],
      // 配置项
      confOptions: [],
      // 用户表格数据
      brandList: [],
      // 弹出层标题
      title: "",
      mainTitle: "",
      // 是否显示弹出层
      open: false,
      kindList: {
        1: this.$t("bike.model.Meter"),
        2: this.$t("bike.model.GPS")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("bike.model.nameNotNull"),
            trigger: "blur"
          }
        ],
        code: [
          {
            required: true,
            message: this.$t("bike.model.codeNotNull"),
            trigger: "blur"
          }
        ],
        kind: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.model.DeviceKind"),
            trigger: "change"
          }
        ],
        icon: [
          {
            required: true,
            message: this.$t("form.upload") + this.$t("bike.model.DeviceIcon"),
            trigger: "change"
          }
        ],
        topImg: [
          {
            required: true,
            message: this.$t("form.upload") + this.$t("bike.model.TopImage"),
            trigger: "change"
          }
        ],
        countryId: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("acc.user.country"),
            trigger: "change"
          }
        ],
        name: [
          {
            required: true,
            message:
              this.$t("form.select") +
              this.$t("bike.computer.instructionBookName"),
            trigger: "blur"
          }
        ],
        link: [
          {
            required: true,
            message:
              this.$t("form.upload") + this.$t("bike.computer.instructionBook"),
            trigger: ["change", "blur"]
          }
        ]
      }
    };
  },
  computed: {
    isAddBook() {
      return (
        this.countryList.length !== (this.form.list && this.form.list.length)
      );
    }
  },
  watch: {
    "form.icon"(val) {
      if (val) {
        this.clearValidateItem("form", "icon");
      }
    },
    "form.topImg"(val) {
      if (val) {
        this.clearValidateItem("form", "topImg");
      }
    },
    open(bool) {
      if (!bool) {
        this.title = "";
      }
    }
  },
  created() {
    this.getConfig();
    this.getList();
    // 1车辆型号 2iot型号 3头盔型号 4雷达型号 5运动相机型号 6.按键型号 7电子锁型号
    confDict({ type: this.isType }).then(res => {
      this.confOptions = res.data;
    });
  },
  methods: {
    getConfig() {
      switch (this.$route.name) {
        // IOT
        case "IotManage":
          this.apiFn = typeList;
          this.apiSaveFn = typeSave;
          this.apiUpdateFn = typeUpdate;
          this.apiAuthFn = typeAuth;
          this.isType = 2;
          this.mainTitle = this.$t("route.IotManage");
          this.isIot = true;
          this.isBook = false;
          break;
        // 头盔
        case "HeadManage":
          this.apiFn = helmetModelList;
          this.apiSaveFn = helmetModelCreate;
          this.apiUpdateFn = helmetModelEdit;
          this.apiAuthFn = helmetModelAuth;
          this.isType = 3;
          this.mainTitle = this.$t("route.HeadManage");
          break;
        // 雷达
        case "RadarManage":
          this.apiFn = radarList;
          this.apiSaveFn = radarSave;
          this.apiUpdateFn = radarUpdate;
          this.apiAuthFn = radarAuth;
          this.isType = 4;
          this.mainTitle = this.$t("route.RadarManage");
          break;
        // 运动相机
        case "SportCamearManage":
          this.apiFn = cameraList;
          this.apiSaveFn = cameraSave;
          this.apiUpdateFn = cameraUpdate;
          this.apiAuthFn = cameraAuth;
          this.isType = 5;
          this.mainTitle = this.$t("route.SportCamearManage");
          break;
        // 按键管理
        case "KeyManage":
          this.apiFn = keyList;
          this.apiSaveFn = keySave;
          this.apiUpdateFn = keyUpdate;
          this.apiAuthFn = keyAuth;
          this.isType = 6;
          this.mainTitle = this.$t("route.KeyManage");
          break;
      }
    },
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      this.apiFn(this.queryParams).then(response => {
        this.brandList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    onAddIntro() {
      this.form.list.push({
        countryId: "",
        link: ""
      });
    },
    removeIntro(item) {
      var index = this.form.list.indexOf(item);
      if (index !== -1) {
        this.form.list.splice(index, 1);
      }
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = null;
      if (this.isType === 2) {
        text =
          row.status === 0
            ? this.$t("bike.model.startUsing")
            : this.$t("bike.model.blockUp");
      } else {
        text =
          row.status === 1
            ? this.$t("bike.model.startUsing")
            : this.$t("bike.model.blockUp");
      }
      this.$confirm(this.$t("bike.model.sure"), this.$t("bike.model.warn"), {
        confirmButtonText: this.$t("bike.model.confirm"),
        cancelButtonText: this.$t("bike.model.cancel"),
        type: this.$t("bike.model.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = {
            id: row.id,
            status: row.status
          };
          data.push(authData);

          this.apiAuthFn(data).then(() => {
            this.msgSuccess(text + this.$t("bike.model.succeed"));
            this.loading = false;
          });
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 下载说明书
    onDownLoadLink(id) {
      this.$refs.downLoadlink.dialogTableVisible = true;
      this.$refs.downLoadlink.modelId = id;
      this.$refs.downLoadlink.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        list: [
          {
            countryId: "",
            link: ""
          }
        ]
      };
      this.resetForm("form");
    },
    handleAuth(status) {
      // 由于status不统一导致

      if (this.tabSelection(this.ids)) {
        if (this.isType === 2) {
          if (status === 0) {
            return this.warningMessage(`${this.$t("form.enableData")}`, 1);
          } else {
            return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
          }
        } else {
          if (status === 1) {
            return this.warningMessage(`${this.$t("form.enableData")}`, 1);
          } else {
            return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
          }
        }
      }

      let text = null;
      if (this.isType === 2) {
        text =
          status === 0
            ? this.$t("bike.model.startUsing")
            : this.$t("bike.model.blockUp");
      } else {
        text =
          status === 1
            ? this.$t("bike.model.startUsing")
            : this.$t("bike.model.blockUp");
      }

      this.$confirm(this.$t("bike.model.sure"), this.$t("bike.model.warn"), {
        confirmButtonText: this.$t("bike.model.confirm"),
        cancelButtonText: this.$t("bike.model.cancel"),
        type: this.$t("bike.model.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let authData = {
              id: this.ids[i],
              status: status
            };
            data.push(authData);
          }
          this.apiAuthFn(data).then(() => {
            this.$refs.multipleTableRef.clearSelection();
            this.msgSuccess(text + this.$t("bike.model.succeed"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(function () {
          status = status === "0" ? "1" : "0";
        });
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("queryParams.add") + this.mainTitle;
      this.getInstrCountry();
    },
    // 国家
    getInstrCountry() {
      instrCountry().then(res => {
        this.countryList = res.data;
      });
    },
    handleDetail() {
      this.open = true;
      this.title = this.$t("bike.model.updateCarModel");
    },
    handleUpdate(row, type) {
      this.reset();
      this.type = type;
      this.open = true;
      this.title = this.$t("queryParams.update") + this.mainTitle;
      this.form = Object.assign({}, row);
      this.getInstrCountry();
      this.onInstrSearch(row.id);
    },
    onInstrSearch(modelId) {
      instrSearch({
        modelId
      }).then(res => {
        const data = res.data;
        data.forEach(item => {
          item.countryId = +item.countryId;
        });
        this.$set(this.form, "list", data);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          let params = Object.assign({}, this.form);
          if (params.id !== undefined) {
            this.apiUpdateFn(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            this.apiSaveFn(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.model.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
<style lang="scss">
.kindStyle {
  width: 100%;
  display: inline-flex;
  justify-content: space-between;
}

// 表格头部样式
.table-header {
  font-weight: 600;
  color: #606266;
  text-align: center;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
  margin-bottom: 10px;
  border-radius: 4px;
}

// 优化对话框内的表单布局
.el-dialog__body {
  padding: 20px 25px;

  .el-form {
    .el-row {
      margin-bottom: 0;

      .el-col {
        margin-bottom: 18px;
      }
    }

    .el-form-item {
      margin-bottom: 0;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }

    // IOT设备类型单选框样式优化
    .el-radio-group.kindStyle {
      .el-radio {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }

        &.is-bordered {
          padding: 8px 15px;
          border-radius: 6px;
          transition: all 0.3s;

          &:hover {
            border-color: #409eff;
          }

          &.is-checked {
            border-color: #409eff;
            background-color: #ecf5ff;
          }
        }
      }
    }
  }

  // 卡片内容样式
  .el-card {
    border: 1px solid #e6ebf5;

    .el-card__header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e6ebf5;

      .text-blue {
        color: #409eff;
        font-weight: 600;
      }
    }

    .el-card__body {
      padding: 20px;
    }
  }

  // 上传组件样式优化
  .avatar-uploader-intro {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  // 删除按钮样式
  .text-red {
    color: #f56c6c;

    &:hover {
      color: #f78989;
    }
  }

  // 操作列样式优化
  .operation-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .avatar-uploader-intro {
      width: 100%;

      .el-upload {
        width: 100%;
        min-height: 32px;
      }
    }

    .delete-btn {
      padding: 4px 8px;
      font-size: 12px;
      min-height: auto;
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .el-dialog__body {
    padding: 15px;

    .el-row {
      .el-col {
        &[class*="span-8"] {
          flex: 0 0 100%;
          max-width: 100%;
        }

        &[class*="span-4"] {
          flex: 0 0 50%;
          max-width: 50%;
        }

        &[class*="span-9"] {
          flex: 0 0 100%;
          max-width: 100%;
        }

        &[class*="span-3"] {
          flex: 0 0 50%;
          max-width: 50%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .el-dialog__body {
    .el-row {
      .el-col {
        &[class*="span-"] {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }

    .kindStyle {
      flex-direction: column;
      gap: 10px;

      .el-radio {
        margin-bottom: 10px;
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
