<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('mointerObj.host')" prop="ipaddr">
        <el-input v-model="queryParams.ipaddr" :placeholder="$t('form.input') + $t('mointerObj.host')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('navigatorManage.tableTxt.userName')" prop="userName">
        <el-input v-model="queryParams.userName" :placeholder="$t('form.input') + $t('navigatorManage.tableTxt.userName')
          " clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
          {{ $t("queryParams.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          {{ $t("queryParams.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="list.slice((p - 1) * l, p * l)" style="width: 100%">
      <el-table-column :label="$t('acc.medal.serialNumber')" width="65" type="index" align="center">
        <template slot-scope="scope">
          <span>{{ (p - 1) * l + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('mointerObj.SessionNumber')" align="center" prop="tokenId"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('mointerObj.loginName')" align="center" prop="userName"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('system.dept.deptName')" align="center" prop="deptName" />
      <el-table-column :label="$t('mointerObj.hostIP')" align="center" prop="ipaddr" :show-overflow-tooltip="true" />
      <el-table-column :label="$t('mointerObj.loginPlace')" align="center" prop="loginLocation"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('mointerObj.browser')" align="center" prop="browser" />
      <el-table-column :label="$t('mointerObj.OperatingSystem')" align="center" prop="os" />
      <el-table-column :label="$t('mointerObj.loginTime')" align="center" prop="loginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.loginTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.opt')" align="center"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleForceLogout(scope.row)"
            hasPerminone="['monitor:online:forceLogout']">
            {{ $t("mointerObj.StrongRetreat") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="p" :limit.sync="l" />
  </div>
</template>

<script>
import { list, forceLogout } from "@/api/monitor/online";

export default {
  name: "Online",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      p: 1,
      l: 20,
      // 查询参数
      queryParams: {
        ipaddr: undefined,
        userName: undefined,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询登录日志列表 */
    getList() {
      this.loading = true;
      list(this.queryParams).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 强退按钮操作 */
    handleForceLogout(row) {
      this.$confirm(
        '是否确认强退名称为"' + row.userName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return forceLogout(row.tokenId);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("强退成功");
        })
        .catch(function () { });
    },
  },
};
</script>
