<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('app.swiper.mainTitle')" prop="mainTitle">
        <el-input v-model.trim="queryParams.mainTitle" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('app.swiper.subHead')" prop="subHead">
        <el-input v-model.trim="queryParams.subHead" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('form.select')" clearable>
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("base.store.newAdd") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('app.swiper.mainTitle')" prop="mainTitle" align="center">
        <span slot-scope="{ row }" v-NoData="row.mainTitle"></span>
      </el-table-column>
      <el-table-column :label="$t('app.swiper.subHead')" prop="subHead" align="center">
        <span slot-scope="{ row }" v-NoData="row.subHead"></span>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.imgs')" prop="imgs" align="center">
        <template slot-scope="scope">
          <preview-img :imgUrl="scope.row.imgs" width="45px" height="45px" />
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('base.medal.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"
          />
        </template>
      </el-table-column> -->
      <el-table-column label="状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column :label="$t('base.medal.createBy')" prop="createBy" align="center">
        <span slot-scope="{ row }" v-NoData="row.createBy"></span>
      </el-table-column>
      <el-table-column :label="$t('base.medal.createTime')" prop="createTime" align="center">
        <span slot-scope="{ row }" v-NoData="parseTime(row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.opt')" align="center"
        class-name="small-padding fixed-width">
        <template slot-scope="{ row }">
          <el-button class="text-red" type="text" @click="handleUpdate(row)">
            {{ $t("base.medal.update") }}
          </el-button>

          <el-divider direction="vertical"></el-divider>

          <el-popconfirm title="确定要删除吗？" @confirm="handleDelete(row)">
            <el-button slot="reference">
              {{ $t("queryParams.delete") }}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 新增、编辑 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body center :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row>
          <el-col>
            <el-form-item :label="$t('app.swiper.mainTitle')" prop="mainTitle">
              <el-input v-model.trim="form.mainTitle" clearable :placeholder="$t('form.input')" />
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item :label="$t('app.swiper.subHead')" prop="subHead">
              <el-input v-model.trim="form.subHead" clearable :placeholder="$t('form.input')" />
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictValue">
                  {{ dict.dictLabel }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col>
            <el-form-item :label="$t('discover.posted.imgs')" prop="imgs">
              <el-upload-sortable v-model="form.imgs" :imgW="98" :imgH="98" :max="9" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button @click="cancel">{{ $t("base.store.cancel") }}</el-button>
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.store.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { commonJs } from "@/mixinFile/common";
export default {
  name: "AppSwiper",
  mixins: [commonJs],
  data() {
    return {
      loading: false,
      title: undefined,
      // 状态数据字典
      statusOptions: [],
      queryParams: {
        p: 1,
        l: 20,
        mainTitle: undefined,
        subHead: undefined,
        status: undefined,
      },
      form: {
        mainTitle: undefined,
        subHead: undefined,
        status: 0,
        imgs: undefined,
      },
      rules: {
        mainTitle: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("app.swiper.mainTitle"),
            trigger: "blur",
          },
        ],
        subHead: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("app.swiper.subHead"),
            trigger: "blur",
          },
        ],
        imgs: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("discover.posted.imgs"),
            trigger: "change",
          },
        ],
      },
      dataList: [],
    };
  },
  watch: {
    "form.imgs"(url) {
      if (url) {
        this.clearValidateItem("form", "imgs");
      }
    },
  },
  created() {
    // this.getList();

    // 状态
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
  },
  methods: {
    getList() {
      this.loading = true;
      listFeedback(this.queryParams).then((res) => {
        const { list, total } = res.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery({ queryForm = "queryForm" } = {}) {
      this.queryParams = {};
      this.resetForm(queryForm);
      this.handleQuery();
    },
    // 数据状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    reset() {
      this.form = {
        mainTitle: undefined,
        subHead: undefined,
        status: "0",
        imgs: undefined,
      };
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("app.swiper.addAppSwiper");
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.form = Object.assign({}, row);
      this.title = this.$t("app.swiper.editAppSwiper");
    },
    handleDelete(row) {
      // delMenu(row.id);
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.isBtnLoading = true;
        }
      });
    },
  },
};
</script>

<style></style>