<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- 设备名称 -->
      <el-form-item :label="$t('navigatorManage.tableTxt.IOTName')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('form.input') + $t('navigatorManage.tableTxt.IOTName')
          " clearable />
      </el-form-item>
      <!-- 蓝牙地址 -->
      <el-form-item :label="$t('bike.bike.bluetooth')" prop="bluetooth">
        <el-input v-model.trim="queryParams.bluetooth" :placeholder="$t('form.input') + $t('bike.bike.bluetooth')"
          clearable maxlength="17" @input="filterBluetoothValue" @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList" :height="tableHeight()">
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <!-- 设备名称 -->
      <el-table-column :label="$t('navigatorManage.tableTxt.IOTName')" prop="deviceName" align="center" />
      <!-- 蓝牙地址  -->
      <el-table-column :label="$t('bike.bike.bluetooth')" prop="bluetooth" align="center">
        <span slot-scope="scope" v-NoData="scope.row.bluetooth"></span>
      </el-table-column>
      <!-- topic头 -->
      <el-table-column :label="$t('iot.topicHead')" show-overflow-tooltip prop="topic" align="center" />
      <!-- 上报类型 -->
      <el-table-column :label="$t('iot.reportType')" prop="type" align="center">
        <template slot-scope="{ row }">
          {{ typeOptions[row.type] }}
        </template>
      </el-table-column>
      <!-- 上报内容 -->
      <el-table-column :label="$t('system.notice.noticeContent')" show-overflow-tooltip prop="value" align="center" />
      <!-- 上报时间 -->
      <el-table-column :label="$t('base.carexception.queryTime')" prop="time" sortable align="center">
        <template slot-scope="{ row }">
          {{ parseTime(row.time) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { iotDeviceLogList } from "@/api/iot/iot";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 遮罩层
      loading: true,
      typeOptions: {
        0: this.$t("iot.sendOnDevice"),
        1: this.$t("iot.serviceDelivery")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        name: "",
        bluetooth: ""
      },
      userList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      iotDeviceLogList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.userList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    filterBluetoothValue(val) {
      val = val.replace(/[^0-9a-z]/gi, "");
      if (this.$IS_Empty(val)) {
        this.queryParams.bluetooth = "";
      } else {
        const reg = new RegExp("\\w{1," + 2 + "}", "g");
        let ma = val.match(reg);
        ma = ma.join(":");
        this.queryParams.bluetooth = ma.toUpperCase();
      }
    }
  }
};
</script>
