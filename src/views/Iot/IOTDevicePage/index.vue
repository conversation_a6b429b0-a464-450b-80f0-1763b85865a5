<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <!-- 设备名称 -->
      <el-form-item :label="$t('navigatorManage.tableTxt.IOTName')" prop="name">
        <el-input v-model="queryParams.name" :placeholder="$t('form.input') + $t('navigatorManage.tableTxt.IOTName')
          " clearable />
      </el-form-item>

      <!-- 蓝牙地址 -->
      <el-form-item :label="$t('bike.bike.bluetooth')" prop="mac">
        <el-input v-model.trim="queryParams.mac" :placeholder="$t('form.input') + $t('bike.bike.bluetooth')" clearable
          :maxlength="17" @input="filterBluetoothValue" @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList" :height="tableHeight()">
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template v-slot="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <!-- IOT名称 -->
      <el-table-column :label="$t('navigatorManage.tableTxt.IOTName')" prop="iotName" align="center" />
      <!-- 卡片归属 -->
      <el-table-column :label="$t('iot.markAffiliation')" prop="mark" align="center">
        <span slot-scope="scope" v-NoData="markOptions[scope.row.mark]"></span>
      </el-table-column>
      <!-- 蓝牙地址  -->
      <el-table-column :label="$t('bike.bike.bluetooth')" prop="mac" align="center">
        <span slot-scope="scope" v-NoData="scope.row.mac"></span>
      </el-table-column>
      <!-- IOT产品键 -->
      <el-table-column :label="$t('iot.iotProductKey')" prop="iotProductKey" align="center">
        <span slot-scope="scope" v-NoData="scope.row.iotProductKey"></span>
      </el-table-column>
      <!-- IOT密文 -->
      <el-table-column :label="$t('iot.iotSecret')" prop="iotSecret" align="center">
        <span slot-scope="scope" v-NoData="scope.row.iotSecret"></span>
      </el-table-column>
      <!-- SN -->
      <el-table-column label="SN" prop="sn" align="center" />
      <!-- 设备状态 -->
      <el-table-column :label="$t('iot.deviceStatus')" prop="status" align="center">
        <template v-slot="{ row }">
          {{ typeOptions[row.status] }}
        </template>
      </el-table-column>
      <!-- 是否开启日志记录 -->
      <el-table-column :label="$t('iot.isLog')" prop="isLog" align="center">
        <template v-slot="{ row }">
          {{ logOptions[row.isLog] }}
        </template>
      </el-table-column>
      <!-- IP -->
      <el-table-column label="IP" prop="ip" align="center" />
      <!-- IP区域 -->
      <el-table-column :label="$t('iot.ipRegion')" prop="ipRegion" align="center" />
      <!-- 证书文件 -->
      <el-table-column :label="$t('iot.certificateFile')" show-overflow-tooltip prop="certificateFile" align="center" />
      <!-- 私钥文件 -->
      <el-table-column :label="$t('iot.privateFile')" show-overflow-tooltip prop="privateFile" align="center" />
      <!-- 根证书文件 -->
      <el-table-column :label="$t('iot.rootFile')" show-overflow-tooltip prop="rootFile" align="center">
        <span slot-scope="scope" v-NoData="scope.row.rootFile"></span>
      </el-table-column>
      <!-- 创建时间 -->
      <el-table-column :label="$t('base.help.createTime')" prop="createTime" sortable align="center">
        <template v-slot="{ row }">
          {{ parseTime(row.createTime) }}
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { iotDeviceList } from "@/api/iot/iot";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 遮罩层
      loading: true,
      markOptions: {
        0: this.$t("iot.homonychidae"),
        1: this.$t("iot.notHomonychidae")
      },
      typeOptions: {
        0: this.$t("iot.offLine"),
        1: this.$t("iot.onLine")
      },
      logOptions: {
        0: this.$t("iot.notOpen"),
        1: this.$t("iot.open")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        name: "",
        mac: ""
      },
      userList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      iotDeviceList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.userList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    filterBluetoothValue(val) {
      val = val.replace(/[^0-9a-z]/gi, "");
      if (this.$IS_Empty(val)) {
        this.queryParams.mac = "";
      } else {
        const reg = new RegExp("\\w{1," + 2 + "}", "g");
        let ma = val.match(reg);
        ma = ma.join(":");
        this.queryParams.mac = ma.toUpperCase();
      }
    }
  }
};
</script>
