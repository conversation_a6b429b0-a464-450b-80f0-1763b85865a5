<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('acc.log.nickName')" prop="user">
        <el-input v-model.trim="queryParams.user" :placeholder="$t('form.input') + $t('acc.log.nickName')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('shopManage.searchTxt.status')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t('form.select') + $t('shopManage.searchTxt.status')"
          clearable @change="handleQuery">
          <el-option v-for="(value, key) in typeOptions" :key="key" :label="value" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList">
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.log.nickName')" prop="nickName" align="center">
        <template slot-scope="scope">
          <el-link @click="jump(scope.row, 1)">
            {{ scope.row.nickName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.startTime')" prop="createTime" align="center">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.residueDate')" prop="lastReplyTime" align="center">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.lastReplyTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('acc.user.RenewalRecord')" prop="desc" align="center">
        <span slot-scope="scope" v-NoData="scope.row.desc" />
      </el-table-column>
      <el-table-column :label="$t('shopManage.searchTxt.status')" prop="type" align="center">
        <template slot-scope="{ row }">
          {{ typeOptions[row.type] }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.opt')" align="center" class-name="small-padding fixed-width"
        width="100">
        <template slot-scope="{ row }">
          <el-button class="text-red" type="text" @click="handleUntie(row, 3)" hasPerminone="['acc:user:query']">
            {{ $t("navigatorManage.optionTxt.freeze") }}
          </el-button>
          <el-button type="text" @click="handleUntie(row, 1)" hasPerminone="['acc:user:query']">
            {{ $t("navigatorManage.optionTxt.recover") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { listFeedback } from "@/api/base/feedback";
import { svsTighAuth } from "@/api/shopManage";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 遮罩层
      loading: true,
      typeOptions: {
        1: this.$t("navigatorManage.stateList.inEffect"),
        2: this.$t("acc.user.arrears"),
        3: this.$t("navigatorManage.stateList.frozen")
      },
      // 查询参数
      queryParams: {
        p: 1,
        l: 20
      },
      userList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listFeedback(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.userList = list;
        this.total = total;
        this.loading = false;
      });
    },
    handleUntie({ id, residueDate }, status) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        svsTighAuth([{ id, status, residueDate }]).then(() => {
          const txt =
            status === 3
              ? this.$t("navigatorManage.optionTxt.freeze")
              : this.$t("navigatorManage.optionTxt.recover");
          this.msgSuccess(txt + this.$t("acc.user.succeed"));
          this.getList();
        });
      });
    },
    jump(row, type) {
      if (type === 1) {
        this.$router.push({
          path: "/bike/userCenter/user",
          query: {
            nickName: row.userId
          }
        });
      } else {
        this.$router.push({
          path: "/Basic/modelCenter/model",
          query: {
            bikeName: row.bikeName
          }
        });
      }
    }
  }
};
</script>
