<template>
  <div class="home-style">
    <el-tabs type="border-card" v-model="queryParams.sort" @tab-click="changeTabs">
      <el-tab-pane :label="item.name" v-for="(item, index) in tabsList" :key="index" :name="item.value">
        <my-item :list="list" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import PanThumb from "@/components/PanThumb";
import Mallki from "@/components/TextHoverEffect/Mallki";
import myItem from "./item";
import { rankCount } from "@/api/sys";
export default {
  components: { myItem },
  filters: {
    indexFilter(index) {
      const statusMap = {
        0: "danger",
        1: "warning",
        2: "",
        3: "info",
      };

      return statusMap[index] == undefined ? "info" : statusMap[status];
    },
  },
  data() {
    return {
      active: 0,
      tabsList: [
        {
          name: this.$t("home.tatal"),
          vaule: 0,
        },
        {
          name: this.$t("home.day"),
          vaule: 1,
        },
        {
          name: this.$t("home.week"),
          vaule: 2,
        },
        {
          name: this.$t("home.month"),
          vaule: 3,
        },
      ],
      statisticsData: {
        article_count: 1024,
        pageviews_count: 1024,
      },
      list: [],
      avatar: "",
      queryParams: {
        p: 1,
        l: 20,
        sort: 0,
      },
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      rankCount(this.queryParams).then((response) => {
        let { data } = response;

        if (data.list.length) {
          let full = data.list[0].mileage;
          this.avatar = data.list[0].headName;
          for (let key of data.list) {
            key.percentage = (key.mileage / full) * 100;
          }
        }
        this.list = response.data.list;
      });
    },
    changeTabs(val) {
      this.getList();
    },

    // initChart(data) {
    //   this.list = data;
    // },
  },
};
</script>

<style lang="scss" scope>
.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background: #fff;
}

.box-card-component {
  height: 973px;

  .el-card__header {
    padding: 0px !important;
  }
}

.el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: transparent;
}

.home-style {
  .el-tabs--border-card>.el-tabs__content {
    padding: 10px;
  }

  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
<style lang="scss">
.el-table .Danger-row {
  background: #f56c6c;
}

.el-table .Warning-row {
  background: #e6a23c;
}

.el-table .Info-row {
  background: #909399;
}

.row-item {
  display: flex;
  align-items: center;
}

.box-card-component {
  .ranking-list-item {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;

    >div {
      display: flex;
      align-items: center;

      img {
        margin-right: 8px;
      }
    }
  }

  .box-card-header {
    position: relative;
    height: 220px;

    img {
      width: 100%;
      height: 100%;
      transition: all 0.2s linear;

      &:hover {
        transform: scale(1.1, 1.1);
        filter: contrast(130%);
      }
    }
  }

  .mallki-text {
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 20px;
    font-weight: bold;
  }

  .panThumb {
    z-index: 100;
    height: 70px !important;
    width: 70px !important;
    position: absolute !important;
    top: -45px;
    left: 0px;
    border: 5px solid #ffffff;
    background-color: #fff;
    margin: auto;
    box-shadow: none !important;

    ::v-deep .pan-info {
      box-shadow: none !important;
    }
  }

  .progress-item {
    margin-bottom: 10px;
    font-size: 14px;
  }

  @media only screen and (max-width: 1510px) {
    .mallki-text {
      display: none;
    }
  }
}
</style>
