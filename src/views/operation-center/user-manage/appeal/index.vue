<template>
  <div class="app-container">
    <div class="page-header">
      <h3>{{ t('title') }}</h3>
      <p>{{ t('description') }}</p>
    </div>

    <!-- 搜索表单 -->
    <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
      <el-form-item :label="t('search.bluetooth')" prop="bluetooth">
        <el-input v-model="searchForm.bluetooth" :placeholder="t('search.bluetoothPlaceholder')" clearable
          style="width: 200px" />
      </el-form-item>
      <el-form-item :label="t('search.nickName')" prop="nickName">
        <el-input v-model="searchForm.nickName" :placeholder="t('search.nickNamePlaceholder')" clearable
          style="width: 150px" />
      </el-form-item>
      <el-form-item :label="t('search.email')" prop="email">
        <el-input v-model="searchForm.email" :placeholder="t('search.emailPlaceholder')" clearable
          style="width: 200px" />
      </el-form-item>
      <el-form-item :label="t('search.phone')" prop="phone">
        <el-input v-model="searchForm.phone" :placeholder="t('search.phonePlaceholder')" clearable
          style="width: 150px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          {{ t('common.search') }}
        </el-button>
        <el-button @click="resetSearch">
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%">
      <el-table-column prop="bluetooth" :label="t('table.bluetooth')" width="180" show-overflow-tooltip />
      <el-table-column prop="nickName" :label="t('table.nickName')" width="120" />
      <el-table-column prop="originalName" :label="t('table.originalName')" width="120" />
      <el-table-column prop="email" :label="t('table.email')" width="180" show-overflow-tooltip />
      <el-table-column prop="phone" :label="t('table.phone')" width="120" />
      <el-table-column prop="buyImg" :label="t('table.buyImg')" width="100" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.buyImg" size="mini" type="text" @click="handleViewImage(scope.row.buyImg)">
            {{ t('actions.viewImage') }}
          </el-button>
          <span v-else class="text-muted">{{ t('messages.noImage') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('table.createTime')" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('table.status')" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'warning' : 'success'">
            {{ scope.row.status === 0 ? t('status.pending') : t('status.processed') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('table.actions')" width="120" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 0" size="mini" type="text" style="color: #F56C6C"
            @click="handleUnbind(scope.row)">
            {{ t('actions.unbind') }}
          </el-button>
          <el-button v-else size="mini" type="text" @click="handleViewDetails(scope.row)">
            {{ t('actions.viewDetails') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="searchForm.p" :limit.sync="searchForm.l"
      @pagination="getList" />

    <!-- 图片预览对话框 -->
    <el-dialog :title="t('table.buyImg')" :visible.sync="imageDialogVisible" width="600px" center>
      <div class="image-preview">
        <img :src="previewImageUrl" alt="购买凭证" style="max-width: 100%; max-height: 400px;" />
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog :title="t('actions.viewDetails')" :visible.sync="detailDialogVisible" width="800px">
      <el-form :model="detailForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.bluetooth')">
              <span>{{ detailForm.bluetooth }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.nickName')">
              <span>{{ detailForm.nickName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.originalName')">
              <span>{{ detailForm.originalName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.email')">
              <span>{{ detailForm.email }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.phone')">
              <span>{{ detailForm.phone }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.status')">
              <el-tag :type="detailForm.status === 0 ? 'warning' : 'success'">
                {{ detailForm.status === 0 ? t('status.pending') : t('status.processed') }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="t('form.createTime')">
          <span>{{ parseTime(detailForm.createTime) }}</span>
        </el-form-item>
        <el-form-item :label="t('form.buyImg')">
          <el-button v-if="detailForm.buyImg" size="small" @click="handleViewImage(detailForm.buyImg)">
            {{ t('actions.viewImage') }}
          </el-button>
          <span v-else class="text-muted">{{ t('messages.noImage') }}</span>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { createI18nManager } from './lang'
import { getAppealList, unbindVehicle } from '@/api/acc/appeal'

export default {
  name: "Appeal",
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      searchForm: {
        bluetooth: '',
        nickName: '',
        email: '',
        phone: '',
        p: 1,
        l: 20
      },

      // 图片预览
      imageDialogVisible: false,
      previewImageUrl: '',

      // 详情对话框
      detailDialogVisible: false,
      detailForm: {}
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.getList()
  },
  methods: {
    // 简洁的国际化文本获取方法
    t(key) {
      return this.i18nManager.getText(key)
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getAppealList({
          ...this.searchForm,
          p: String(this.searchForm.p),
          l: String(this.searchForm.l)
        })

        if (response.code === 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取解绑申述列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.p = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        bluetooth: '',
        nickName: '',
        email: '',
        phone: '',
        p: 1,
        l: 20
      }
      this.$nextTick(() => {
        this.$refs.searchForm && this.$refs.searchForm.clearValidate()
      })
      this.getList()
    },

    // 查看图片
    handleViewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.imageDialogVisible = true
    },

    // 解除绑定
    async handleUnbind(row) {
      try {
        await this.$confirm(
          this.t('actions.unbindWarning'),
          this.t('actions.confirmUnbind'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const response = await unbindVehicle(row.bluetooth)

        if (response.code === 200) {
          this.$message.success(this.t('messages.unbindSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.unbindError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('解除绑定失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 查看详情
    handleViewDetails(row) {
      this.detailForm = { ...row }
      this.detailDialogVisible = true
    },

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 20px;
  }

  p {
    margin: 5px 0 0 0;
    color: #606266;
    font-size: 14px;
  }
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 4px;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.image-preview {
  text-align: center;

  img {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      display: block;
      margin-bottom: 10px;

      .el-input {
        width: 100% !important;
      }
    }
  }

  .el-table {
    font-size: 12px;
  }
}
</style>
