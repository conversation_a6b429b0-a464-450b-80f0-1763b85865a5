<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <!-- <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
      <el-form-item :label="t('search.title')" prop="title">
        <el-input
          v-model="searchForm.title"
          :placeholder="t('search.titlePlaceholder')"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item :label="t('search.status')" prop="status">
        <el-select
          v-model="searchForm.status"
          :placeholder="t('search.statusPlaceholder')"
          clearable
          style="width: 150px"
        >
          <el-option :label="t('status.all')" value="" />
          <el-option :label="t('status.enabled')" :value="0" />
          <el-option :label="t('status.disabled')" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          {{ t('common.search') }}
        </el-button>
        <el-button @click="resetSearch">
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form> -->

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
        {{ t('actions.add') }}
      </el-button>
      <el-button type="success" icon="el-icon-check" :disabled="selectedRows.length === 0" @click="handleBatchEnable">
        {{ t('actions.batchEnable') }}
      </el-button>
      <el-button type="warning" icon="el-icon-close" :disabled="selectedRows.length === 0" @click="handleBatchDisable">
        {{ t('actions.batchDisable') }}
      </el-button>
      <el-button type="danger" icon="el-icon-delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete">
        {{ t('actions.batchDelete') }}
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" :label="t('table.title')" min-width="150" show-overflow-tooltip />
      <el-table-column prop="detail" :label="t('table.detail')" min-width="200">
        <template slot-scope="scope">
          <div class="detail-content">
            {{ scope.row.detail }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="img" :label="t('table.img')" width="120" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.img" class="table-image-container">
            <img :src="scope.row.img" :alt="scope.row.title" class="table-image" @click="handleViewImage(scope.row.img)"
              @error="handleImageError" />
          </div>
          <span v-else class="text-muted">{{ t('messages.noImage') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sort" :label="t('table.sort')" width="80" align="center" />
      <el-table-column prop="status" :label="t('table.status')" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? t('status.enabled') : t('status.disabled') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('table.createTime')" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="t('table.actions')" width="140" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)">
            {{ t('actions.edit') }}
          </el-button>
          <el-button v-if="scope.row.status === 1" size="mini" type="text" style="color: #67C23A"
            @click="handleToggleStatus(scope.row, 0)">
            {{ t('actions.enable') }}
          </el-button>
          <el-button v-else size="mini" type="text" style="color: #E6A23C" @click="handleToggleStatus(scope.row, 1)">
            {{ t('actions.disable') }}
          </el-button>
          <el-button size="mini" type="text" style="color: #F56C6C" @click="handleDelete(scope.row)">
            {{ t('actions.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="searchForm.p" :limit.sync="searchForm.l"
      @pagination="getList" />

    <!-- 表单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" @close="resetForm">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="t('form.title')" prop="title">
          <el-input v-model="form.title" :placeholder="t('form.titlePlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.detail')" prop="detail">
          <el-input v-model="form.detail" :placeholder="t('form.detailPlaceholder')" type="textarea" rows="4" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.sort')" prop="sort">
              <el-input-number v-model="form.sort" :min="0" style="width: 100%"
                :placeholder="t('form.sortPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.status')">
              <el-select v-model="form.status" style="width: 100%">
                <el-option :label="t('form.statusOptions.enabled')" :value="0" />
                <el-option :label="t('form.statusOptions.disabled')" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="t('form.img')" prop="img">
          <image-cover-upload v-model="form.img" :limit="1" :width="200" :height="150" @change="handleImageChange" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="submitForm">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :title="t('dialog.imagePreview')" :visible.sync="imageDialogVisible" width="80%" center
      :close-on-click-modal="true" class="image-preview-dialog">
      <div class="image-preview">
        <img :src="previewImageUrl" alt="引导页图片" class="preview-image" @load="handleImageLoad"
          @error="handleImageError" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="imageDialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ImageCoverUpload from '@/components/ImageCoverUpload'
import { createI18nManager } from './lang'
import {
  getGuideList,
  getGuideInfo,
  addGuide,
  updateGuide,
  deleteGuide,
  authGuide
} from '@/api/acc/guide'

export default {
  name: "Guide",
  components: {
    Pagination,
    ImageCoverUpload
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      selectedRows: [],
      searchForm: {
        title: '',
        status: '',
        p: 1,
        l: 20
      },

      // 表单对话框
      dialogVisible: false,
      dialogTitle: '',
      form: {
        id: null,
        title: '',
        detail: '',
        img: '',
        sort: 0,
        status: 0
      },
      rules: {},

      // 图片预览
      imageDialogVisible: false,
      previewImageUrl: ''
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.initRules()
    this.getList()
  },
  methods: {
    // 简洁的国际化文本获取方法
    t(key) {
      return this.i18nManager.getText(key)
    },

    // 初始化表单验证规则
    initRules() {
      this.rules = {
        title: [
          { required: true, message: this.t('validation.titleRequired'), trigger: 'blur' }
        ],
        detail: [
          { required: true, message: this.t('validation.detailRequired'), trigger: 'blur' }
        ],
        img: [
          { required: true, message: this.t('validation.imgRequired'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.t('validation.sortRequired'), trigger: 'blur' },
          { type: 'number', message: this.t('validation.sortNumber'), trigger: 'blur' }
        ]
      }
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getGuideList({
          ...this.searchForm,
          p: String(this.searchForm.p),
          l: String(this.searchForm.l)
        })

        if (response.code === 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取引导页列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.p = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        title: '',
        status: '',
        p: 1,
        l: 20
      }
      this.$nextTick(() => {
        this.$refs.searchForm && this.$refs.searchForm.clearValidate()
      })
      this.getList()
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 添加
    handleAdd() {
      this.dialogTitle = this.t('dialog.add')
      this.dialogVisible = true
    },

    // 编辑
    async handleEdit(row) {
      this.dialogTitle = this.t('dialog.edit')
      try {
        const response = await getGuideInfo(row.id)
        if (response.code === 200) {
          this.form = { ...response.data }
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取引导页详情失败:', error)
        this.$message.error(this.t('messages.loadError'))
      }
    },

    // 删除单个
    async handleDelete(row) {
      try {
        await this.$confirm(
          this.t('actions.confirmDelete'),
          this.t('common.confirm'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const response = await deleteGuide([row.id])

        if (response.code === 200) {
          this.$message.success(this.t('messages.deleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除引导页失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(
          this.t('actions.confirmDelete'),
          this.t('common.confirm'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const ids = this.selectedRows.map(row => row.id)
        const response = await deleteGuide(ids)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDeleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除引导页失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 切换状态
    async handleToggleStatus(row, newStatus) {
      try {
        const authList = [{
          id: row.id,
          email: row.email || '',
          status: newStatus
        }]

        const response = await authGuide(authList)

        if (response.code === 200) {
          const successMsg = newStatus === 0 ?
            this.t('messages.enableSuccess') :
            this.t('messages.disableSuccess')
          this.$message.success(successMsg)
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        console.error('切换状态失败:', error)
        this.$message.error(this.t('messages.operationError'))
      }
    },

    // 批量启用
    async handleBatchEnable() {
      if (this.selectedRows.length === 0) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(
          this.t('actions.confirmEnable'),
          this.t('common.confirm'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const authList = this.selectedRows.map(row => ({
          id: row.id,
          email: row.email || '',
          status: 0
        }))

        const response = await authGuide(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchEnableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量启用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量禁用
    async handleBatchDisable() {
      if (this.selectedRows.length === 0) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(
          this.t('actions.confirmDisable'),
          this.t('common.confirm'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const authList = this.selectedRows.map(row => ({
          id: row.id,
          email: row.email || '',
          status: 1
        }))

        const response = await authGuide(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDisableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量禁用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 查看图片
    handleViewImage(imageUrl) {
      this.previewImageUrl = imageUrl
      this.imageDialogVisible = true
    },

    // 图片加载成功处理
    handleImageLoad(event) {
      console.log('图片加载成功:', event.target.src)
    },

    // 图片加载错误处理
    handleImageError(event) {
      // 使用一个简单的错误占位图
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0yOCAyOEwzNiAzNkwyOCA0NEgyMFYyOEgyOFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'
      event.target.style.opacity = '0.5'
      event.target.style.cursor = 'not-allowed'
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.form.validate()

        const isEdit = !!this.form.id
        const apiMethod = isEdit ? updateGuide : addGuide

        const response = await apiMethod(this.form)

        if (response.code === 200) {
          const successMsg = isEdit ?
            this.t('messages.updateSuccess') :
            this.t('messages.addSuccess')
          this.$message.success(successMsg)
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        console.error('提交表单失败:', error)
      }
    },

    // 重置表单
    resetForm() {
      this.form = {
        id: null,
        title: '',
        detail: '',
        img: '',
        sort: 0,
        status: 0
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    // 图片变化处理
    handleImageChange(urls) {
      console.log('引导页图片更新:', urls)
      // ImageCoverUpload组件会自动更新v-model，这里可以做额外处理
    },


  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 20px;
  }

  p {
    margin: 5px 0 0 0;
    color: #606266;
    font-size: 14px;
  }
}

.search-form {
  background: #f5f7fa;
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.detail-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.5;
  padding: 8px 0;
  max-width: none;
}

.dialog-footer {
  text-align: right;
}

.table-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
}

.table-image {
  max-width: 80px;
  max-height: 50px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
  border: 1px solid #e8e8e8;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #409eff;
  }
}

.image-preview {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  cursor: zoom-in;

  &:hover {
    transform: scale(1.02);
  }
}

.image-preview-dialog {
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      display: block;
      margin-bottom: 10px;

      .el-input,
      .el-select {
        width: 100% !important;
      }
    }
  }

  .toolbar {
    .el-button {
      margin-bottom: 10px;
      width: 100%;
    }
  }

  .el-table {
    font-size: 12px;
  }

  .table-image {
    max-width: 60px;
    max-height: 40px;
  }

  .image-preview-dialog {
    width: 95% !important;

    .preview-image {
      max-height: 50vh;
    }
  }
}
</style>
