<template>
  <div class="app-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-card>
        <!-- 搜索表单 -->
        <!-- <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
          <el-form-item :label="t('search.code')" prop="code">
            <el-input
              v-model="searchForm.code"
              :placeholder="t('search.codePlaceholder')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="t('search.content')" prop="content">
            <el-input
              v-model="searchForm.content"
              :placeholder="t('search.contentPlaceholder')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="t('search.status')" prop="status">
            <el-select
              v-model="searchForm.status"
              :placeholder="t('search.statusPlaceholder')"
              clearable
              style="width: 120px"
            >
              <el-option :label="t('status.all')" value="" />
              <el-option :label="t('status.normal')" :value="0" />
              <el-option :label="t('status.disabled')" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              {{ t('common.search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ t('common.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
         -->
        <!-- 操作按钮 -->
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            {{ t('actions.add') }}
          </el-button>
          <el-button type="success" icon="el-icon-check" :disabled="!hasSelection" @click="handleBatchEnable">
            {{ t('actions.batchEnable') }}
          </el-button>
          <el-button type="warning" icon="el-icon-close" :disabled="!hasSelection" @click="handleBatchDisable">
            {{ t('actions.batchDisable') }}
          </el-button>
          <el-button type="danger" icon="el-icon-delete" :disabled="!hasSelection" @click="handleBatchDelete">
            {{ t('actions.batchDelete') }}
          </el-button>
        </div>

        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="tableData" style="width: 100%" @selection-change="handleSelectionChange"
          :height="tableHeight()">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" :label="t('table.code')" width="120" />
          <el-table-column prop="content" :label="t('table.content')" show-overflow-tooltip />
          <el-table-column prop="enContent" :label="t('table.enContent')" show-overflow-tooltip />
          <el-table-column prop="img" :label="t('table.img')" width="100">
            <template slot-scope="scope">
              <preview-img v-if="scope.row.img" :imgUrl="scope.row.img" width="50px" height="50px" />
              <span v-else class="no-image">{{ t('messages.noImage') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="link" :label="t('table.link')" width="120">
            <template slot-scope="scope">
              <el-button v-if="scope.row.link" type="text" size="mini" @click="openLink(scope.row.link)">
                {{ t('actions.viewLink') }}
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sort" :label="t('table.sort')" width="80" sortable />
          <el-table-column prop="status" :label="t('table.status')" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                {{ scope.row.status === 0 ? t('status.normal') : t('status.disabled') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('table.actions')" width="200" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="handleEdit(scope.row)">
                {{ t('actions.edit') }}
              </el-button>
              <el-button type="text" size="mini" :class="scope.row.status === 0 ? 'danger-text' : 'success-text'"
                @click="handleToggleStatus(scope.row)">
                {{ scope.row.status === 0 ? t('actions.disable') : t('actions.enable') }}
              </el-button>
              <el-button type="text" size="mini" class="danger-text" @click="handleDelete(scope.row)">
                {{ t('actions.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="searchForm.p" :limit.sync="searchForm.l"
          @pagination="getList" />
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="isEdit ? t('dialog.edit') : t('dialog.add')" :visible.sync="dialogVisible" width="600px"
      :close-on-click-modal="false" @close="handleDialogClose">
      <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="120px">
        <el-form-item :label="t('form.code')" prop="code">
          <el-input v-model="formData.code" :placeholder="t('form.codePlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.content')" prop="content">
          <el-input v-model="formData.content" type="textarea" :rows="3" :placeholder="t('form.contentPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.enContent')" prop="enContent">
          <el-input v-model="formData.enContent" type="textarea" :rows="3"
            :placeholder="t('form.enContentPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.img')" prop="img">
          <ImageCoverUpload :limit="1" v-model="formData.img" :placeholder="t('form.imgPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.link')" prop="link">
          <el-input v-model="formData.link" :placeholder="t('form.linkPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.sort')" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="9999" :placeholder="t('form.sortPlaceholder')"
            style="width: 100%" />
        </el-form-item>
        <el-form-item :label="t('form.status')" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="0">{{ t('form.statusOptions.normal') }}</el-radio>
            <el-radio :label="1">{{ t('form.statusOptions.disabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ImageCoverUpload from '@/components/ImageCoverUpload'
import { createI18nManager } from './lang'
import {
  getDeviceAdList,
  getDeviceAdInfo,
  addDeviceAd,
  updateDeviceAd,
  deleteDeviceAd,
  authDeviceAd
} from '@/api/acc/deviceAd'

export default {
  name: "DeviceAd",
  components: {
    Pagination,
    ImageCoverUpload
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      tableData: [],
      total: 0,
      selectedRows: [],
      searchForm: {
        code: '',
        content: '',
        status: '',
        p: 1,
        l: 20
      },
      dialogVisible: false,
      isEdit: false,
      formData: {
        id: '',
        code: '',
        content: '',
        enContent: '',
        img: '',
        link: '',
        sort: 0,
        status: 0
      },
      formRules: {}
    }
  },
  computed: {
    hasSelection() {
      return this.selectedRows.length > 0
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.initFormRules()
    this.getList()
  },
  methods: {
    // 简洁的国际化文本获取方法
    t(key) {
      return this.i18nManager.getText(key)
    },

    // 初始化表单验证规则
    initFormRules() {
      this.formRules = {
        code: [
          { required: true, message: this.t('validation.codeRequired'), trigger: 'blur' }
        ],
        content: [
          { required: true, message: this.t('validation.contentRequired'), trigger: 'blur' }
        ],
        enContent: [
          { required: true, message: this.t('validation.enContentRequired'), trigger: 'blur' }
        ],
        img: [
          { required: true, message: this.t('validation.imgRequired'), trigger: 'change' }
        ],
        sort: [
          { required: true, message: this.t('validation.sortRequired'), trigger: 'blur' },
          { type: 'number', message: this.t('validation.sortNumber'), trigger: 'blur' }
        ],
        // link: [
        //   { 
        //     required: false,
        //     pattern: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
        //     message: this.t('validation.linkFormat'),
        //     trigger: 'blur'
        //   }
        // ]
      }
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getDeviceAdList(this.searchForm)
        if (response.code === 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取设备广告列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.p = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        code: '',
        content: '',
        status: '',
        p: 1,
        l: 20
      }
      this.$nextTick(() => {
        this.$refs.searchForm && this.$refs.searchForm.clearValidate()
      })
      this.getList()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.formData = {
        id: '',
        code: '',
        content: '',
        enContent: '',
        img: '',
        link: '',
        sort: 0,
        status: 0
      }
      this.dialogVisible = true
    },

    // 编辑
    async handleEdit(row) {
      this.isEdit = true
      try {
        const response = await getDeviceAdInfo(row.id)
        if (response.code === 200) {
          this.formData = { ...response.data }
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取设备广告详情失败:', error)
        this.$message.error(this.t('messages.loadError'))
      }
    },

    // 切换状态
    async handleToggleStatus(row) {
      const newStatus = row.status === 0 ? 1 : 0
      const action = newStatus === 0 ? 'enable' : 'disable'
      const confirmMessage = newStatus === 0 ? this.t('actions.confirmEnable') : this.t('actions.confirmDisable')

      try {
        await this.$confirm(confirmMessage, this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = [{ id: row.id, status: newStatus }]
        const response = await authDeviceAd(authList)

        if (response.code === 200) {
          this.$message.success(this.t(`messages.${action}Success`))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('状态切换失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm(this.t('actions.confirmDelete'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const response = await deleteDeviceAd([row.id])
        if (response.code === 200) {
          this.$message.success(this.t('messages.deleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量启用
    async handleBatchEnable() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmEnable'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = this.selectedRows.map(row => ({ id: row.id, status: 0 }))
        const response = await authDeviceAd(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchEnableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量启用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量禁用
    async handleBatchDisable() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmDisable'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = this.selectedRows.map(row => ({ id: row.id, status: 1 }))
        const response = await authDeviceAd(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDisableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量禁用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmDelete'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const ids = this.selectedRows.map(row => row.id)
        const response = await deleteDeviceAd(ids)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDeleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 打开链接
    openLink(link) {
      if (!link) {
        this.$message.warning(this.t('messages.invalidLink'))
        return
      }

      let url = link
      if (!link.startsWith('http://') && !link.startsWith('https://')) {
        url = 'http://' + link
      }

      window.open(url, '_blank')
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.dataForm.validate()
        this.submitLoading = true

        const apiCall = this.isEdit ? updateDeviceAd : addDeviceAd
        const response = await apiCall(this.formData)

        if (response.code === 200) {
          this.$message.success(this.t(this.isEdit ? 'messages.updateSuccess' : 'messages.addSuccess'))
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时返回 false
          console.error('提交失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      } finally {
        this.submitLoading = false
      }
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.dataForm && this.$refs.dataForm.resetFields()
      this.formData = {
        id: '',
        code: '',
        content: '',
        enContent: '',
        img: '',
        link: '',
        sort: 0,
        status: 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h3 {
    margin: 0;
    color: #303133;
  }

  p {
    margin: 5px 0 0 0;
    color: #606266;
    font-size: 14px;
  }
}

.search-form {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

.danger-text {
  color: #f56c6c;
}

.success-text {
  color: #67c23a;
}

.dialog-footer {
  text-align: right;
}
</style>
