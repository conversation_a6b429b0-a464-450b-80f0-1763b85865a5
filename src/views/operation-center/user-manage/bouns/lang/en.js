export default {
  title: 'Check-in Management',
  description: 'Manage user check-in base rules and special day bonus rules',
  
  tabs: {
    baseRule: 'Base Rule',
    bonusRule: 'Bonus Rule'
  },
  
  baseRule: {
    title: 'Base Day Rules',
    description: 'Set basic rewards for daily user check-ins',
    coins: 'Coins',
    points: 'Points added over previous day',
    coinsPlaceholder: 'Please enter coin amount',
    pointsPlaceholder: 'Please enter points amount',
    saveBtn: 'Save Base Rule',
    saveSuccess: 'Base rule saved successfully'
  },
  
  bonusRule: {
    title: 'Special Day Rules',
    description: 'Set additional reward rules for specific days',
    triggerDay: 'Trigger Day',
    coins: 'Reward Coins',
    points: 'Reward Points',
    triggerDayPlaceholder: 'Please enter trigger day',
    coinsPlaceholder: 'Please enter reward coins',
    pointsPlaceholder: 'Please enter reward points'
  },
  
  search: {
    triggerDay: 'Trigger Day',
    triggerDayPlaceholder: 'Please enter trigger day'
  },
  
  table: {
    id: 'ID',
    triggerDay: 'Trigger Day',
    coins: 'Reward Coins',
    points: 'Reward Points',
    actions: 'Actions'
  },
  
  actions: {
    add: 'Add Rule',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    reset: 'Reset',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    batchDelete: 'Batch Delete',
    confirmDelete: 'Are you sure to delete this rule?',
    confirmBatchDelete: 'Are you sure to delete the selected {count} rules?',
    selectItems: 'Please select items to delete'
  },
  
  form: {
    addTitle: 'Add Special Rule',
    editTitle: 'Edit Special Rule'
  },
  
  validation: {
    triggerDayRequired: 'Trigger day is required',
    coinsRequired: 'Coins amount is required',
    pointsRequired: 'Points amount is required',
    triggerDayInvalid: 'Trigger day must be a positive integer',
    coinsInvalid: 'Coins amount must be a non-negative integer',
    pointsInvalid: 'Points amount must be a non-negative integer',
    triggerDayExists: 'This trigger day already exists, please choose another'
  },
  
  messages: {
    addSuccess: 'Rule added successfully',
    updateSuccess: 'Rule updated successfully',
    deleteSuccess: 'Rule deleted successfully',
    batchDeleteSuccess: 'Batch delete successfully',
    loadError: 'Failed to load data, please try again',
    saveError: 'Failed to save, please try again',
    deleteError: 'Failed to delete, please try again'
  }
}
