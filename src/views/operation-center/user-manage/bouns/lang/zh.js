export default {
  title: '签到管理',
  description: '管理用户签到基础规则和特殊天数奖励规则',
  
  tabs: {
    baseRule: '基础规则',
    bonusRule: '特殊规则'
  },
  
  baseRule: {
    title: '基础天数规则',
    description: '设置用户每日签到的基础奖励',
    coins: '金币',
    points: '比前一天加的积分',
    coinsPlaceholder: '请输入金币数量',
    pointsPlaceholder: '请输入积分数量',
    saveBtn: '保存基础规则',
    saveSuccess: '基础规则保存成功'
  },
  
  bonusRule: {
    title: '特殊天数规则',
    description: '设置特定天数的额外奖励规则',
    triggerDay: '触发天数',
    coins: '奖励金币',
    points: '奖励积分',
    triggerDayPlaceholder: '请输入触发天数',
    coinsPlaceholder: '请输入奖励金币',
    pointsPlaceholder: '请输入奖励积分'
  },
  
  search: {
    triggerDay: '触发天数',
    triggerDayPlaceholder: '请输入触发天数'
  },
  
  table: {
    id: 'ID',
    triggerDay: '触发天数',
    coins: '奖励金币',
    points: '奖励积分',
    actions: '操作'
  },
  
  actions: {
    add: '添加规则',
    edit: '编辑',
    delete: '删除',
    search: '搜索',
    reset: '重置',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    batchDelete: '批量删除',
    confirmDelete: '确定要删除这条规则吗？',
    confirmBatchDelete: '确定要删除选中的 {count} 条规则吗？',
    selectItems: '请选择要删除的项目'
  },
  
  form: {
    addTitle: '添加特殊规则',
    editTitle: '编辑特殊规则'
  },
  
  validation: {
    triggerDayRequired: '触发天数不能为空',
    coinsRequired: '金币数量不能为空',
    pointsRequired: '积分数量不能为空',
    triggerDayInvalid: '触发天数必须是正整数',
    coinsInvalid: '金币数量必须是非负整数',
    pointsInvalid: '积分数量必须是非负整数',
    triggerDayExists: '该触发天数已存在，请选择其他天数'
  },
  
  messages: {
    addSuccess: '规则添加成功',
    updateSuccess: '规则更新成功',
    deleteSuccess: '规则删除成功',
    batchDeleteSuccess: '批量删除成功',
    loadError: '数据加载失败，请重试',
    saveError: '保存失败，请重试',
    deleteError: '删除失败，请重试'
  }
}
