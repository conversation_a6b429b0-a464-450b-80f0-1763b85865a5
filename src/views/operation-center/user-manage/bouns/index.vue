<template>
  <div class="app-container">
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <!-- 基础规则 -->
      <el-tab-pane :label="t('tabs.baseRule')" name="base">
        <el-card>
          <div slot="header" class="clearfix">
            <span>{{ t('baseRule.title') }}</span>
          </div>

          <div class="base-rule-content">
            <p class="rule-description">{{ t('baseRule.description') }}</p>

            <el-form ref="baseForm" :model="baseForm" :rules="baseRules" label-width="160px" class="base-form">
              <el-form-item :label="t('baseRule.coins')" prop="coins">
                <el-input-number v-model="baseForm.coins" :min="0" :precision="0"
                  :placeholder="t('baseRule.coinsPlaceholder')" style="width: 200px" />
              </el-form-item>

              <el-form-item :label="t('baseRule.points')" prop="points">
                <el-input-number v-model="baseForm.points" :min="0" :precision="0"
                  :placeholder="t('baseRule.pointsPlaceholder')" style="width: 200px" />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" :loading="baseLoading" @click="saveBaseRule">
                  {{ t('baseRule.saveBtn') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 特殊规则 -->
      <el-tab-pane :label="t('tabs.bonusRule')" name="bonus">
        <el-card>
          <div slot="header" class="clearfix">
            <span>{{ t('bonusRule.title') }}</span>
          </div>

          <div class="bonus-rule-content">
            <p class="rule-description">{{ t('bonusRule.description') }}</p>
            <!-- 操作按钮 -->
            <div class="toolbar">
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                {{ t('actions.add') }}
              </el-button>
              <el-button type="danger" icon="el-icon-delete" :disabled="multipleSelection.length === 0"
                @click="handleBatchDelete">
                {{ t('actions.batchDelete') }}
              </el-button>
            </div>

            <!-- 数据表格 -->
            <el-table ref="bonusTable" v-loading="bonusLoading" :data="bonusTableData" style="width: 100%"
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="triggerDay" :label="t('table.triggerDay')" sortable />
              <el-table-column prop="coins" :label="t('table.coins')" />
              <el-table-column prop="points" :label="t('table.points')" />
              <el-table-column :label="t('table.actions')" width="180">
                <template slot-scope="scope">
                  <el-button size="mini" @click="handleEdit(scope.row)">
                    {{ t('actions.edit') }}
                  </el-button>
                  <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
                    {{ t('actions.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination v-show="bonusTotal > 0" :total="bonusTotal" :page.sync="searchForm.page"
              :limit.sync="searchForm.limit" @pagination="getBonusList" />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="resetForm">
      <el-form ref="bonusForm" :model="bonusForm" :rules="bonusRules" label-width="120px">
        <el-form-item :label="t('bonusRule.triggerDay')" prop="triggerDay">
          <el-input-number v-model="bonusForm.triggerDay" :min="1" :precision="0"
            :placeholder="t('bonusRule.triggerDayPlaceholder')" :disabled="isEdit" style="width: 100%" />
        </el-form-item>

        <el-form-item :label="t('bonusRule.coins')" prop="coins">
          <el-input-number v-model="bonusForm.coins" :min="0" :precision="0"
            :placeholder="t('bonusRule.coinsPlaceholder')" style="width: 100%" />
        </el-form-item>

        <el-form-item :label="t('bonusRule.points')" prop="points">
          <el-input-number v-model="bonusForm.points" :min="0" :precision="0"
            :placeholder="t('bonusRule.pointsPlaceholder')" style="width: 100%" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ t('actions.cancel') }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ t('actions.save') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { createI18nManager } from './lang'
import {
  getBaseInfo,
  editBase,
  getBonusList,
  addBonus,
  editBonus,
  deleteBonus
} from '@/api/check'

export default {
  name: "CheckinManagement",
  components: {
    Pagination
  },
  data() {
    return {
      // Tab切换
      activeTab: 'base',

      // 基础规则相关
      baseLoading: false,
      baseForm: {
        id: '',
        coins: 0,
        points: 0
      },

      // 特殊规则相关
      bonusLoading: false,
      bonusTableData: [],
      bonusTotal: 0,
      multipleSelection: [],

      // 搜索表单
      searchForm: {
        triggerDay: null,
        page: 1,
        limit: 10
      },

      // 弹窗相关
      dialogVisible: false,
      submitLoading: false,
      isEdit: false,
      bonusForm: {
        id: '',
        triggerDay: null,
        coins: 0,
        points: 0
      }
    }
  },
  computed: {
    // 弹窗标题
    dialogTitle() {
      return this.isEdit ? this.t('form.editTitle') : this.t('form.addTitle')
    },

    // 表单验证规则
    baseRules() {
      return {
        coins: [
          { required: true, message: this.t('validation.coinsRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.coinsInvalid'), trigger: 'blur' }
        ],
        points: [
          { required: true, message: this.t('validation.pointsRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.pointsInvalid'), trigger: 'blur' }
        ]
      }
    },

    bonusRules() {
      return {
        triggerDay: [
          { required: true, message: this.t('validation.triggerDayRequired'), trigger: 'blur' },
          { type: 'number', min: 1, message: this.t('validation.triggerDayInvalid'), trigger: 'blur' },
          { validator: this.validateTriggerDay, trigger: 'blur' }
        ],
        coins: [
          { required: true, message: this.t('validation.coinsRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.coinsInvalid'), trigger: 'blur' }
        ],
        points: [
          { required: true, message: this.t('validation.pointsRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.pointsInvalid'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.getBaseInfo()
    this.getBonusList()
  },
  methods: {
    // 简洁的国际化文本获取方法（支持参数）
    t(key, args) {
      return this.i18nManager.getText(key, args)
    },

    // 获取基础规则信息
    async getBaseInfo() {
      try {
        this.baseLoading = true
        const response = await getBaseInfo()
        if (response.code === 200 && response.data) {
          this.baseForm = {
            id: response.data.id || '',
            coins: response.data.coins || 0,
            points: response.data.points || 0
          }
        }
      } catch (error) {
        this.$message.error(this.t('messages.loadError'))
        console.error('获取基础规则失败:', error)
      } finally {
        this.baseLoading = false
      }
    },

    // 保存基础规则
    async saveBaseRule() {
      try {
        await this.$refs.baseForm.validate()
        this.baseLoading = true

        const response = await editBase(this.baseForm)
        if (response.code === 200) {
          this.$message.success(this.t('baseRule.saveSuccess'))
          this.getBaseInfo() // 重新获取数据
        } else {
          this.$message.error(response.msg || this.t('messages.saveError'))
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$message.error(this.t('messages.saveError'))
          console.error('保存基础规则失败:', error)
        }
      } finally {
        this.baseLoading = false
      }
    },

    // 获取特殊规则列表
    async getBonusList() {
      try {
        this.bonusLoading = true
        const params = {
          page: this.searchForm.page,
          limit: this.searchForm.limit
        }

        // 如果有搜索条件，添加到参数中
        if (this.searchForm.triggerDay) {
          params.triggerDay = this.searchForm.triggerDay
        }

        const response = await getBonusList(params)
        if (response.code === 200 && response.data) {
          this.bonusTableData = response.data.list || []
          this.bonusTotal = response.data.total || 0
        }
      } catch (error) {
        this.$message.error(this.t('messages.loadError'))
        console.error('获取特殊规则列表失败:', error)
      } finally {
        this.bonusLoading = false
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.page = 1
      this.getBonusList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm.triggerDay = null
      this.searchForm.page = 1
      this.getBonusList()
    },

    // 添加特殊规则
    handleAdd() {
      this.isEdit = false
      this.bonusForm = {
        id: '',
        triggerDay: null,
        coins: 0,
        points: 0
      }
      this.dialogVisible = true
    },

    // 编辑特殊规则
    handleEdit(row) {
      this.isEdit = true
      this.bonusForm = {
        id: row.id,
        triggerDay: row.triggerDay,
        coins: row.coins,
        points: row.points
      }
      this.dialogVisible = true
    },

    // 删除特殊规则
    async handleDelete(row) {
      try {
        await this.$confirm(
          this.t('actions.confirmDelete'),
          this.t('actions.confirm'),
          {
            confirmButtonText: this.t('actions.confirm'),
            cancelButtonText: this.t('actions.cancel'),
            type: 'warning'
          }
        )

        const response = await deleteBonus([row.id])
        if (response.code === 200) {
          this.$message.success(this.t('messages.deleteSuccess'))
          this.getBonusList()
        } else {
          this.$message.error(response.msg || this.t('messages.deleteError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.t('messages.deleteError'))
          console.error('删除特殊规则失败:', error)
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning(this.t('actions.selectItems'))
        return
      }

      try {
        await this.$confirm(
          this.t('actions.confirmBatchDelete', { count: this.multipleSelection.length }),
          this.t('actions.confirm'),
          {
            confirmButtonText: this.t('actions.confirm'),
            cancelButtonText: this.t('actions.cancel'),
            type: 'warning'
          }
        )

        const ids = this.multipleSelection.map(item => item.id)
        const response = await deleteBonus(ids)
        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDeleteSuccess'))
          this.getBonusList()
          this.$refs.bonusTable.clearSelection()
        } else {
          this.$message.error(response.msg || this.t('messages.deleteError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(this.t('messages.deleteError'))
          console.error('批量删除特殊规则失败:', error)
        }
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.bonusForm.validate()
        this.submitLoading = true

        let response
        if (this.isEdit) {
          response = await editBonus(this.bonusForm)
        } else {
          response = await addBonus(this.bonusForm)
        }

        if (response.code === 200) {
          this.$message.success(
            this.isEdit ? this.t('messages.updateSuccess') : this.t('messages.addSuccess')
          )
          this.dialogVisible = false
          this.getBonusList()
        } else {
          this.$message.error(response.msg || this.t('messages.saveError'))
        }
      } catch (error) {
        if (error !== false) {
          this.$message.error(this.t('messages.saveError'))
          console.error('提交表单失败:', error)
        }
      } finally {
        this.submitLoading = false
      }
    },

    // 重置表单
    resetForm() {
      if (this.$refs.bonusForm) {
        this.$refs.bonusForm.resetFields()
      }
    },

    // 验证触发天数是否重复
    validateTriggerDay(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 编辑时，如果天数没有变化，则不需要验证
      if (this.isEdit && this.bonusTableData.some(item =>
        item.id !== this.bonusForm.id && item.triggerDay === value
      )) {
        callback(new Error(this.t('validation.triggerDayExists')))
      } else if (!this.isEdit && this.bonusTableData.some(item =>
        item.triggerDay === value
      )) {
        callback(new Error(this.t('validation.triggerDayExists')))
      } else {
        callback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.app-container {
  padding: 20px;
}

// 页面头部样式
.page-header {
  margin-bottom: 20px;
  padding: 20px 0;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }

  p {
    margin: 8px 0 0 0;
    font-size: 14px;
    color: #909399;
  }
}

// Tab切换样式
.main-tabs {
  background: white;
  border-radius: 4px;

  :deep(.el-tabs__header) {
    margin: 0;
    background-color: #fafafa;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #606266;

    &.is-active {
      color: #409eff;
      font-weight: 500;
    }

    &:hover {
      color: #409eff;
    }
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }

  :deep(.el-tab-pane) {
    padding: 20px;
  }
}

// 规则描述样式
.rule-description {
  margin: 0 0 20px 0;
  padding: 12px 16px;
  background-color: #ecf5ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

// 基础规则内容样式
.base-rule-content {
  .base-form {
    max-width: 600px;
    padding: 20px;
    border-radius: 4px;

    :deep(.el-form-item) {
      margin-bottom: 22px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }
  }
}

// 特殊规则内容样式
.bonus-rule-content {

  // 搜索表单样式
  .search-form {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: #f5f7fa;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  // 工具栏样式
  .toolbar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

// 表单样式
.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 表格样式
:deep(.el-table) {
  th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 500;
    text-align: center;
  }

  td {
    text-align: center;
  }
}

// 分页样式
:deep(.pagination-container) {
  padding: 20px 0;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }

  .main-tabs {
    :deep(.el-tabs__nav-wrap) {
      padding: 0 16px;
    }

    :deep(.el-tab-pane) {
      padding: 16px;
    }
  }

  .base-rule-content .base-form {
    padding: 16px;
  }

  .bonus-rule-content {
    .search-form {
      padding: 16px;
    }

    .toolbar {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>