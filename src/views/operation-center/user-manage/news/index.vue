<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <news-search-form :query-params="queryParams" :show-search="showSearch" @query="handleQuery" @reset="resetQuery" />

    <!-- 操作按钮 -->
    <news-operation-buttons :multiple="multiple" :show-search.sync="showSearch" @add="handleAdd"
      @delete="handleDelete" />

    <!-- 数据表格 -->
    <news-table :news-list="newsList" :loading="loading" :query-params="queryParams"
      @selection-change="handleSelectionChange" @status-change="handleStatusChange" @view="handleView"
      @update="handleUpdate" @delete="handleDelete" />

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改新闻对话框 -->
    <news-edit-dialog :open.sync="open" :title="title" :form="form" @submit="submitForm" @cancel="cancel" />

    <!-- 新闻详情对话框 -->
    <news-detail-dialog :view-open.sync="viewOpen" :view-data="viewData" :detail-loading="detailLoading" />
  </div>
</template>

<script>
import { getNewsList, getNewsInfo, saveNews, updateNews, deleteNews } from "@/api/news/index";



import NewsSearchForm from "./components/NewsSearchForm.vue";
import NewsOperationButtons from "./components/NewsOperationButtons.vue";
import NewsTable from "./components/NewsTable.vue";
import NewsEditDialog from "./components/NewsEditDialog.vue";
import NewsDetailDialog from "./components/NewsDetailDialog.vue";
import { createI18nManager } from './lang'
export default {
  name: "News",
  components: {
    NewsSearchForm,
    NewsOperationButtons,
    NewsTable,
    NewsEditDialog,
    NewsDetailDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻表格数据
      newsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看详情弹出层
      viewOpen: false,
      // 查看数据
      viewData: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 详情加载状态
      detailLoading: true,

    };
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n);
    this.getList();
  },
  methods: {
    t(key, args) {
      if (!this.i18nManager) {
        return key;
      }
      return this.i18nManager.getText(key, args);
    },

    /** 查询新闻列表 */
    getList() {
      this.loading = true;
      const params = {
        p: this.queryParams.pageNum,
        l: this.queryParams.pageSize,
        title: this.queryParams.title,
        status: this.queryParams.status,
      };

      getNewsList(params)
        .then((response) => {
          this.newsList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        })
        .catch((error) => {
          console.error(this.t("dialog.failedToGetNewsList"), error);
          this.loading = false;
          this.$message.error(this.t("dialog.failedToGetNewsList"));
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.viewOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: "",
        content: "",
        thumbnail: "", // coverImage组件使用字符串
        imgs: [], // fileUpload组件使用数组
        status: 0,
        likeCount: 0,
        commentCount: 0,
        createBy: null,
        createTime: null,
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      if (this.$refs.queryForm) {
        this.$refs.queryForm.resetFields();
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.t("dialog.addNews");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 确保详情弹窗关闭
      this.viewOpen = false;
      this.viewData = null;
      
      this.reset();
      const id = row.id || this.ids;
      let _this = this;
      getNewsInfo(id)
        .then((response) => {
          this.form = response.data;

          // 处理图片数据回显
          // 文章图片 - 将字符串转换为数组
          if (this.form.imgs && typeof this.form.imgs === "string") {
            this.form.imgs = this.form.imgs
              .split(",")
              .filter((img) => img.trim());
          }
          // 缩略图 - ImageCoverUpload组件使用字符串URL
          // 如果后端返回的是数组，则取第一个；如果是字符串则直接使用
          if (Array.isArray(this.form.thumbnail)) {
            this.form.thumbnail = this.form.thumbnail.length > 0 ? this.form.thumbnail[0] : "";
          } else if (!this.form.thumbnail) {
            this.form.thumbnail = [];
          }
          this.open = true;
          this.title = this.t("dialog.editNews");
        })
        .catch((error) => {
          console.error(_this.t("dialog.failedToGetNewsDetail"), error);
          _this.$message.error(_this.t("dialog.failedToGetNewsDetail"));
        });
    },
    /** 查看按钮操作 */
    handleView(row) {
      // 确保编辑弹窗关闭
      this.open = false;
      this.form = {};
      
      const id = row.id;
      this.detailLoading = true;
      getNewsInfo(id)
        .then((response) => {
          this.viewData = response.data;
          this.viewOpen = true;
        })
        .catch((error) => {
          console.error(this.t("dialog.failedToGetNewsDetail"), error);
          this.$message.error(this.t("dialog.failedToGetNewsDetail"));
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    /** 提交按钮 */
    submitForm(formData) {
      // 处理图片数据
      // 处理文章图片 - 如果是数组则转换为逗号分隔的字符串
      if (Array.isArray(formData.imgs)) {
        formData.imgs = formData.imgs.join(",");
      }

      // 处理缩略图 - ImageCoverUpload组件可能返回字符串或数组
      // 确保最终提交的是字符串格式
      if (Array.isArray(formData.thumbnail)) {
        formData.thumbnail = formData.thumbnail.length > 0 ? formData.thumbnail[0] : "";
      }
      // 确保thumbnail是字符串，如果为空则设为空字符串
      if (!formData.thumbnail) {
        formData.thumbnail = "";
      }

      if (formData.id != null) {
        updateNews(formData)
          .then((response) => {
            this.$message.success(this.t("dialog.modifiedSuccessfully"));
            this.open = false;
            this.getList();
          })
          .catch((error) => {
            this.$message.error(this.t("dialog.modifiedSuccessfully"));
          });
      } else {
        saveNews(formData)
          .then((response) => {
            this.$message.success(this.t("dialog.addedSuccessfully"));
            this.open = false;
            this.getList();
          })
          .catch((error) => {
            console.error(this.t("dialog.addedSuccessfully"), error);
            this.$message.error(this.t("dialog.addedSuccessfully"));
          });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        this.t("dialog.confirmDelete", [ids]),
        this.t("dialog.warn"),
        {
          confirmButtonText: this.t("dialog.confirm"),
          cancelButtonText: this.t("dialog.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          return deleteNews(Array.isArray(ids) ? ids : [ids]);
        })
        .then(() => {
          this.getList();
          this.$message.success(this.t("dialog.deleteSuccess"));
        })
        .catch((error) => {
          if (error !== "cancel") {
            console.error(this.t("dialog.deleteFailed"), error);
            this.$message.error(this.t("dialog.deleteFailed"));
          }
        });
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let actionText = row.status === 0 ? this.t("dialog.enable") : this.t("dialog.disable");
      this.$confirm(
        this.t("dialog.confirmStatusChange", [actionText]),
        this.t("dialog.warn"),
        {
          confirmButtonText: this.t("dialog.confirm"),
          cancelButtonText: this.t("dialog.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          return updateNews(row);
        })
        .then(() => {
          this.$message.success(actionText + this.t("dialog.addSuccess"));
        })
        .catch((error) => {
          if (error !== "cancel") {
            console.error(this.t("dialog.statusChangeFailed"), error);
            this.$message.error(this.t("dialog.statusChangeFailed"));
          }
          row.status = row.status === 0 ? 1 : 0;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.no-image {
  color: #909399;
  font-size: 12px;
}

.news-detail {
  .detail-item {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;

    label {
      width: 100px;
      font-weight: bold;
      color: #606266;
      flex-shrink: 0;
    }

    span {
      color: #303133;
    }

    .content {
      flex: 1;
      max-height: 300px;
      overflow-y: auto;
      padding: 8px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #fafafa;

      ::v-deep img {
        max-width: 100%;
        height: auto;
      }
    }
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    .detail-item {
      margin-bottom: 0;
      margin-right: 40px;
      flex: none;
    }
  }

  .image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

// 表格状态开关样式
::v-deep .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

::v-deep .el-switch__label--left {
  z-index: 9;
  left: 6px;
}

::v-deep .el-switch__label--right {
  z-index: 9;
  right: 6px;
}

::v-deep .el-switch__label.is-active {
  display: block;
}

::v-deep .el-switch.is-checked .el-switch__label--left {
  display: block;
}

::v-deep .el-switch .el-switch__label--right {
  display: block;
}

// 富文本编辑器样式优化
::v-deep .tinymce-container {
  width: 100% !important;
}

// 缩略图上传组件样式
::v-deep .image-cover-upload {
  .el-upload--picture-card {
    width: 120px;
    height: 120px;
    line-height: 120px;
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 120px;
    height: 120px;
  }
}

// 图片预览对话框样式
::v-deep .image-preview-dialog {
  .el-message-box__content {
    text-align: center;
    padding: 20px;
  }

  .el-message-box__message {
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 5vh auto !important;
    }

    .news-detail {
      .detail-header {
        flex-direction: column;
        gap: 16px;

        .header-stats {
          justify-content: center;
        }
      }

      .detail-section {
        .image-gallery {
          justify-content: center;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .news-detail-dialog {
    .news-detail {
      .detail-header {
        .header-main {
          .news-title {
            font-size: 16px;
          }

          .header-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }

        .header-stats {
          .stat-item {
            padding: 8px 12px;
            min-width: 50px;

            .stat-number {
              font-size: 16px;
            }
          }
        }
      }

      .detail-section {
        .thumbnail-container {

          .thumbnail-wrapper,
          .no-image-placeholder {
            width: 100px;
            height: 60px;
          }
        }

        .image-gallery {
          .gallery-item {
            width: 80px;
            height: 60px;
          }
        }
      }
    }
  }
}

.news-detail-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    animation: slideDown 0.3s ease-out;

    .el-dialog__title {
      color: white;
      font-weight: bold;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
      transition: all 0.3s ease;

      &:hover {
        transform: rotate(90deg);
        color: #ffd04b;
      }
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
    animation: fadeIn 0.4s ease-out;
  }

  .news-detail {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      padding: 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      animation: slideInUp 0.5s ease-out;

      .header-main {
        flex: 1;

        .news-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 12px;
          color: #2c3e50;
          line-height: 1.4;
          animation: fadeInLeft 0.6s ease-out;
        }

        .header-meta {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          animation: fadeInLeft 0.7s ease-out;

          .meta-item {
            display: flex;
            align-items: center;
            color: #606266;
            font-size: 13px;
            background: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 1);
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            i {
              margin-right: 4px;
              color: #409eff;
            }
          }
        }
      }

      .header-stats {
        display: flex;
        gap: 16px;
        animation: fadeInRight 0.6s ease-out;

        .stat-item {
          text-align: center;
          background: rgba(255, 255, 255, 0.9);
          padding: 12px 16px;
          border-radius: 6px;
          min-width: 60px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 4px;
          }

          .stat-label {
            color: #606266;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }

    .detail-section {
      margin-bottom: 24px;
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8eaec;
      animation: slideInUp 0.5s ease-out;
      animation-delay: calc(var(--section-index, 0) * 0.1s);

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        color: #2c3e50;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
          font-size: 18px;
        }
      }

      .thumbnail-container {
        .thumbnail-wrapper {
          position: relative;
          display: inline-block;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .thumbnail-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(64, 158, 255, 0.8) 0%,
                rgba(102, 126, 234, 0.8) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            i {
              color: white;
              font-size: 24px;
              transform: scale(0.8);
              ;
              transition: transform 0.3s ease;
            }
          }

          &:hover {
            .thumbnail-overlay {
              opacity: 1;

              i {
                transform: scale(1);
              }
            }

            img {
              transform: scale(1.1);
            }
          }
        }

        .no-image-placeholder {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          color: #909399;
          font-size: 12px;
          border: 2px dashed #d9d9d9;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          }

          i {
            margin-bottom: 4px;
            font-size: 24px;
            color: #c0c4cc;
            transition: color 0.3s ease;
          }

          &:hover i {
            color: #409eff;
          }
        }
      }

      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .gallery-item {
          position: relative;
          width: 100px;
          height: 70px;
          border-radius: 6px;
          overflow: hidden;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          ;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(64, 158, 255, 0.8) 0%,
                rgba(102, 126, 234, 0.8) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            i {
              color: white;
              font-size: 20px;
              transform: scale(0.8);
              ;
              transition: transform 0.3s ease;
            }
          }

          &:hover {
            .gallery-overlay {
              opacity: 1;

              i {
                transform: scale(1);
              }
            }

            img {
              transform: scale(1.1);
            }
          }
        }
      }

      .content-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 16px;
        border: 1px solid #e8eaec;
        border-radius: 6px;
        background: #fafbfc;
        line-height: 1.6;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        ::v-deep {
          img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 8px 0;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.02);
            }
          }

          p {
            margin: 8px 0;
            color: #2c3e50;
          }

          h1,
          h2,
          h3,
          h4,
          h5,
          h6 {
            color: #2c3e50;
            margin: 16px 0 8px 0;
          }

          a {
            color: #409eff;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
              color: #66b1ff;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

// 动画关键帧
@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #c0c4cc;
  }

  h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #606266;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}
</style>