<template>
  <el-dialog :title="title" :visible="open" @close="handleClose" width="1000px" append-to-body>
    <el-form ref="form" :model="form" :rules="getRules()" label-width="80px">
      <el-form-item :label="t('table.articleTitle')" prop="title">
        <el-input v-model="form.title" :placeholder="t('searchForm.placeholder.title')" />
      </el-form-item>
      <el-form-item :label="t('table.thumbnail')" prop="thumbnail">
        <image-cover-upload v-model="form.thumbnail" :limit="1" />
      </el-form-item>
      <el-form-item :label="t('dialog.articleImages')" prop="imgs">
        <!-- <file-upload v-model="form.imgs" :limit="9" :file-type="['jpg', 'jpeg', 'png', 'gif']" :file-size="5" /> -->
        <image-cover-upload v-model="form.imgs" :limit="9" />
      </el-form-item>
      <el-form-item :label="t('dialog.articleContent')" prop="content">
        <editor v-model="form.content" :min-height="300" v-if="open" />
      </el-form-item>
      <el-form-item :label="t('table.status')" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="0">{{
            t("searchForm.statusOptions.enabled")
            }}</el-radio>
          <el-radio :label="1">{{
            t("searchForm.statusOptions.disabled")
            }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">{{
        t("dialog.confirm")
        }}</el-button>
      <el-button @click="cancel">{{ t("dialog.cancel") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import FileUpload from "@/components/FileUpload";
import Editor from "@/components/Editor";
import ImageCoverUpload from "@/components/ImageCoverUpload";
import { createI18nManager } from "../lang";

export default {
  name: "NewsEditDialog",
  components: {
    FileUpload,
    Editor,
    ImageCoverUpload,
  },
  props: {
    open: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    form: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      i18nManager: null,
    };
  },
  created() {
    this.i18nManager = createI18nManager(this.$i18n);
  },
  methods: {
    t(key, args) {
      if (!this.i18nManager) {
        return key;
      }
      return this.i18nManager.getText(key, args);
    },
    getRules() {
      return {
        title: [
          {
            required: true,
            message: this.t("validation.titleRequired"),
            trigger: "blur",
          },
        ],
        content: [
          {
            required: true,
            message: this.t("validation.contentRequired"),
            trigger: "change",
          },
        ],
        thumbnail: [
          {
            required: true,
            message: this.t("validation.thumbnailRequired"),
            trigger: "change",
          },
        ],
      };
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        }
      });
    },
    cancel() {
      this.$emit("cancel");
    },
    handleClose() {
      this.$emit("update:open", false);
    },
  },
};
</script>
