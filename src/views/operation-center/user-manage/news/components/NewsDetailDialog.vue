<template>
  <el-dialog :title="t('dialog.newsDetail')" :visible.sync="viewOpen" width="900px" append-to-body
    class="news-detail-dialog">
    <div v-loading="detailLoading" class="news-detail" v-if="viewData">
      <!-- 头部信息卡片 -->
      <div class="detail-header">
        <div class="header-main">
          <h2 class="news-title">{{ viewData.title }}</h2>
          <div class="header-meta">
            <el-tag :type="viewData.status === 0 ? 'success' : 'danger'" size="small">
              {{
                viewData.status === 0
                  ? t("searchForm.statusOptions.enabled")
                  : t("searchForm.statusOptions.disabled")
              }}
            </el-tag>
            <span class="meta-item">
              <i class="el-icon-user"></i>
              {{ viewData.createBy || t("dialog.unknown") }}
            </span>
            <span class="meta-item">
              <i class="el-icon-time"></i>
              {{ parseTime(viewData.createTime) }}
            </span>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ viewData.likeCount || 0 }}</div>
            <div class="stat-label">{{ t("dialog.like") }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ viewData.commentCount || 0 }}</div>
            <div class="stat-label">{{ t("dialog.comment") }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">
              {{ getImageList(viewData.imgs).length }}
            </div>
            <div class="stat-label">{{ t("dialog.image") }}</div>
          </div>
        </div>
      </div>

      <!-- 缩略图展示 -->
      <div class="detail-section" style="--section-index: 1;">
        <h3 class="section-title">
          <i class="el-icon-picture"></i>
          {{ t("table.thumbnail") }}
        </h3>
        <div class="thumbnail-container">
          <div v-if="viewData.thumbnail" class="thumbnail-wrapper" @click="previewImage(viewData.thumbnail)">
            <img :src="viewData.thumbnail" class="detail-thumbnail" @error="handleImageError" />
            <div class="thumbnail-overlay">
              <i class="el-icon-zoom-in"></i>
            </div>
          </div>
          <div v-else class="no-image-placeholder">
            <i class="el-icon-picture-outline"></i>
            <span>{{ t("dialog.noThumbnail") }}</span>
          </div>
        </div>
      </div>

      <!-- 文章图片展示 -->
      <div class="detail-section" v-if="getImageList(viewData.imgs).length > 0" style="--section-index: 2;">
        <h3 class="section-title">
          <i class="el-icon-picture-outline"></i>
          {{ t("dialog.articleImages") }} ({{
            getImageList(viewData.imgs).length
          }}{{ t("dialog.images") }})
        </h3>
        <div class="image-gallery">
          <div v-for="(img, index) in getImageList(viewData.imgs)" :key="index" class="gallery-item"
            @click="previewImage(img)">
            <img :src="img" class="gallery-image" @error="handleImageError" />
            <div class="gallery-overlay">
              <i class="el-icon-zoom-in"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 文章内容展示 -->
      <div class="detail-section" style="--section-index: 3;">
        <h3 class="section-title">
          <i class="el-icon-document"></i>
          {{ t("dialog.articleContent") }}
        </h3>
        <div class="content-container" v-html="viewData.content"></div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!detailLoading" class="empty-state">
      <i class="el-icon-document"></i>
      <h3>{{ t("dialog.noData") }}</h3>
      <p>{{ t("dialog.failedToGetNewsDetail") }}</p>
    </div>
  </el-dialog>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import { createI18nManager } from "../lang";

export default {
  name: "NewsDetailDialog",
  props: {
    viewOpen: {
      type: Boolean,
      required: true,
    },
    viewData: {
      type: Object,
      default: null,
    },
    detailLoading: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      i18nManager: null,
    };
  },
  created() {
    this.i18nManager = createI18nManager(this.$i18n);
  },
  methods: {
    t(key, args) {
      return this.i18nManager.getText(key, args);
    },
    parseTime(time) {
      return parseTime(time);
    },
    getImageList(imgs) {
      if (!imgs) return [];
      return imgs.split(",").filter((img) => img.trim());
    },
    previewImage(url) {
      this.$alert(
        `<img src="${url}" style="max-width: 100%; height: auto;" />`,
        this.t("dialog.imagePreview"),
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.t("dialog.close"),
          customClass: "image-preview-dialog",
        }
      );
    },
    handleImageError() {
      console.error(this.t("dialog.imageLoadFailed"));
    },
  },
};
</script>

<style lang="scss" scoped>
.news-detail-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: white;
      font-weight: bold;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;

      &:hover {
        color: #ffd04b;
      }
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .news-detail {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 24px;
      padding: 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .header-main {
        flex: 1;

        .news-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 12px;
          color: #2c3e50;
          line-height: 1.4;
        }

        .header-meta {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;

          .meta-item {
            display: flex;
            align-items: center;
            color: #606266;
            font-size: 13px;
            background: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            border-radius: 4px;

            &:hover {
              background: rgba(255, 255, 255, 1);
            }

            i {
              margin-right: 4px;
              color: #409eff;
            }
          }
        }
      }

      .header-stats {
        display: flex;
        gap: 16px;

        .stat-item {
          text-align: center;
          background: rgba(255, 255, 255, 0.9);
          padding: 12px 16px;
          border-radius: 6px;
          min-width: 60px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 4px;
          }

          .stat-label {
            color: #606266;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }

    .detail-section {
      margin-bottom: 24px;
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8eaec;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        color: #2c3e50;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
          font-size: 18px;
        }
      }

      .thumbnail-container {
        .thumbnail-wrapper {
          position: relative;
          display: inline-block;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .thumbnail-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(64, 158, 255, 0.8) 0%,
                rgba(102, 126, 234, 0.8) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;

            i {
              color: white;
              font-size: 24px;
            }
          }

          &:hover {
            .thumbnail-overlay {
              opacity: 1;
            }
          }
        }

        .no-image-placeholder {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 120px;
          height: 80px;
          border-radius: 6px;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          color: #909399;
          font-size: 12px;
          border: 2px dashed #d9d9d9;

          &:hover {
            border-color: #409eff;
            background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          }

          i {
            margin-bottom: 4px;
            font-size: 24px;
            color: #c0c4cc;
          }

          &:hover i {
            color: #409eff;
          }
        }
      }

      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .gallery-item {
          position: relative;
          width: 100px;
          height: 70px;
          border-radius: 6px;
          overflow: hidden;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(64, 158, 255, 0.8) 0%,
                rgba(102, 126, 234, 0.8) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;

            i {
              color: white;
              font-size: 20px;
            }
          }

          &:hover {
            .gallery-overlay {
              opacity: 1;
            }
          }
        }
      }

      .content-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 16px;
        border: 1px solid #e8eaec;
        border-radius: 6px;
        background: #fafbfc;
        line-height: 1.6;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        ::v-deep {
          img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 8px 0;
          }

          p {
            margin: 8px 0;
            color: #2c3e50;
          }

          h1,
          h2,
          h3,
          h4,
          h5,
          h6 {
            color: #2c3e50;
            margin: 16px 0 8px 0;
          }

          a {
            color: #409eff;
            text-decoration: none;

            &:hover {
              color: #66b1ff;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}


// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #c0c4cc;
  }

  h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #606266;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}
</style>
