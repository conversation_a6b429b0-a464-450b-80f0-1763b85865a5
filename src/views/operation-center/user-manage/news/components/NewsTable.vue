<template>
  <el-table v-loading="loading" :data="newsList" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column :label="$t('system.notice.noticeId')" align="center" prop="id" width="80">
      <template slot-scope="scope">
        <span style="color: #999;">{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column :label="t('table.thumbnail')" align="center" prop="thumbnail" width="100">
      <template slot-scope="scope">
        <preview-img v-if="scope.row.thumbnail" :imgUrl="scope.row.thumbnail" width="60px" height="40px" />
        <span v-else class="no-image">{{ t("table.noImage") }}</span>
      </template>
    </el-table-column>
    <el-table-column :label="t('table.articleTitle')" align="center" prop="title" :show-overflow-tooltip="true" />
    <el-table-column :label="t('table.imageCount')" align="center" width="100">
      <template slot-scope="scope">
        <span>{{ getImageCount(scope.row.imgs) }}</span>
      </template>
    </el-table-column>
    <el-table-column :label="t('table.likeCount')" align="center" prop="likeCount" width="100" />
    <el-table-column :label="t('table.commentCount')" align="center" prop="commentCount" width="100" />
    <el-table-column :label="t('table.status')" align="center" prop="status" width="100">
      <template slot-scope="scope">
        <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
          @change="handleStatusChange(scope.row)" />
      </template>
    </el-table-column>
    <el-table-column :label="t('table.creator')" align="center" prop="createBy" width="120" />
    <el-table-column :label="t('table.createTime')" align="center" prop="createTime" width="180">
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.createTime) }}</span>
      </template>
    </el-table-column>
    <el-table-column :label="t('table.operations')" align="center" class-name="small-padding fixed-width" width="220">
      <template slot-scope="scope">
        <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">{{ t("table.view")
        }}</el-button>
        <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{ t("table.edit")
        }}</el-button>
        <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{ t("table.delete")
        }}</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";
import { createI18nManager } from "../lang";

export default {
  name: "NewsTable",
  props: {
    newsList: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      required: true,
    },
    queryParams: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      i18nManager: null,
    };
  },
  created() {
    this.i18nManager = createI18nManager(this.$i18n);
  },
  methods: {
    t(key, args) {
      return this.i18nManager.getText(key, args);
    },
    parseTime(time) {
      return parseTime(time);
    },
    getImageCount(imgs) {
      if (!imgs) return 0;
      return imgs.split(",").filter((img) => img.trim()).length;
    },
    handleSelectionChange(selection) {
      this.$emit("selection-change", selection);
    },
    handleStatusChange(row) {
      this.$emit("status-change", row);
    },
    handleView(row) {
      this.$emit("view", row);
    },
    handleUpdate(row) {
      this.$emit("update", row);
    },
    handleDelete(row) {
      this.$emit("delete", row);
    },
  },
};
</script>

<style scoped>
.no-image {
  color: #909399;
  font-size: 12px;
}
</style>
