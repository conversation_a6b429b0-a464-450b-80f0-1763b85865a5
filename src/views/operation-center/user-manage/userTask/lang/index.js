import zhLang from './zh'
import enLang from './en'

// 创建国际化管理器
export function createI18nManager(i18n) {
  const languages = {
    zh: zhLang,
    en: enLang
  }
  
  return {
    getText(key, args = {}) {
      const currentLang = i18n.locale || 'zh'
      const langData = languages[currentLang] || languages.zh
      
      // 支持嵌套键值访问，如 'form.name'
      const keys = key.split('.')
      let result = langData
      
      for (const k of keys) {
        if (result && typeof result === 'object' && k in result) {
          result = result[k]
        } else {
          result = key // 如果找不到翻译，返回原始key
          break
        }
      }
      
      // 如果result是字符串且包含参数占位符，进行参数替换
      if (typeof result === 'string' && Object.keys(args).length > 0) {
        return result.replace(/\{(\w+)\}/g, (match, param) => args[param] || match)
      }
      
      return result
    }
  }
}