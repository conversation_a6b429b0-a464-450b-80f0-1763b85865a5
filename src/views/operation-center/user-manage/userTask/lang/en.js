export default {
  title: 'User Task Management',
  description: 'Manage user tasks creation, editing, status control and reward settings',
  mainContent: 'Task List',
  
  // Search area
  search: {
    title: 'Search Conditions',
    keyword: 'Keyword',
    keywordPlaceholder: 'Enter task name or code',
    name: 'Task Name',
    namePlaceholder: 'Enter task name',
    enName: 'English Name',
    enNamePlaceholder: 'Enter english name',
    taskCode: 'Task Code',
    taskCodePlaceholder: 'Enter task code',
    status: 'Status',
    type: 'Task Type',
    typePlaceholder: 'Select task type',
    model: 'Function Module'
  },
  
  // Table columns
  table: {
    title: 'Task List',
    id: 'ID',
    name: 'Task Name',
    enName: 'English Name',
    taskCode: 'Task Code',
    description: 'Description',
    coins: 'Coins',
    point: 'Points',
    maxCoins: 'Max Coins',
    maxPoint: 'Max Points',
    condition: 'Condition',
    cycle: 'Cycle',
    times: 'Times',
    sort: 'Sort',
    status: 'Status',
    type: 'Task Type',
    model: 'Function Module',
    createTime: 'Create Time',
    actions: 'Actions'
  },
  
  // Action buttons
  actions: {
    add: 'Add Task',
    edit: 'Edit',
    delete: 'Delete',
    enable: 'Enable',
    disable: 'Disable',
    search: 'Search',
    reset: 'Reset',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    confirmDelete: 'Are you sure to delete this task?',
    confirmEnable: 'Are you sure to enable this task?',
    confirmDisable: 'Are you sure to disable this task?'
  },
  
  // Form
  form: {
    addTitle: 'Add Task',
    editTitle: 'Edit Task',
    basicInfo: 'Basic Information',
    rewardInfo: 'Reward Information',
    advancedInfo: 'Advanced Settings',
    name: 'Task Name',
    enName: 'English Name',
    taskCode: 'Task Code',
    description: 'Description',
    enDescription: 'English Description',
    coins: 'Coin Reward',
    point: 'Point Reward',
    maxCoins: 'Max Coins',
    maxPoint: 'Max Points',
    condition: 'Condition Value',
    cycle: 'Task Cycle (Days)',
    times: 'Completion Times Limit',
    sort: 'Sort Order',
    status: 'Status',
    type: 'Task Type',
    model: 'Function Module',
    
    // Placeholders
    namePlaceholder: 'Enter task name',
    enNamePlaceholder: 'Enter english name',
    taskCodePlaceholder: 'Enter task code',
    descriptionPlaceholder: 'Enter task description',
    enDescriptionPlaceholder: 'Enter english description'
  },
  
  // Enum values
  enums: {
    status: {
      0: 'Enabled',
      1: 'Disabled'
    },
    type: {
      0: 'Basic Task',
      1: 'Advanced Task'
    },
    model: {
      0: 'Riding',
      1: 'Community',
      2: 'Photography',
      3: 'Guide',
      4: 'Team',
      5: 'Mall',
      6: 'Exercise Plan',
      7: 'Profile',
      8: 'Vehicle Status',
      9: 'Cycling Circle',
      10: 'Official'
    },
    completeStatus: {
      0: 'Incomplete',
      1: 'Completed'
    }
  },
  
  // Validation messages
  validation: {
    nameRequired: 'Please enter task name',
    enNameRequired: 'Please enter english name',
    taskCodeRequired: 'Please enter task code',
    descriptionRequired: 'Please enter task description',
    coinsRequired: 'Please enter coin reward',
    pointRequired: 'Please enter point reward',
    conditionRequired: 'Please enter condition value',
    sortRequired: 'Please enter sort value',
    modelRequired: 'Please select function module',
    typeRequired: 'Please select task type',
    statusRequired: 'Please select status',
    coinsInvalid: 'Coin reward must be greater than or equal to 0',
    pointInvalid: 'Point reward must be greater than or equal to 0',
    conditionInvalid: 'Condition value must be greater than or equal to 0',
    sortInvalid: 'Sort value must be greater than or equal to 0',
    timesInvalid: 'Completion times must be greater than or equal to 0',
    cycleInvalid: 'Task cycle must be greater than 0'
  },
  
  // Messages
  messages: {
    addSuccess: 'Task created successfully',
    updateSuccess: 'Task updated successfully',
    enableSuccess: 'Task enabled successfully',
    disableSuccess: 'Task disabled successfully',
    deleteSuccess: 'Task deleted successfully',
    loadError: 'Failed to load data',
    saveError: 'Failed to save',
    operationError: 'Operation failed'
  }
}