export default {
  title: '用户任务管理',
  description: '管理用户任务的创建、编辑、状态控制和奖励设置',
  mainContent: '任务列表',
  
  // 搜索区域
  search: {
    title: '搜索条件',
    keyword: '关键词',
    keywordPlaceholder: '请输入任务名称或编码',
    name: '任务名称',
    namePlaceholder: '请输入任务名称',
    enName: '英文名称',
    enNamePlaceholder: '请输入英文名称',
    taskCode: '任务编码',
    taskCodePlaceholder: '请输入任务编码',
    status: '状态',
    type: '任务类型',
    typePlaceholder: '请选择任务类型',
    model: '功能模块'
  },
  
  // 表格列
  table: {
    title: '任务列表',
    id: 'ID',
    name: '任务名称',
    enName: '英文名称',
    taskCode: '任务编码',
    description: '任务描述',
    coins: '金币',
    point: '积分',
    maxCoins: '最大金币',
    maxPoint: '最大积分',
    condition: '条件值',
    cycle: '任务周期',
    times: '完成次数',
    sort: '排序',
    status: '状态',
    type: '任务类型',
    model: '功能模块',
    createTime: '创建时间',
    actions: '操作'
  },
  
  // 操作按钮
  actions: {
    add: '新增任务',
    edit: '编辑',
    delete: '删除',
    enable: '启用',
    disable: '禁用',
    search: '搜索',
    reset: '重置',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    confirmDelete: '确认删除该任务吗？',
    confirmEnable: '确认启用该任务吗？',
    confirmDisable: '确认禁用该任务吗？'
  },
  
  // 表单
  form: {
    addTitle: '新增任务',
    editTitle: '编辑任务',
    basicInfo: '基础信息',
    rewardInfo: '奖励信息',
    advancedInfo: '高级设置',
    name: '任务名称',
    enName: '英文名称',
    taskCode: '任务编码',
    description: '任务描述',
    enDescription: '英文描述',
    coins: '金币奖励',
    point: '积分奖励',
    maxCoins: '最大金币',
    maxPoint: '最大积分',
    condition: '条件值',
    cycle: '任务周期（天）',
    times: '完成次数限制',
    sort: '排序',
    status: '状态',
    type: '任务类型',
    model: '功能模块',
    
    // 占位符
    namePlaceholder: '请输入任务名称',
    enNamePlaceholder: '请输入英文名称',
    taskCodePlaceholder: '请输入任务编码',
    descriptionPlaceholder: '请输入任务描述',
    enDescriptionPlaceholder: '请输入英文描述'
  },
  
  // 枚举值
  enums: {
    status: {
      0: '启用',
      1: '禁用'
    },
    type: {
      0: '基础任务',
      1: '进阶任务'
    },
    model: {
      0: '骑行',
      1: '社区',
      2: '摄影',
      3: '攻略',
      4: '组队',
      5: '商场',
      6: '运动计划',
      7: '个人页面',
      8: '车况',
      9: '车友圈',
      10: '官方'
    },
    completeStatus: {
      0: '未完成',
      1: '已完成'
    }
  },
  
  // 验证消息
  validation: {
    nameRequired: '请输入任务名称',
    enNameRequired: '请输入英文名称',
    taskCodeRequired: '请输入任务编码',
    descriptionRequired: '请输入任务描述',
    coinsRequired: '请输入金币奖励',
    pointRequired: '请输入积分奖励',
    conditionRequired: '请输入条件值',
    sortRequired: '请输入排序值',
    modelRequired: '请选择功能模块',
    typeRequired: '请选择任务类型',
    statusRequired: '请选择状态',
    coinsInvalid: '金币奖励必须大于等于0',
    pointInvalid: '积分奖励必须大于等于0',
    conditionInvalid: '条件值必须大于等于0',
    sortInvalid: '排序值必须大于等于0',
    timesInvalid: '完成次数必须大于等于0',
    cycleInvalid: '任务周期必须大于0'
  },
  
  // 消息提示
  messages: {
    addSuccess: '任务创建成功',
    updateSuccess: '任务更新成功',
    enableSuccess: '任务启用成功',
    disableSuccess: '任务禁用成功',
    deleteSuccess: '任务删除成功',
    loadError: '数据加载失败',
    saveError: '保存失败',
    operationError: '操作失败'
  }
}