<template>
  <div class="app-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 操作按钮 -->
      <div class="toolbar">
        <!-- 搜索表单 -->
        <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
          <el-form-item :label="t('search.name')">
            <el-input v-model="searchForm.name" :placeholder="t('search.namePlaceholder')" clearable
              style="width: 200px" />
          </el-form-item>
          <el-form-item :label="t('search.enName')">
            <el-input v-model="searchForm.enName" :placeholder="t('search.enNamePlaceholder')" clearable
              style="width: 200px" />
          </el-form-item>
          <el-form-item :label="t('search.taskCode')">
            <el-input v-model="searchForm.taskCode" :placeholder="t('search.taskCodePlaceholder')" clearable
              style="width: 150px" />
          </el-form-item>
          <el-form-item :label="t('search.type')">
            <el-select v-model="searchForm.type" :placeholder="t('search.typePlaceholder')" clearable
              style="width: 120px">
              <el-option :label="t('enums.type.0')" :value="0" />
              <el-option :label="t('enums.type.1')" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              {{ t('actions.search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ t('actions.reset') }}
            </el-button>
          </el-form-item>

        </el-form>
        <div class="right-button">
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
            {{ t('actions.add') }}
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="tableData" style="width: 100%" :height="tableHeight()">
        <el-table-column :label="$t('base.carexception.customerName')" type="index" width="65" prop="customerName"
          align="center">
          <template slot-scope="scope">
            {{ (searchForm.pageNum - 1) * searchForm.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="t('table.name')" width="150" show-overflow-tooltip />
        <el-table-column prop="enName" :label="t('table.enName')" width="150" show-overflow-tooltip />
        <el-table-column prop="taskCode" :label="t('table.taskCode')" width="120" />
        <el-table-column prop="description" :label="t('table.description')" show-overflow-tooltip />
        <el-table-column prop="coins" :label="t('table.coins')" width="80" align="center" />
        <el-table-column prop="point" :label="t('table.point')" width="80" align="center" />
        <el-table-column prop="condition" :label="t('table.condition')" width="80" align="center" />
        <el-table-column prop="cycle" :label="t('table.cycle')" width="80" align="center" />

        <el-table-column prop="status" :label="t('table.status')" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
              {{ t(`enums.status.${scope.row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="t('table.type')" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 0 ? 'primary' : 'warning'">
              {{ t(`enums.type.${scope.row.type}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="model" :label="t('table.model')" width="100" align="center">
          <template slot-scope="scope">
            {{ t(`enums.model.${scope.row.model}`) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('table.actions')" width="140" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">
              {{ t('actions.edit') }}
            </el-button>
            <el-button v-if="scope.row.status === 1" size="mini" type="text" style="color: #67c23a"
              @click="handleStatusChange(scope.row, 0)">
              {{ t('actions.enable') }}
            </el-button>
            <el-button v-else size="mini" type="text" style="color: #e6a23c" @click="handleStatusChange(scope.row, 1)">
              {{ t('actions.disable') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" :page.sync="searchForm.pageNum" :limit.sync="searchForm.pageSize"
        @pagination="getList" />
    </div>

    <!-- 添加/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false">
      <el-form ref="taskForm" :model="taskForm" :rules="taskRules" label-width="140px">
        <!-- 基础信息 -->
        <div class="form-module">
          <div class="form-module-title">{{ t('form.basicInfo') }}</div>
          <div class="form-module-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.name')" prop="name">
                  <el-input v-model="taskForm.name" :placeholder="t('form.namePlaceholder')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.enName')" prop="enName">
                  <el-input v-model="taskForm.enName" :placeholder="t('form.enNamePlaceholder')" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.taskCode')" prop="taskCode">
                  <el-input v-model="taskForm.taskCode" :placeholder="t('form.taskCodePlaceholder')" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.model')" prop="model">
                  <el-select v-model="taskForm.model" :placeholder="t('form.model')" style="width: 100%">
                    <el-option v-for="(label, value) in t('enums.model')" :key="value" :label="label"
                      :value="parseInt(value)" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :label="t('form.description')" prop="description">
              <el-input v-model="taskForm.description" type="textarea" :rows="3"
                :placeholder="t('form.descriptionPlaceholder')" />
            </el-form-item>
            <el-form-item :label="t('form.enDescription')" prop="enDescription">
              <el-input v-model="taskForm.enDescription" type="textarea" :rows="3"
                :placeholder="t('form.enDescriptionPlaceholder')" />
            </el-form-item>
          </div>
        </div>

        <!-- 奖励信息 -->
        <div class="form-module">
          <div class="form-module-title">{{ t('form.rewardInfo') }}</div>
          <div class="form-module-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.coins')" prop="coins">
                  <el-input-number v-model="taskForm.coins" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.point')" prop="point">
                  <el-input-number v-model="taskForm.point" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.maxCoins')" prop="maxCoins">
                  <el-input-number v-model="taskForm.maxCoins" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.maxPoint')" prop="maxPoint">
                  <el-input-number v-model="taskForm.maxPoint" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 高级设置 -->
        <div class="form-module">
          <div class="form-module-title">{{ t('form.advancedInfo') }}</div>
          <div class="form-module-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.type')" prop="type">
                  <el-select v-model="taskForm.type" :placeholder="t('form.type')" style="width: 100%">
                    <el-option v-for="(label, value) in t('enums.type')" :key="value" :label="label"
                      :value="parseInt(value)" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.condition')" prop="condition">
                  <el-input-number v-model="taskForm.condition" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.cycle')" prop="cycle">
                  <el-input-number v-model="taskForm.cycle" :min="1" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('form.times')" prop="times">
                  <el-input-number v-model="taskForm.times" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('form.sort')" prop="sort">
                  <el-input-number v-model="taskForm.sort" :min="0" :precision="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :label="t('form.status')" prop="status">
              <el-radio-group v-model="taskForm.status">
                <el-radio v-for="(label, value) in t('enums.status')" :key="value" :label="parseInt(value)">
                  {{ label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ t('actions.cancel') }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ t('actions.save') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { createI18nManager } from './lang'
import * as taskApi from '@/api/task'
import { tableHeight } from '@/utils/ruoyi'

export default {
  name: "UserTask",
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      tableData: [],
      total: 0,
      searchForm: {
        name: '',
        enName: '',
        taskCode: '',
        status: '',
        type: undefined,
        pageNum: 1,
        pageSize: 20
      },
      dialogVisible: false,
      isEdit: false,
      taskForm: {
        id: '',
        name: '',
        enName: '',
        taskCode: '',
        description: '',
        enDescription: '',
        coins: 0,
        point: 0,
        maxCoins: 0,
        maxPoint: 0,
        condition: 0,
        cycle: 1,
        times: 0,
        sort: 0,
        status: 0,
        type: 0,
        model: 0,
        completeStatus: 0
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? this.t('form.editTitle') : this.t('form.addTitle')
    },
    taskRules() {
      return {
        name: [
          { required: true, message: this.t('validation.nameRequired'), trigger: 'blur' }
        ],
        enName: [
          { required: true, message: this.t('validation.enNameRequired'), trigger: 'blur' }
        ],
        taskCode: [
          { required: true, message: this.t('validation.taskCodeRequired'), trigger: 'blur' }
        ],
        description: [
          { required: true, message: this.t('validation.descriptionRequired'), trigger: 'blur' }
        ],
        coins: [
          { required: true, message: this.t('validation.coinsRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.coinsInvalid'), trigger: 'blur' }
        ],
        point: [
          { required: true, message: this.t('validation.pointRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.pointInvalid'), trigger: 'blur' }
        ],
        condition: [
          { required: true, message: this.t('validation.conditionRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.conditionInvalid'), trigger: 'blur' }
        ],
        cycle: [
          { type: 'number', min: 1, message: this.t('validation.cycleInvalid'), trigger: 'blur' }
        ],
        times: [
          { type: 'number', min: 0, message: this.t('validation.timesInvalid'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.t('validation.sortRequired'), trigger: 'blur' },
          { type: 'number', min: 0, message: this.t('validation.sortInvalid'), trigger: 'blur' }
        ],
        model: [
          { required: true, message: this.t('validation.modelRequired'), trigger: 'change' }
        ],
        type: [
          { required: true, message: this.t('validation.typeRequired'), trigger: 'change' }
        ],
        status: [
          { required: true, message: this.t('validation.statusRequired'), trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.getList()
  },
  methods: {
    // 简洁的国际化文本获取方法（支持参数）
    t(key, args) {
      return this.i18nManager.getText(key, args)
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = {
          p: this.searchForm.pageNum,
          l: this.searchForm.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.name) {
          params.name = this.searchForm.name
        }
        if (this.searchForm.enName) {
          params.enName = this.searchForm.enName
        }
        if (this.searchForm.taskCode) {
          params.taskCode = this.searchForm.taskCode
        }
        if (this.searchForm.type !== undefined) {
          params.type = this.searchForm.type
        }

        const response = await taskApi.getTaskList(params)
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
          this.tableData = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
        this.tableData = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.pageNum = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.$refs.searchForm.resetFields()
      this.searchForm = {
        name: '',
        enName: '',
        taskCode: '',
        status: '',
        type: undefined,
        pageNum: 1,
        pageSize: 10
      }
      this.getList()
    },

    // 添加
    handleAdd() {
      this.isEdit = false
      this.taskForm = {
        id: '',
        name: '',
        enName: '',
        taskCode: '',
        description: '',
        enDescription: '',
        coins: 0,
        point: 0,
        maxCoins: 0,
        maxPoint: 0,
        condition: 0,
        cycle: 1,
        times: 0,
        sort: 0,
        status: 0,
        type: 0,
        model: 0,
        completeStatus: 0
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.taskForm.clearValidate()
      })
    },

    // 编辑
    handleEdit(row) {
      this.isEdit = true
      this.taskForm = { ...row }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.taskForm.clearValidate()
      })
    },

    // 状态变更
    handleStatusChange(row, newStatus) {
      const action = newStatus === 0 ? 'confirmEnable' : 'confirmDisable'
      this.$confirm(
        this.t(`actions.${action}`),
        this.t('actions.confirm'),
        {
          confirmButtonText: this.t('actions.confirm'),
          cancelButtonText: this.t('actions.cancel'),
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await taskApi.updateTaskStatus({
            id: row.id,
            status: newStatus
          })
          if (response.code === 200) {
            const message = newStatus === 0 ? 'enableSuccess' : 'disableSuccess'
            this.$message.success(this.t(`messages.${message}`))
            this.getList()
          } else {
            this.$message.error(response.msg || this.t('messages.operationError'))
          }
        } catch (error) {
          console.error('状态更新失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }).catch(() => {
        // 取消操作
      })
    },

    // 提交表单
    submitForm() {
      this.$refs.taskForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const response = this.isEdit
              ? await taskApi.editTask(this.taskForm)
              : await taskApi.addTask(this.taskForm)

            if (response.code === 200) {
              const message = this.isEdit ? 'updateSuccess' : 'addSuccess'
              this.$message.success(this.t(`messages.${message}`))
              this.dialogVisible = false
              this.getList()
            } else {
              this.$message.error(response.msg || this.t('messages.saveError'))
            }
          } catch (error) {
            console.error('保存失败:', error)
            this.$message.error(this.t('messages.saveError'))
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.taskForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {}

.page-header {
  margin-bottom: 20px;
  padding: 20px 0;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #303133;
  }

  p {
    margin: 8px 0 0 0;
    color: #909399;
    font-size: 14px;
  }
}

.main-content {
  .el-card {
    border-radius: 4px;
    border: 1px solid #ebeef5;
  }
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

.toolbar {
  display: flex;
  align-items: self-start;
  justify-content: space-between;
  gap: 10px;

  .right-button {
    position: relative;
  }
}

// 表格样式
:deep(.el-table) {
  th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 500;
    text-align: center;
  }

  td {
    text-align: center;
  }
}

// 分页样式
:deep(.pagination-container) {
  padding: 20px 0 0 0;
  text-align: center;
}

// 弹窗表单模块样式 - 边框中间标题效果
.form-module {
  position: relative;
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #ffffff;

  .form-module-title {
    position: absolute;
    top: -12px;
    left: 20px;
    padding: 4px 12px;
    background-color: #ffffff;
    color: #303133;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
  }

  .form-module-content {
    padding: 20px;

    .el-row {
      margin-bottom: 18px;
    }
  }
}

// 弹窗样式
.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }

  .module-container {
    .module-content {
      padding: 16px;
    }
  }

  .search-form {
    :deep(.el-form-item) {
      display: block;
      margin-bottom: 16px;

      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }

  .toolbar {
    flex-direction: column;
    gap: 8px;

    .el-button {
      width: 100%;
    }
  }

  .form-module {
    .form-module-content {
      padding: 16px;
    }

    .el-row {
      .el-col {
        width: 100%;

        .el-form-item {
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
