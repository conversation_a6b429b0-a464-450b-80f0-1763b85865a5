<template>
  <div class="app-container">

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" type="card" class="identity-tabs">
      <!-- 身份认证类型管理 -->
      <el-tab-pane :label="t('tabs.identityTypes')" name="identityTypes">
        <div class="tab-content">
          <!-- 搜索表单 -->
          <el-form ref="identitySearchForm" :model="identitySearchForm" :inline="true" class="search-form">
            <el-form-item :label="t('identityTypes.search.name')" prop="name">
              <el-input v-model="identitySearchForm.name" :placeholder="t('identityTypes.search.namePlaceholder')"
                clearable style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleIdentitySearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetIdentitySearch">
                {{ t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 操作按钮 -->
          <div class="toolbar">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddIdentity">
              {{ t('identityTypes.actions.add') }}
            </el-button>
          </div>

          <!-- 身份认证类型表格 -->
          <el-table v-loading="identityLoading" :data="identityTableData" style="width: 100%"
            :height="tableHeight(-100)">
            <el-table-column prop="name" :label="t('identityTypes.table.name')" min-width="120" />
            <el-table-column prop="enName" :label="t('identityTypes.table.enName')" min-width="120" />
            <el-table-column prop="code" :label="t('identityTypes.table.code')" width="100" />
            <el-table-column prop="desc" :label="t('identityTypes.table.desc')" min-width="150" show-overflow-tooltip />
            <el-table-column prop="rule" :label="t('identityTypes.table.rule')" width="100" />
            <el-table-column prop="timeFrame" :label="t('identityTypes.table.timeFrame')" width="100">
              <template slot-scope="scope">
                {{ scope.row.timeFrame }}{{ t('identityTypes.table.months') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="t('identityTypes.table.status')" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                  {{ scope.row.status === 0 ? t('identityTypes.status.enabled') : t('identityTypes.status.disabled') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isAdmin" :label="t('identityTypes.table.isAdmin')" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isAdmin === 1 ? 'success' : 'info'">
                  {{ scope.row.isAdmin === 1 ? t('identityTypes.isAdmin.yes') : t('identityTypes.isAdmin.no') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="t('identityTypes.table.actions')" width="140">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="handleEditIdentity(scope.row)">
                  {{ t('identityTypes.actions.edit') }}
                </el-button>
                <el-button v-if="scope.row.status === 1" size="mini" type="text" style="color: #67C23A"
                  @click="handleToggleIdentityStatus(scope.row, 0)">
                  {{ t('identityTypes.actions.enable') }}
                </el-button>
                <el-button v-else size="mini" type="text" style="color: #E6A23C"
                  @click="handleToggleIdentityStatus(scope.row, 1)">
                  {{ t('identityTypes.actions.disable') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination v-show="identityTotal > 0" :total="identityTotal" :page.sync="identitySearchForm.p"
            :limit.sync="identitySearchForm.l" @pagination="getIdentityList" />
        </div>
      </el-tab-pane>

      <!-- 用户申请审核 -->
      <el-tab-pane :label="t('tabs.userApplications')" name="userApplications">
        <div class="tab-content">
          <!-- 搜索表单 -->
          <el-form ref="recordSearchForm" :model="recordSearchForm" :inline="true" class="search-form">
            <el-form-item :label="t('userApplications.search.name')" prop="name">
              <el-input v-model="recordSearchForm.name" :placeholder="t('userApplications.search.namePlaceholder')"
                clearable style="width: 150px" />
            </el-form-item>
            <el-form-item :label="t('userApplications.search.nickName')" prop="nickName">
              <el-input v-model="recordSearchForm.nickName"
                :placeholder="t('userApplications.search.nickNamePlaceholder')" clearable style="width: 150px" />
            </el-form-item>
            <el-form-item :label="t('userApplications.search.state')" prop="state">
              <el-select v-model="recordSearchForm.state" :placeholder="t('userApplications.search.statePlaceholder')"
                clearable style="width: 150px">
                <el-option :label="t('userApplications.status.pending')" value="0" />
                <el-option :label="t('userApplications.status.approved')" value="1" />
                <el-option :label="t('userApplications.status.rejected')" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleRecordSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetRecordSearch">
                {{ t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 用户申请表格 -->
          <el-table v-loading="recordLoading" :data="recordTableData" style="width: 100%">
            <el-table-column prop="identityName" :label="t('userApplications.table.identityName')" width="120" />
            <el-table-column prop="identityEnName" :label="t('userApplications.table.identityEnName')" width="140" />
            <el-table-column prop="nickName" :label="t('userApplications.table.nickName')" width="100" />
            <el-table-column prop="headImg" :label="t('userApplications.table.headImg')" width="80">
              <template slot-scope="scope">
                <preview-img v-if="scope.row.headImg" :imgUrl="scope.row.headImg" width="40px" height="40px"
                  radius="50%" fit="cover" />
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="desc" :label="t('userApplications.table.desc')" min-width="150"
              show-overflow-tooltip />
            <el-table-column prop="rule" :label="t('userApplications.table.rule')" width="80" align="center" />
            <el-table-column prop="timeFrame" :label="t('userApplications.table.timeFrame')" width="100" align="center">
              <template slot-scope="scope">
                {{ scope.row.timeFrame }}{{ t('userApplications.table.months') }}
              </template>
            </el-table-column>
            <el-table-column prop="times" :label="t('userApplications.table.times')" width="60" align="center" />
            <el-table-column :label="t('userApplications.table.stats')" width="100" align="center">
              <template slot-scope="scope">
                <div class="stats-info">
                  <div><i class="el-icon-view"></i>{{ scope.row.readNum }}k</div>
                  <div><i class="el-icon-star-off"></i>{{ scope.row.likeNum }}k</div>
                  <div><i class="el-icon-chat-dot-round"></i>{{ scope.row.commonNum }}k</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="state" :label="t('userApplications.table.state')" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStateTagType(scope.row.state)">
                  {{ getStateText(scope.row.state) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" :label="t('userApplications.table.createTime')" width="150">
              <template slot-scope="scope">
                {{ parseTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column :label="t('userApplications.table.actions')" width="150">
              <template slot-scope="scope">
                <el-button v-if="scope.row.state === 0" size="mini" type="text" style="color: #67C23A"
                  @click="handleApproveRecord(scope.row)">
                  {{ t('userApplications.actions.approve') }}
                </el-button>
                <el-button v-if="scope.row.state === 0" size="mini" type="text" style="color: #F56C6C"
                  @click="handleRejectRecord(scope.row)">
                  {{ t('userApplications.actions.reject') }}
                </el-button>
                <!-- <el-button v-if="scope.row.state !== 0" size="mini" type="text" @click="handleViewDetails(scope.row)">
                  {{ t('userApplications.actions.viewDetails') }}
                </el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination v-show="recordTotal > 0" :total="recordTotal" :page.sync="recordSearchForm.p"
            :limit.sync="recordSearchForm.l" @pagination="getRecordList" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 身份认证类型表单对话框 -->
    <el-dialog :title="identityDialogTitle" :visible.sync="identityDialogVisible" width="1000px"
      @close="resetIdentityForm">
      <el-form ref="identityForm" :model="identityForm" :rules="identityRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.name')" prop="name">
              <el-input v-model="identityForm.name" :placeholder="t('form.namePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.enName')" prop="enName">
              <el-input v-model="identityForm.enName" :placeholder="t('form.enNamePlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.code')" prop="code">
              <el-input v-model="identityForm.code" :placeholder="t('form.codePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.status')">
              <el-select v-model="identityForm.status" style="width: 100%">
                <el-option :label="t('form.statusOptions.enabled')" :value="0" />
                <el-option :label="t('form.statusOptions.disabled')" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label="t('form.desc')" prop="desc">
          <el-input v-model="identityForm.desc" :placeholder="t('form.descPlaceholder')" type="textarea" rows="3" />
        </el-form-item>

        <el-form-item :label="t('form.enDesc')" prop="enDesc">
          <el-input v-model="identityForm.enDesc" :placeholder="t('form.enDescPlaceholder')" type="textarea" rows="3" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="t('form.rule')" prop="rule">
              <el-input-number v-model="identityForm.rule" :min="0" style="width: 100%"
                :placeholder="t('form.rulePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('form.timeFrame')" prop="timeFrame">
              <el-input-number v-model="identityForm.timeFrame" :min="0" style="width: 100%"
                :placeholder="t('form.timeFramePlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('form.img')" prop="img">
              <image-cover-upload v-model="identityForm.img" :limit="1" :width="100" :height="100"
                @change="handleImageChange" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="t('form.readNum')" prop="readNum">
              <el-input-number v-model="identityForm.readNum" :min="0" style="width: 100%"
                :placeholder="t('form.readNumPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('form.likeNum')" prop="likeNum">
              <el-input-number v-model="identityForm.likeNum" :min="0" style="width: 100%"
                :placeholder="t('form.likeNumPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('form.commonNum')" prop="commonNum">
              <el-input-number v-model="identityForm.commonNum" :min="0" style="width: 100%"
                :placeholder="t('form.commonNumPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="identityDialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="submitIdentityForm">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 审核理由对话框 -->
    <el-dialog :title="t('userApplications.actions.enterReason')" :visible.sync="reasonDialogVisible" width="1000px">
      <el-form ref="reasonForm" :model="reasonForm" :rules="reasonRules" label-width="100px">
        <el-form-item :label="t('form.reason')" prop="reason">
          <el-input v-model="reasonForm.reason" :placeholder="t('form.reasonPlaceholder')" type="textarea" rows="4" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="reasonDialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="submitReasonForm">
          {{ t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ImageCoverUpload from '@/components/ImageCoverUpload'
import PreviewImg from '@/components/PreviewImg'
import { createI18nManager } from './lang'
import {
  getIdentityList,
  addIdentity,
  editIdentity,
  authIdentity,
  getIdentityRecordList,
  authRecord
} from '@/api/acc/identityAuth'

export default {
  name: "IdentityAuth",
  components: {
    Pagination,
    ImageCoverUpload,
    PreviewImg
  },
  data() {
    return {
      // 标签页
      activeTab: 'identityTypes',

      // 身份认证类型管理
      identityLoading: false,
      identityTableData: [],
      identityTotal: 0,
      identitySearchForm: {
        name: '',
        p: 1,
        l: 20
      },

      // 身份认证类型表单
      identityDialogVisible: false,
      identityDialogTitle: '',
      identityForm: {
        id: null,
        name: '',
        enName: '',
        code: '',
        status: 0,
        desc: '',
        enDesc: '',
        rule: 0,
        timeFrame: 0,
        img: '',
        readNum: 0,
        likeNum: 0,
        commonNum: 0
      },
      identityRules: {},

      // 用户申请审核
      recordLoading: false,
      recordTableData: [],
      recordTotal: 0,
      recordSearchForm: {
        name: '',
        nickName: '',
        state: '',
        p: 1,
        l: 20
      },

      // 审核理由表单
      reasonDialogVisible: false,
      reasonForm: {
        id: null,
        state: null,
        reason: ''
      },
      reasonRules: {}
    }
  },
  computed: {

  },
  created() {
    // 初始化国际化管理器
    this.i18nManager = createI18nManager(this.$i18n)
    this.initRules()
    this.getIdentityList()
  },
  methods: {
    // 简洁的国际化文本获取方法
    t(key) {
      return this.i18nManager.getText(key)
    },

    // 初始化表单验证规则
    initRules() {
      this.identityRules = {
        name: [
          { required: true, message: this.t('validation.nameRequired'), trigger: 'blur' }
        ],
        enName: [
          { required: true, message: this.t('validation.enNameRequired'), trigger: 'blur' }
        ],
        code: [
          { required: true, message: this.t('validation.codeRequired'), trigger: 'blur' }
        ],
        desc: [
          { required: true, message: this.t('validation.descRequired'), trigger: 'blur' }
        ]
      }

      this.reasonRules = {
        reason: [
          { required: true, message: this.t('validation.reasonRequired'), trigger: 'blur' }
        ]
      }
    },

    // =============== 身份认证类型管理 ===============

    // 获取身份认证类型列表
    async getIdentityList() {
      this.identityLoading = true
      try {
        const response = await getIdentityList({
          ...this.identitySearchForm,
          p: String(this.identitySearchForm.p),
          l: String(this.identitySearchForm.l)
        })

        if (response.code === 200) {
          this.identityTableData = response.data.list || []
          this.identityTotal = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取身份认证类型列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.identityLoading = false
      }
    },

    // 搜索身份认证类型
    handleIdentitySearch() {
      this.identitySearchForm.p = 1
      this.getIdentityList()
    },

    // 重置搜索
    resetIdentitySearch() {
      // 手动重置表单数据
      this.identitySearchForm = {
        name: '',
        p: 1,
        l: 20
      }
      // 清除表单验证
      this.$nextTick(() => {
        this.$refs.identitySearchForm && this.$refs.identitySearchForm.clearValidate()
      })
      this.getIdentityList()
    },

    // 添加身份认证类型
    handleAddIdentity() {
      this.identityDialogTitle = this.t('identityTypes.actions.add')
      this.identityDialogVisible = true
    },

    // 编辑身份认证类型
    handleEditIdentity(row) {
      this.identityDialogTitle = this.t('identityTypes.actions.edit')
      this.identityForm = { ...row }
      this.identityDialogVisible = true
    },

    // 切换身份认证类型状态
    async handleToggleIdentityStatus(row, newStatus) {
      const action = newStatus === 0 ? 'enable' : 'disable'
      const confirmMsg = newStatus === 0 ?
        this.t('identityTypes.actions.confirmEnable') :
        this.t('identityTypes.actions.confirmDisable')

      try {
        await this.$confirm(confirmMsg, this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const response = await authIdentity({
          id: row.id,
          status: newStatus
        })

        if (response.code === 200) {
          const successMsg = newStatus === 0 ?
            this.t('messages.enableSuccess') :
            this.t('messages.disableSuccess')
          this.$message.success(successMsg)
          this.getIdentityList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('切换状态失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 提交身份认证类型表单
    async submitIdentityForm() {
      try {
        await this.$refs.identityForm.validate()

        const isEdit = !!this.identityForm.id
        const apiMethod = isEdit ? editIdentity : addIdentity

        const response = await apiMethod(this.identityForm)

        if (response.code === 200) {
          const successMsg = isEdit ?
            this.t('messages.updateSuccess') :
            this.t('messages.addSuccess')
          this.$message.success(successMsg)
          this.identityDialogVisible = false
          this.getIdentityList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        console.error('提交表单失败:', error)
      }
    },

    // 重置身份认证类型表单
    resetIdentityForm() {
      this.identityForm = {
        id: null,
        name: '',
        enName: '',
        code: '',
        status: 0,
        desc: '',
        enDesc: '',
        rule: 0,
        timeFrame: 0,
        img: '',
        readNum: 0,
        likeNum: 0,
        commonNum: 0
      }
      this.$nextTick(() => {
        this.$refs.identityForm && this.$refs.identityForm.clearValidate()
      })
    },

    // =============== 用户申请审核 ===============

    // 获取用户申请列表
    async getRecordList() {
      this.recordLoading = true
      try {
        const response = await getIdentityRecordList({
          ...this.recordSearchForm,
          p: String(this.recordSearchForm.p),
          l: String(this.recordSearchForm.l)
        })

        if (response.code === 200) {
          this.recordTableData = response.data.list || []
          this.recordTotal = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取用户申请列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.recordLoading = false
      }
    },

    // 搜索用户申请
    handleRecordSearch() {
      this.recordSearchForm.p = 1
      this.getRecordList()
    },

    // 重置搜索
    resetRecordSearch() {
      // 手动重置表单数据
      this.recordSearchForm = {
        name: '',
        nickName: '',
        state: '',
        p: 1,
        l: 20
      }
      // 清除表单验证
      this.$nextTick(() => {
        this.$refs.recordSearchForm && this.$refs.recordSearchForm.clearValidate()
      })
      this.getRecordList()
    },

    // 通过申请
    async handleApproveRecord(row) {
      try {
        await this.$confirm(
          this.t('userApplications.actions.confirmApprove'),
          this.t('common.confirm'),
          {
            confirmButtonText: this.t('common.confirm'),
            cancelButtonText: this.t('common.cancel'),
            type: 'warning'
          }
        )

        const response = await authRecord({
          id: row.id,
          state: 1,
          reason: this.t('messages.approveReason')
        })

        if (response.code === 200) {
          this.$message.success(this.t('messages.approveSuccess'))
          this.getRecordList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('审核通过失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 拒绝申请
    handleRejectRecord(row) {
      this.reasonForm.id = row.id
      this.reasonForm.state = 2
      this.reasonForm.reason = ''
      this.reasonDialogVisible = true
    },

    // 提交审核理由
    async submitReasonForm() {
      try {
        await this.$refs.reasonForm.validate()

        const response = await authRecord({
          id: this.reasonForm.id,
          state: this.reasonForm.state,
          reason: this.reasonForm.reason
        })

        if (response.code === 200) {
          this.$message.success(this.t('messages.rejectSuccess'))
          this.reasonDialogVisible = false
          this.getRecordList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        console.error('提交审核理由失败:', error)
      }
    },

    // 查看详情
    handleViewDetails(row) {
      // TODO: 实现查看详情功能
      this.$message.info(this.t('messages.viewDetailsTodo'))
    },

    // 获取状态标签类型
    getStateTagType(state) {
      const typeMap = {
        0: 'warning', // 申请中
        1: 'success', // 审核通过
        2: 'danger'   // 审核失败
      }
      return typeMap[state] || 'info'
    },

    // 获取状态文本
    getStateText(state) {
      const textMap = {
        0: this.t('userApplications.status.pending'),
        1: this.t('userApplications.status.approved'),
        2: this.t('userApplications.status.rejected')
      }
      return textMap[state] || this.t('userApplications.status.unknown')
    },

    // 图片变化处理
    handleImageChange(urls) {
      console.log('认证图标更新:', urls)
      // ImageCoverUpload组件会自动更新v-model，这里可以做额外处理
    }
  },
  watch: {
    // 监听标签页切换，加载对应数据
    activeTab(newTab) {
      if (newTab === 'userApplications' && this.recordTableData.length === 0) {
        this.getRecordList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h3 {
    margin: 0;
    color: #303133;
    font-size: 20px;
  }

  p {
    margin: 5px 0 0 0;
    color: #606266;
    font-size: 14px;
  }
}

.identity-tabs {

  .section-header {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 5px 0;
      color: #303133;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 13px;
    }
  }
}

.search-form {
  margin-bottom: 20px;
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

.stats-info {
  font-size: 12px;
  line-height: 1.2;

  div {
    margin-bottom: 2px;

    i {
      margin-right: 2px;
      width: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      display: block;
      margin-bottom: 10px;

      .el-input,
      .el-select {
        width: 100% !important;
      }
    }
  }

  .el-table {
    font-size: 12px;
  }
}
</style>
