export default {
  title: '身份认证管理',
  description: '管理用户身份认证类型和审核用户申请',
  
  // 标签页
  tabs: {
    identityTypes: '身份认证类型',
    userApplications: '用户申请审核'
  },
  
  // 身份认证类型管理
  identityTypes: {
    title: '身份认证类型管理',
    description: '管理系统中的身份认证类型',
    search: {
      name: '认证名称',
      namePlaceholder: '请输入认证名称'
    },
    table: {
      id: 'ID',
      name: '认证名称',
      enName: '英文名称',
      code: '认证代码',
      desc: '描述',
      rule: '条件规则',
      timeFrame: '时间范围',
      months: '月',
      status: '状态',
      actions: '操作',
      isAdmin: '是否管理员'
    },
    status: {
      enabled: '已启用',
      disabled: '已禁用'
    },
    actions: {
      add: '添加认证类型',
      edit: '编辑',
      enable: '启用',
      disable: '禁用',
      confirmEnable: '确定要启用这个认证类型吗？',
      confirmDisable: '确定要禁用这个认证类型吗？'
    },
    isAdmin: {
      yes: '是',
      no: '否'
    }
  },
  
  // 用户申请审核
  userApplications: {
    title: '用户申请审核',
    description: '审核用户提交的身份认证申请',
    search: {
      name: '认证名称',
      namePlaceholder: '请输入认证名称',
      nickName: '用户昵称',
      nickNamePlaceholder: '请输入用户昵称',
      state: '审核状态',
      statePlaceholder: '请选择审核状态'
    },
    table: {
      identityName: '认证类型',
      identityEnName: '英文名称',
      nickName: '用户昵称',
      headImg: '用户头像',
      desc: '描述',
      rule: '条件规则',
      timeFrame: '时间范围',
      months: '月',
      times: '次数',
      stats: '统计数据',
      state: '审核状态',
      createTime: '申请时间',
      actions: '操作'
    },
    status: {
      pending: '申请中',
      approved: '审核通过',
      rejected: '审核失败',
      unknown: '未知'
    },
    actions: {
      approve: '通过',
      reject: '拒绝',
      viewDetails: '查看详情',
      confirmApprove: '确定要通过这个申请吗？',
      confirmReject: '确定要拒绝这个申请吗？',
      enterReason: '请输入审核理由'
    }
  },
  
  // 表单
  form: {
    name: '认证名称',
    namePlaceholder: '请输入认证名称',
    enName: '英文名称',
    enNamePlaceholder: '请输入英文名称',
    code: '认证代码',
    codePlaceholder: '请输入认证代码',
    desc: '描述',
    descPlaceholder: '请输入描述',
    enDesc: '英文描述',
    enDescPlaceholder: '请输入英文描述',
    rule: '条件规则',
    rulePlaceholder: '请输入条件规则（如多少miles、多少分钟等）',
    timeFrame: '时间范围',
    timeFramePlaceholder: '请输入时间范围（单位：月）',
    img: '认证图标',
    imgPlaceholder: '请输入图标URL',
    readNum: '阅读数量',
    readNumPlaceholder: '请输入阅读数量（单位：k）',
    likeNum: '点赞数量',
    likeNumPlaceholder: '请输入点赞数量',
    commonNum: '评论数量',
    commonNumPlaceholder: '请输入评论数量',
    status: '状态',
    statusOptions: {
      enabled: '启用',
      disabled: '禁用'
    },
    reason: '审核理由',
    reasonPlaceholder: '请输入审核理由（拒绝时必填）'
  },
  
  // 验证
  validation: {
    nameRequired: '认证名称不能为空',
    enNameRequired: '英文名称不能为空',
    codeRequired: '认证代码不能为空',
    descRequired: '描述不能为空',
    enDescRequired: '英文描述不能为空',
    ruleRequired: '条件规则不能为空',
    timeFrameRequired: '时间范围不能为空',
    imgRequired: '认证图标不能为空',
    readNumRequired: '阅读数量不能为空',
    likeNumRequired: '点赞数量不能为空',
    commonNumRequired: '评论数量不能为空',
    reasonRequired: '审核理由不能为空'
  },
  
  // 消息
  messages: {
    addSuccess: '添加成功',
    updateSuccess: '更新成功',
    enableSuccess: '启用成功',
    disableSuccess: '禁用成功',
    approveSuccess: '审核通过',
    rejectSuccess: '审核拒绝',
    loadError: '数据加载失败',
    operationError: '操作失败',
    approveReason: '审核通过',
    viewDetailsTodo: '查看详情功能待实现'
  },
  
  // 通用
  common: {
    search: '搜索',
    reset: '重置',
    add: '添加',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确定',
    loading: '加载中...',
    noData: '暂无数据',
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间'
  }
}
