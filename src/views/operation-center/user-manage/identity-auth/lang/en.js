export default {
  title: 'Identity Authentication Management',
  description: 'Manage user identity authentication types and review user applications',
  
  // Tabs
  tabs: {
    identityTypes: 'Identity Types',
    userApplications: 'User Applications'
  },
  
  // Identity Types Management
  identityTypes: {
    title: 'Identity Types Management',
    description: 'Manage identity authentication types in the system',
    search: {
      name: 'Authentication Name',
      namePlaceholder: 'Please enter authentication name'
    },
    table: {
      id: 'ID',
      name: 'Authentication Name',
      enName: 'English Name',
      code: 'Authentication Code',
      desc: 'Description',
      rule: 'Condition Rule',
      timeFrame: 'Time Frame',
      months: 'months',
      status: 'Status',
      actions: 'Actions',
      isAdmin: 'Is Admin'
    },
    status: {
      enabled: 'Enabled',
      disabled: 'Disabled'
    },
    actions: {
      add: 'Add Authentication Type',
      edit: 'Edit',
      enable: 'Enable',
      disable: 'Disable',
      confirmEnable: 'Are you sure to enable this authentication type?',
      confirmDisable: 'Are you sure to disable this authentication type?'
    },
    isAdmin: {
      yes: 'Yes',
      no: 'No'
    }
  },
  
  // User Applications Review
  userApplications: {
    title: 'User Applications Review',
    description: 'Review user submitted identity authentication applications',
    search: {
      name: 'Authentication Name',
      namePlaceholder: 'Please enter authentication name',
      nickName: 'User Nickname',
      nickNamePlaceholder: 'Please enter user nickname',
      state: 'Review Status',
      statePlaceholder: 'Please select review status'
    },
    table: {
      identityName: 'Authentication Type',
      identityEnName: 'English Name',
      nickName: 'User Nickname',
      headImg: 'User Avatar',
      desc: 'Description',
      rule: 'Condition Rule',
      timeFrame: 'Time Frame',
      months: 'months',
      times: 'Times',
      stats: 'Statistics',
      state: 'Review Status',
      createTime: 'Apply Time',
      actions: 'Actions'
    },
    status: {
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      unknown: 'Unknown'
    },
    actions: {
      approve: 'Approve',
      reject: 'Reject',
      viewDetails: 'View Details',
      confirmApprove: 'Are you sure to approve this application?',
      confirmReject: 'Are you sure to reject this application?',
      enterReason: 'Please enter review reason'
    }
  },
  
  // Form
  form: {
    name: 'Authentication Name',
    namePlaceholder: 'Please enter authentication name',
    enName: 'English Name',
    enNamePlaceholder: 'Please enter English name',
    code: 'Authentication Code',
    codePlaceholder: 'Please enter authentication code',
    desc: 'Description',
    descPlaceholder: 'Please enter description',
    enDesc: 'English Description',
    enDescPlaceholder: 'Please enter English description',
    rule: 'Condition Rule',
    rulePlaceholder: 'Please enter condition rule (e.g., miles, minutes)',
    timeFrame: 'Time Frame',
    timeFramePlaceholder: 'Please enter time frame (unit: months)',
    img: 'Authentication Icon',
    imgPlaceholder: 'Please enter icon URL',
    readNum: 'Read Count',
    readNumPlaceholder: 'Please enter read count (unit: k)',
    likeNum: 'Like Count',
    likeNumPlaceholder: 'Please enter like count',
    commonNum: 'Comment Count',
    commonNumPlaceholder: 'Please enter comment count',
    status: 'Status',
    statusOptions: {
      enabled: 'Enabled',
      disabled: 'Disabled'
    },
    reason: 'Review Reason',
    reasonPlaceholder: 'Please enter review reason (required for rejection)'
  },
  
  // Validation
  validation: {
    nameRequired: 'Authentication name is required',
    enNameRequired: 'English name is required',
    codeRequired: 'Authentication code is required',
    descRequired: 'Description is required',
    enDescRequired: 'English description is required',
    ruleRequired: 'Condition rule is required',
    timeFrameRequired: 'Time frame is required',
    imgRequired: 'Authentication icon is required',
    readNumRequired: 'Read count is required',
    likeNumRequired: 'Like count is required',
    commonNumRequired: 'Comment count is required',
    reasonRequired: 'Review reason is required'
  },
  
  // Messages
  messages: {
    addSuccess: 'Add successfully',
    updateSuccess: 'Update successfully',
    enableSuccess: 'Enable successfully',
    disableSuccess: 'Disable successfully',
    approveSuccess: 'Approve successfully',
    rejectSuccess: 'Reject successfully',
    loadError: 'Data load failed',
    operationError: 'Operation failed',
    approveReason: 'Approved',
    viewDetailsTodo: 'View details feature is not implemented yet'
  },
  
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time'
  }
}
