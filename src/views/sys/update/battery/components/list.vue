<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" :inline="true"></el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" v-if="checkRolesAdd('dev')" @click="handleAdd">
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" size="mini" v-debounce-click="() => handleAuth(0, aFn, getList)">
          {{ $t("system.computer.auth") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" v-debounce-click="() => handleAuth(1, aFn, getList)">
          {{ $t("system.computer.disabled") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-refresh" size="mini" v-debounce-click="handleQuery">
          {{ $t("system.computer.refresh") }}
        </el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="softUpdateList" height="500" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column type="index" width="65" align="center" :label="$t('base.carexception.customerName')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.carNameOrCode')" prop="bikeModelName" align="center">
      </el-table-column>
      <el-table-column :label="$t('system.app.versionNum')" prop="versionName" align="center" />
      <el-table-column :label="$t('system.app.versionSn')" prop="versionCode" align="center" />
      <el-table-column :label="$t('system.computer.desc')" prop="desc" align="center">
        <span slot-scope="scope">{{ scope.row.desc || "--" }}</span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.descEn')" prop="descEn" align="center">
        <span slot-scope="scope">{{ scope.row.descEn || "--" }}</span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.downLink')" prop="downLink" align="center">
        <template v-slot="{ row }">
          <el-image v-if="row.downLink" style="width: 30px; height: 30px; cursor: pointer"
            :src="require('@/assets/image/xiazai.png')" v-debounce-click="() => urlDownload(row.downLink)" />
        </template>
      </el-table-column>

      <el-table-column :label="$t('system.computer.status')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.auditStatus')" align="center" prop="state">
        <template slot-scope="scope">
          {{ setAuditStatus(scope.row.state) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createBy')" align="center" prop="createBy">
        <span slot-scope="scope">{{ scope.row.createBy || "--" }}</span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createTime')" align="center" prop="createTime">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.handle')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row, 1)">
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button v-if="auditAuth(scope.row)" size="mini" type="text"
            v-debounce-click="() => handleAudit(scope.row)">
            {{ $t("system.computer.audit") }}
          </el-button>
          <el-button v-if="reviewAuth(scope.row)" size="mini" type="text"
            v-debounce-click="() => handleAudit(scope.row)">
            {{ $t("system.computer.release") }}
          </el-button>
          <el-button size="mini" type="text" class="text-red" @click="handleDel(scope.row)">
            {{ $t("bike.info.del") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body center>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <!-- 车型名称 -->
        <el-form-item :label="$t('bike.bike.carNameOrCode')" prop="bikeModelName">
          <selectLoadMore v-model="form.bikeModelName" :data="modelData.data" :page="modelData.page"
            :hasMore="modelData.more" :request="getModelList" dictLabel="name" :moreParams="true"
            @getChange="getModelInfo" :placeholder="$t('form.select') + $t('bike.bike.carNameOrCode')"
            style="width:100%" />
        </el-form-item>
        <el-form-item :label="$t('system.app.versionNum')" prop="versionName">
          <el-input v-model.trim="form.versionName" clearable
            :placeholder="$t('form.input') + $t('system.app.versionNum')" />
        </el-form-item>
        <el-form-item :label="$t('system.app.versionSn')" prop="versionCode">
          <el-input oninput="value=value.replace(/[^\d.]/g,'')" v-model.trim="form.versionCode" clearable
            :placeholder="$t('form.input') + $t('system.app.versionSn')" />
        </el-form-item>
        <el-form-item :label="$t('system.computer.desc')" prop="desc">
          <el-input v-model.trim="form.desc" clearable :placeholder="$t('form.input') + $t('system.computer.desc')" />
        </el-form-item>
        <el-form-item :label="$t('system.computer.descEn')" prop="descEn">
          <el-input v-model.trim="form.descEn" clearable
            :placeholder="$t('form.input') + $t('system.computer.descEn')" />
        </el-form-item>
        <el-form-item :label="$t('system.computer.downLink2')" prop="downLink">
          <DrUpload v-model="form.downLink" :limit="1" :isOnePic="1" :css="{ width: '100%' }"
            class="flex-direction align-start">
            <el-button size="small" type="primary">
              {{ $t("queryParams.upload") }}
            </el-button>
          </DrUpload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  softListGroup,
  authBatteryUpdate,
  addBatteryUpdate,
  editBatteryUpdate,
  editBatteryDelete,
  changeState
} from "@/api/system/battery";
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { listModel } from "@/api/bike/model";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  props: ["content"],
  data() {
    const validateVersionName = (rule, value, callback) => {
      const reg = /^(\d+\.)(\d+\.)(\d+)$|^(\d+\.)(\d+)$|^(\d+)$/;
      if (value === "") {
        callback(new Error(this.$t("system.app.rules.versionNum")));
      } else if (!reg.test(value)) {
        callback(new Error(this.$t("mointerObj.correctVerionNumber")));
      } else {
        callback();
      }
    };
    return {
      aFn: authBatteryUpdate,
      // 车型
      modelNameList: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      // 接收版本选择数据
      row: {},
      isAdd: false,
      // 用户表格数据
      softUpdateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      type: null,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: 1
      },
      modelData: {
        data: [],
        page: 1,
        more: true
      },
      // 表单参数
      form: {
        id: undefined,
        type: 1,
        forceUpdate: "0",
        selectVal: [],
        modelId: "",
        brandId: ""
      },
      // 表单校验
      rules: {
        bikeModelName: [
          {
            required: true,
            message: this.$t("bike.info.modelId"),
            trigger: "change"
          }
        ],
        computerModel: [
          {
            required: false,
            message: this.$t("bike.model.computerIdNotNull"),
            trigger: "change"
          }
        ],
        versionName: [
          {
            required: true,
            validator: validateVersionName,
            trigger: ["blur", "change"]
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("system.app.versionSn"),
            trigger: "change"
          }
        ],
        desc: [
          {
            required: false,
            message: "",
            trigger: "change"
          }
        ],
        type: [
          {
            required: true,
            message:
              this.$t("system.computer.select") +
              this.$t("system.computer.stopwatch"),
            trigger: "blur"
          }
        ],
        mdCode: [
          {
            required: false,
            message: this.$t("system.computer.mdCode"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productModelList = [];
        this.productCodeList = [];
        this.form.productModel = "";
        this.form.code = "";
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.form.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productCodeList = [];
        this.form.code = "";
      }
    },
    /**
     * 审核状态回写
     */
    setAuditStatus(stata) {
      let maps = {
        0: this.$t("system.computer.toAudit"),
        1: this.$t("system.computer.toReview"),
        2: this.$t("system.computer.done")
      };
      return maps[stata];
    },
    /**
     * 审核
     */
    async handleAudit(row) {
      await changeState({
        id: row.id,
        state: Number(row.state) + 1 // 当前状态 + 1 到下个状态
      });
      this.getList();
    },
    /** 查询客户列表 */
    getList(row) {
      this.loading = true;
      if (row) {
        this.row = row;
      }
      row = row ? row : this.row;
      softListGroup({
        bikeModel: row.bikeModel
      }).then(response => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        type: 1,
        selectVal: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.isAdd = true;
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    // 获取车型
    getModelList({ page = 1, more = false, keyword = "" } = {}) {
      return new Promise(resolve => {
        listModel({
          p: page,
          modelKey: keyword
        }).then(res => {
          const { list, total, pageNum, pageSize } = res.data;
          if (more) {
            this.modelData.data = [...this.modelData.data, ...list];
          } else {
            this.modelData.data = list;
          }
          this.modelData.more = pageNum * pageSize < total;
          this.modelData.page = pageNum;
          resolve();
        });
      });
    },
    getModelInfo(info) {
      if (!info) {
        this.form.bikeModel = "";
        return;
      }
      const { id } = JSON.parse(info);
      this.form.bikeModel = id;
    },
    handleUpdate(row, type) {
      this.type = type;
      this.isAdd = false;
      this.form = Object.assign({}, row);
      this.open = true;
      this.title = this.$t("system.computer.handleUpdate");
    },
    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning")
      }).then(() => {
        this.loading = true;
        editBatteryDelete({
          id: row.id
        }).then(() => {
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
          this.loading = false;
          this.getList();
        });
      });
    },
    /**
     * 审核权限
     */
    auditAuth(row) {
      if (row.state === 0) {
        return this.checkRoles("test");
      }
      return false;
    },
    /**
     * 复核权限
     */
    reviewAuth(row) {
      if (row.state === 1) {
        return this.checkRoles("dev_manager");
      }
      return false;
    },
    /**
     * 新增权限判断
     */
    checkRolesAdd(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      return this.$store.getters.roles.includes(role);
    },
    /**
     * 角色判断
     * admin 默认有所有权限
     */
    checkRoles(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      let res = false;
      if (Array.isArray(role)) {
        for (var i = 0; i < role.length; i++) {
          let item = role[i];
          let flag = this.$store.getters.roles.includes(item);
          if (flag) {
            res = true;
            break;
          }
        }
      } else {
        res = this.$store.getters.roles.includes(role);
      }
      return res;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.selectVal && this.form.selectVal.length) {
            this.form.brandId = this.form.selectVal[0];
            this.form.modelId = this.form.selectVal[1];
          }
          let params = Object.assign({}, this.form);
          params.computerModel =
            params.computerModel == "default" ? "" : params.computerModel;
          if (!this.isAdd) {
            editBatteryUpdate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = true;
              });
          } else {
            addBatteryUpdate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = true;
              });
          }
        }
      });
    }
  }
};
</script>
