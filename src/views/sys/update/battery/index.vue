<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item :label="$t('bike.bike.carNameOrCode')" prop="bikeModel">
          <select-loadMore v-model="queryParams.bikeModel" :data="modelData.data" :page="modelData.page"
            :hasMore="modelData.more" :request="getModelList" dictLabel="name" dictValue="id"
            :placeholder="$t('form.select') + $t('bike.bike.carNameOrCode')" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
            {{ $t("discover.posted.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("discover.posted.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-if="checkRolesAdd('dev')" type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :height="tableHeight()" :data="softUpdateList">
      <el-table-column type="index" align="center" :label="$t('base.carexception.customerName')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.carNameOrCode')" prop="bikeModelName" align="center" />
      <el-table-column :label="$t('system.app.versionNum')" prop="versionName" align="center" />
      <el-table-column :label="$t('system.app.versionSn')" prop="versionCode" align="center" />
      <el-table-column :label="$t('system.computer.desc')" prop="desc" align="center">
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.descEn')" prop="descEn" align="center">
        <span slot-scope="scope" v-NoData="scope.row.descEn"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createBy')" align="center" prop="createBy" width="90">
        <span slot-scope="scope" v-NoData="scope.row.createBy"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createTime')" align="center" sortable prop="createTime">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.handle')" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleHistory(scope.row)">
            {{ $t("base.help.lookOver") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body center :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <!-- 车型名称 -->
        <el-form-item :label="$t('bike.info.modelId')" prop="bikeModelName">
          <select-loadMore v-model="form.bikeModelName" :data="modelData.data" :page="modelData.page"
            :hasMore="modelData.more" :request="getModelList" dictLabel="name" :moreParams="true"
            @getChange="getModelInfo" :placeholder="$t('form.select') + $t('bike.info.modelId')" style="width: 100%" />
        </el-form-item>
        <el-form-item :label="$t('system.app.versionNum')" prop="versionName">
          <el-input v-model.trim="form.versionName" clearable
            :placeholder="$t('form.input') + $t('system.app.versionNum')" />
        </el-form-item>
        <el-form-item :label="$t('system.app.versionSn')" prop="versionCode">
          <el-input oninput="value=value.replace(/[^\d]/g,'')" v-model.trim="form.versionCode" clearable
            :placeholder="$t('form.input') + $t('system.app.versionSn')" />
        </el-form-item>
        <el-form-item :label="$t('system.computer.desc')" prop="desc">
          <el-input v-model.trim="form.desc" clearable :placeholder="$t('system.computer.input') + $t('system.computer.desc')
            " />
        </el-form-item>
        <el-form-item :label="$t('system.computer.descEn')" prop="descEn">
          <el-input v-model.trim="form.descEn" clearable :placeholder="$t('system.computer.input') + $t('system.computer.descEn')
            " />
        </el-form-item>
        <el-form-item :label="$t('system.computer.downLink2')" prop="downLink">
          <DrUpload v-model="form.downLink" :limit="1" :isOnePic="1" :css="{ width: '100%' }"
            class="flex-direction align-start">
            <el-button size="small" type="primary">
              {{ $t("queryParams.upload") }}
            </el-button>
          </DrUpload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :title="$t('system.computer.history')" :visible.sync="historyOpen"
      width="80%" center>
      <List ref="list" />
    </el-dialog>
  </div>
</template>

<script>
import {
  listBatteryUpdate,
  addBatteryUpdate,
  editBatteryUpdate
} from "@/api/system/battery";
import {
  getProductClass,
  getProductModel,
  getProductCode
} from "@/api/bike/computer";
import { listModel } from "@/api/bike/model";
import List from "./components/list";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    List
  },
  data() {
    const validateVersionName = (rule, value, callback) => {
      const reg = /^(v|V)?(\d+\.)(\d+\.)(\d+)$|^(\d+\.)(\d+)$|^(\d+)$/;
      if (value === "") {
        callback(new Error(this.$t("system.app.rules.versionNum")));
      } else if (!reg.test(value)) {
        callback(new Error(this.$t("mointerObj.correctVerionNumber")));
      } else {
        callback();
      }
    };
    return {
      // 车型
      modelNameList: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      edit: false,
      historyItem: {},
      historyOpen: false,
      modelOptions: [],
      brandOptions: [],
      // 用户表格数据
      softUpdateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        bikeModel: ""
      },
      modelData: {
        data: [],
        page: 1,
        more: true
      },
      // 表单参数
      form: {
        id: undefined,
        type: 1,
        forceUpdate: "0",
        selectVal: [],
        modelId: "",
        brandId: ""
      },
      // 表单校验
      rules: {
        bikeModelName: [
          {
            required: true,
            message: this.$t("form.select") + this.$t("bike.info.modelId"),
            trigger: "change"
          }
        ],
        computerModel: [
          {
            required: false,
            message: this.$t("bike.model.computerIdNotNull"),
            trigger: "blur"
          }
        ],
        versionName: [
          {
            required: true,
            validator: validateVersionName,
            trigger: ["blur", "change"]
          }
        ],
        versionCode: [
          {
            required: true,
            message: this.$t("form.input") + this.$t("system.app.versionSn"),
            trigger: "blur"
          }
        ],
        desc: [{ required: false, message: "", trigger: "blur" }],
        type: [
          {
            required: true,
            message:
              this.$t("system.computer.select") +
              this.$t("system.computer.stopwatch"),
            trigger: "blur"
          }
        ],
        mdCode: [
          {
            required: false,
            message: this.$t("system.computer.mdCode"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取车型
    getModelList({ page = 1, more = false, keyword = "" } = {}) {
      return new Promise(resolve => {
        listModel({
          p: page,
          modelKey: keyword
        }).then(res => {
          const { list, total, pageNum, pageSize } = res.data;
          if (more) {
            this.modelData.data = [...this.modelData.data, ...list];
          } else {
            this.modelData.data = list;
          }
          this.modelData.more = pageNum * pageSize < total;
          this.modelData.page = pageNum;
          resolve();
        });
      });
    },
    getModelInfo(info) {
      if (!info) {
        this.form.bikeModel = "";
        return;
      }
      const { id } = JSON.parse(info);
      this.form.bikeModel = id;
    },
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      if (val.length >= 2) {
        let res = await getProductClass(val);
        if (res.code === 200 && res.data) {
          this.productClassList = res.data;
        }
      }
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productModelList = [];
        this.productCodeList = [];
        this.form.productModel = "";
        this.form.code = "";
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.form.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      } else {
        // 清除下级联动的值
        this.productCodeList = [];
        this.form.code = "";
      }
    },
    /**
     * 新增权限判断
     */
    checkRolesAdd(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      return this.$store.getters.roles.includes(role);
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listBatteryUpdate(this.queryParams).then(response => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        type: 1,
        selectVal: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
    },
    handleChange(val) {
      for (let i = 0; i < this.modelOptions.length; i++) {
        if (this.modelOptions[i].id == val) {
          this.brandOptions = this.modelOptions[i].modelList;
        }
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;

          if (this.form.selectVal && this.form.selectVal.length) {
            this.form.brandId = this.form.selectVal[0];
            this.form.modelId = this.form.selectVal[1];
          }

          let params = Object.assign({}, this.form);
          params.computerModel =
            params.computerModel == "default" ? "" : params.computerModel;
          if (params.id !== undefined) {
            editBatteryUpdate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.updateSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addBatteryUpdate(params)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("dialog.addSuccess"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
    handleHistory(row) {
      this.historyOpen = true;
      let _this = this;
      setTimeout(() => {
        _this.$refs.list.getList(row);
      });
    }
  }
};
</script>
