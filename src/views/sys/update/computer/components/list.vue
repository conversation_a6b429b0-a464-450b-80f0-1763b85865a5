<template>
  <div class="el-dialog-body">
    <el-form :model="queryParams" ref="queryForm" :inline="true"></el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" :disabled="!checkRoles('dev')" @click="handleAdd">
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" size="mini" v-debounce-click="() => handleAuth(0, aFn, getList)">
          {{ $t("system.computer.auth") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" v-debounce-click="() => handleAuth(1, aFn, getList)">
          {{ $t("system.computer.disabled") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-refresh" size="mini" @click="handleQuery">
          {{ $t("system.computer.refresh") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table ref="multipleTableRef" row-key="id" v-loading="loading" :data="softUpdateList" height="200px"
      max-height="500px" @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column type="index" align="center" :label="$t('base.carexception.customerName')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('system.computer.computerId')"
        prop="computerId"
        align="center"
      >
        <template slot-scope="scope">
          {{ getComputerMap(scope.row.computerId) }}
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('bike.computer.queryTable.productClass')" prop="productClass" align="center" />
      <el-table-column :label="$t('bike.computer.queryTable.productModel')" prop="productModel" align="center" />
      <el-table-column :label="$t('bike.computer.queryTable.code')" prop="computerModel" align="center" />
      <el-table-column :label="$t('system.app.versionNum')" prop="versionName" align="center" />
      <el-table-column :label="$t('system.app.versionSn')" prop="versionCode" align="center" />
      <el-table-column :label="$t('system.computer.desc')" prop="desc" align="center">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.desc)"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.descEn')" prop="descEn" align="center">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.descEn)"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.downLink')" prop="downLink" align="center">
        <template v-slot="{ row }">
          <el-img-icon v-if="row.downLink" v-debounce-click="() => urlDownload(row.downLink)" />
        </template>
      </el-table-column>

      <el-table-column :label="$t('system.computer.status')" align="center">
        <template v-slot="{ row }">
          <el-switch v-model="row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.auditStatus')" align="center" prop="state">
        <template v-slot="{ row }">
          <el-tag :type="isStatusType(row.state)">
            {{ setAuditStatus(row.state) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createBy')" align="center" prop="createBy" />
      <el-table-column :label="$t('system.computer.updateTime')" align="center" prop="updateTime" sortable
        width="120px">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.updateTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.handle')" align="center" width="140"
        class-name="small-padding fixed-width">
        <template v-slot="{ row }">
          <el-button size="mini" type="text" @click="handleUpdate(row)">
            {{ $t("bike.computer.update") }}
          </el-button>
          <el-button v-if="auditAuth(row)" type="text" @click="handleAudit(row)">
            {{ $t("system.computer.audit") }}
          </el-button>
          <el-button v-if="reviewAuth(row)" @click="handleAudit(row)" type="text">
            {{ $t("system.computer.review") }}
          </el-button>
          <el-button type="text" class="text-red" @click="handleDel([{ id: row.id }], delFn, getList)">
            {{ $t("bike.info.del") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog v-bind="dialogOption" :view.sync="dialogOption.view" :visible.sync="dialogOption.show"
      @close="closeDynamicDialog" append-to-body center :close-on-click-modal="false" class="el-dialog-dynamic">
      <component :is="dialogOption.view" :rowData="currentRow" @refresh="getList" @close="closeDynamicDialog">
      </component>
    </el-dialog>
  </div>
</template>

<script>
import {
  softListGroup,
  authSoftUpdate,
  editSoftDelete,
  changeState
} from "@/api/system/update";
import AddDialog from "./add-dialog";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  props: {
    computerOptions: {
      type: Array,
      default: () => []
    },
    rowData: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    AddDialog
    // ImgIcon: () => import("@/components/ImgIcon")
  },
  data() {
    return {
      aFn: authSoftUpdate,
      delFn: editSoftDelete,
      modelOptions: [],
      brandOptions: [],
      deviceOptions: [
        {
          key: this.$t("system.computer.stopwatch"),
          value: 1
        }
      ],
      fileList: [],
      // 用户表格数据
      softUpdateList: [],
      // 码表类型
      computerMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: 1
      },
      // 当前点击的row
      currentRow: {},
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: ""
      }
    };
  },
  computed: {
    isStatusType() {
      return (state) => {
        let type = null;
        switch (state) {
          case 0: type = "warning"; break;
          case 1: type = "danger"; break;
          case 2: type = "success"; break;
        }
        return type;
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      const { computerModel } = this.rowData;
      softListGroup({ computerModel, type: 1 })
        .then(response => {
          this.softUpdateList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        type: 1,
        selectVal: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /**
     * 新增
     */
    handleAdd() {
      this.currentRow = {};
      this.showDynamicDialog(
        "AddDialog",
        this.$t("system.computer.handleAdd"),
        "800px"
      );
    },
    /**
     * 修改
     */
    handleUpdate(row) {
      this.currentRow = row;
      this.showDynamicDialog(
        "AddDialog",
        this.$t("system.computer.handleUpdate"),
        "800px"
      );
    },
    /**
     * 审核权限
     */
    auditAuth(row) {
      if (row.state === 0) {
        return this.checkRoles("test");
      }
      return false;
    },
    /**
     * 复核权限
     */
    reviewAuth(row) {
      if (row.state === 1) {
        return this.checkRoles("dev_manager");
      }
      return false;
    },
    /**
     * 角色判断
     * admin 默认有所有权限
     */
    checkRoles(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      let res = false;
      if (Array.isArray(role)) {
        for (var i = 0; i < role.length; i++) {
          let item = role[i];
          let flag = this.$store.getters.roles.includes(item);
          if (flag) {
            res = true;
            break;
          }
        }
      } else {
        res = this.$store.getters.roles.includes(role);
      }
      return res;
    },
    /**
     * 审核
     */
    async handleAudit(row) {
      await changeState({
        id: row.id,
        state: Number(row.state) + 1 // 当前状态 + 1 到下个状态
      });
      this.getList();
    },
    /**
     * 审核状态回写
     */
    setAuditStatus(stata) {
      let maps = {
        0: this.$t("system.computer.toAudit"),
        1: this.$t("system.computer.toReview"),
        2: this.$t("system.computer.done")
      };
      return maps[stata];
    },
    getComputerMap(type) {
      return this.computerMap.get(String(type));
    },
    showDynamicDialog(view, title, width = "1200px") {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-button.el-button--text.is-disabled {
  color: #ccc;
}
</style>
