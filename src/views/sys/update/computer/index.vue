<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
        <!-- 品类 -->
        <el-form-item :label="$t('bike.computer.queryTable.productClass')" prop="productClass">
          <el-select v-model="queryParams.productClass" remote filterable clearable
            :placeholder="$t('bike.computer.placeholder.productClass')" :remote-method="productClassRemoteMethod"
            @change="handleProductClassSelect">
            <el-option v-for="item in productClassList" :key="item.key" :label="item.value" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 型号 -->
        <el-form-item :label="$t('bike.computer.queryTable.productModel')" prop="productModel">
          <el-select v-model="queryParams.productModel" filterable clearable @change="handleProductModelSelect"
            :placeholder="$t('form.select') + $t('bike.computer.queryTable.productModel')
              ">
            <el-option v-for="item in productModelList" :key="item.key" :label="item.value" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 编码 -->
        <el-form-item :label="$t('bike.computer.queryTable.code')" prop="code">
          <el-select v-model="queryParams.code" filterable clearable :placeholder="$t('form.select') + $t('bike.computer.queryTable.code')
            ">
            <el-option v-for="item in productCodeList" :key="item.key" :label="item.value" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" v-debounce-click="handleQuery">
            {{ $t("discover.posted.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" v-debounce-click="resetQuery">
            {{ $t("discover.posted.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" :disabled="!checkRoles('dev')" @click="handleAdd">
          {{ $t("queryParams.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)">
          {{ $t("bike.model.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1, aFn, getList)">
          {{ $t("bike.model.forbidden") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table ref="multipleTableRef" row-key="id" :height="tableHeight()" v-loading="loading" :data="softUpdateList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column type="index" align="center" :label="$t('base.carexception.customerName')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.computer.queryTable.productClass')" prop="productClass" align="center" />
      <el-table-column :label="$t('bike.computer.queryTable.productModel')" prop="productModel" align="center" />
      <el-table-column :label="$t('bike.computer.queryTable.code')" prop="computerModel" align="center" />
      <el-table-column :label="$t('system.app.versionNum')" prop="versionName" align="center" />
      <el-table-column :label="$t('system.app.versionSn')" prop="versionCode" align="center" />
      <el-table-column :label="$t('system.computer.desc')" prop="desc" align="center">
        <span slot-scope="scope" v-NoData="scope.row.desc"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.descEn')" prop="descEn" align="center">
        <span slot-scope="scope" v-NoData="scope.row.descEn"></span>
      </el-table-column>
      <el-table-column :label="$t('system.computer.status')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.createBy')" align="center" prop="createBy" />
      <el-table-column :label="$t('system.computer.createTime')" align="center" sortable prop="createTime">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('system.computer.handle')" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleHistory(scope.row)">
            {{ $t("base.help.lookOver") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog v-bind="dialogOption" :view.sync="dialogOption.view" :visible.sync="dialogOption.show"
      @close="closeDynamicDialog" append-to-body :close-on-click-modal="false" class="el-dialog-dynamic" center>
      <component :is="dialogOption.view" :computerOptions="computerOptions" :rowData="currentRow" @refresh="getList"
        @close="closeDynamicDialog">
      </component>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSoftUpdate,
  authSoftUpdate,
  editSoftDelete,
} from "@/api/system/update";
import {
  getProductClass,
  getProductModel,
  getProductCode,
} from "@/api/bike/computer";
import { listDictComputer } from "@/api/base/dict";

import List from "./components/list";
import AddDialog from "./components/add-dialog";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    List,
    AddDialog,
  },
  data() {
    return {
      aFn: authSoftUpdate,
      historyItem: {},
      historyOpen: false,
      modelOptions: [],
      brandOptions: [],
      fileList: [],
      // 用户表格数据
      softUpdateList: [],
      // 码表类型
      computerOptions: [],
      computerMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      genderOptions: [],
      // 品类列表
      productClassList: [],
      // 型号列表
      productModelList: [],
      // code 列表
      productCodeList: [],
      // 当前点击的row
      currentRow: {},
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: 1,
        productClass: null,
        productModel: null,
        code: null,
      },
      // 表单参数
      form: {
        id: undefined,
        type: 1,
        forceUpdate: "0",
        selectVal: [],
        modelId: "",
        brandId: "",
      },
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: "",
      },
    };
  },
  created() {
    listDictComputer().then((response) => {
      this.computerOptions = response.data;
      this.computerMap = new Map();
      this.computerOptions.forEach((m) => {
        this.computerMap.set(m.key, m.value);
      });
      this.getList();
    });
  },
  methods: {
    /**
     * 品类远程搜索
     */
    async productClassRemoteMethod(val) {
      let res = await getProductClass(val);
      this.productClassList = res.data;
    },
    /**
     * 选择品类
     */
    async handleProductClassSelect(val) {
      this.productModelList = [];
      this.productCodeList = [];
      this.queryParams.productModel = "";
      this.queryParams.code = "";

      if (val) {
        // 根据品类ID获取型号列表
        let res = await getProductModel(val);
        if (res.code === 200 && res.data) {
          this.productModelList = res.data;
        }
      }
    },
    /**
     * 选择型号
     */
    async handleProductModelSelect(val) {
      this.productCodeList = [];
      this.queryParams.code = "";

      if (val) {
        // 根据品类ID 和 型号 ID 获取 code 列表
        let res = await getProductCode(this.queryParams.productClass, val);
        if (res.code === 200 && res.data) {
          this.productCodeList = res.data;
        }
      }
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listSoftUpdate(this.queryParams).then((response) => {
        this.softUpdateList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        type: 1,
        selectVal: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModelList = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleUpdate(row) {
      this.handleChange(row.modelId);
      this.form = Object.assign({}, row);
      this.form.computerModel = row.computerModel == "" ? "default" : row.computerModel;
      if (row.downLink) {
        this.fileList = [{ name: row.downLink, url: row.downLink }];
      } else {
        this.fileList = [];
      }
      this.open = true;
      this.title = this.$t("system.computer.handleUpdate");
    },
    uploadSuccess(res) {
      this.form.downLink = res.data[0].url;
      this.form.mdCode = res.data[0].md5;
    },
    handleChange(val) {
      for (let i = 0; i < this.modelOptions.length; i++) {
        if (this.modelOptions[i].id == val) {
          this.brandOptions = this.modelOptions[i].modelList;
        }
      }
    },
    computerModelName(row) {
      let { computerOptions } = this;
      let idx = computerOptions.findIndex(
        (item) => item.key == row.computerModel
      );
      if (idx > -1) {
        return computerOptions[idx].value;
      }
      return "";
    },
    computerModelCode(row) {
      let { computerOptions } = this;
      let idx = computerOptions.findIndex(
        (item) => item.key == row.computerModel
      );
      if (idx > -1) {
        return computerOptions[idx].model;
      }
      return "";
    },
    handleDel(row) {
      this.$confirm(this.$t("system.role.deletePoint"), {
        confirmButtonText: this.$t("bike.computer.confirm"),
        cancelButtonText: this.$t("bike.computer.cancel"),
        type: this.$t("bike.computer.warning"),
      }).then(() => {
        this.loading = true;
        this.loading = false;
        editSoftDelete([{ id: row.id }]).then(() => {
          this.msgSuccess(this.$t("dialog.deleteSuccess"));
          this.loading = false;
          this.getList();
        });
      });
    },
    /**
     * 新增
     */
    handleAdd() {
      this.currentRow = {};
      this.showDynamicDialog(
        "AddDialog",
        this.$t("system.computer.handleAdd"),
        "800px"
      );
    },
    /**
     * 查看
     */
    handleHistory(row) {
      this.currentRow = row;
      this.showDynamicDialog(
        "List",
        this.$t("system.computer.history"),
        "1400px"
      );
    },
    /**
     * 角色判断
     * admin 默认有所有权限
     */
    checkRoles(role) {
      if (this.$store.getters.roles.includes("admin")) {
        return true;
      }
      let res = false;
      if (Array.isArray(role)) {
        for (var i = 0; i < role.length; i++) {
          let item = role[i];
          let flag = this.$store.getters.roles.includes(item);
          if (flag) {
            res = true;
            break;
          }
        }
      } else {
        res = this.$store.getters.roles.includes(role);
      }
      return res;
    },
    showDynamicDialog(view, title, width = "1200px") {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    },
  },
};
</script>
