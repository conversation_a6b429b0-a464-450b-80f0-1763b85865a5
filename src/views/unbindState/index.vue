<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="蓝牙地址" prop="bluetooth">
        <el-input v-model.trim="queryParams.bluetooth" :placeholder="$t('form.input')" clearable :maxlength="17"
          @input="handleBlueTooth" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model.trim="queryParams.nickName" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="queryParams.email" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model.trim="queryParams.phone" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="申请用户昵称" prop="nickName" align="center">
        <span slot-scope="{ row }" v-NoData="row.nickName"></span>
      </el-table-column>
      <el-table-column label="蓝牙地址" prop="bluetooth" align="center">
        <span slot-scope="{ row }" v-NoData="row.bluetooth"></span>
      </el-table-column>
      <el-table-column label="原车主邮箱" prop="email" align="center">
        <span slot-scope="{ row }" v-NoData="row.email"></span>
      </el-table-column>
      <el-table-column label="原车主电话号码" prop="phone" align="center">
        <span slot-scope="{ row }" v-NoData="row.phone"></span>
      </el-table-column>
      <el-table-column label="原车主昵称" prop="originalName" align="center">
        <span slot-scope="{ row }" v-NoData="row.originalName"></span>
      </el-table-column>
      <el-table-column label="购买图片" prop="buyImg" align="center">
        <template slot-scope="scope">
          <preview-img :imgUrl="scope.row.buyImg" width="45px" height="45px" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="{ row }">
          <el-tag v-if="row.status === 0" type="danger">未处理</el-tag>
          <el-tag v-if="row.status === 1" type="success">已处理</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申述时间" prop="createTime" align="center">
        <span slot-scope="{ row }" v-NoData="parseTime(row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('navigatorManage.tableTxt.opt')" align="center"
        class-name="small-padding fixed-width">
        <template slot-scope="{ row }">
          <el-popconfirm title="确定要解绑吗？" @confirm="handleUnbindState(row.bluetooth)">
            <el-button slot="reference"> 解绑 </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { unbindStateList, unbindBikeAuth } from "@/api/unbindState";
import { commonJs } from "@/mixinFile/common";
import { transBluetooth } from "@/utils/index.js";

export default {
  name: "UnbindState",
  mixins: [commonJs],
  data() {
    return {
      loading: false,
      queryParams: {
        p: 1,
        l: 20,
        bluetooth: undefined,
        nickName: undefined,
        email: undefined,
        phone: undefined,
      },
      dataList: [],
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      unbindStateList(this.queryParams).then((res) => {
        const { list, total } = res.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 解绑
    handleUnbindState(bluetooth) {
      unbindBikeAuth(bluetooth)
        .then(() => {
          this.msgSuccess("解绑成功");
          this.getList();
        })
        .catch(() => {
          this.msgSuccess("解绑失败");
        });
    },
    handleBlueTooth(bluetooth) {
      if (this.$IS_Empty(bluetooth)) return;

      bluetooth = transBluetooth(bluetooth);
      this.queryParams.bluetooth = bluetooth;
    },
  },
};
</script>

<style></style>