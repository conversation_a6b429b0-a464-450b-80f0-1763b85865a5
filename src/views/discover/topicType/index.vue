<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item label="类型名称" prop="name">
        <el-input v-model.trim="queryParams.name" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("system.user.add") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column type="index" width="65" align="center" label="序号">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="类型名称" prop="name" align="center">
        <span slot-scope="{ row }" v-NoData="row.name"></span>
      </el-table-column>
      <el-table-column label="话题排序" prop="sort" sortable align="center">
        <span slot-scope="{ row }" v-NoData="row.sort"></span>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createBy" align="center">
        <span slot-scope="{ row }" v-NoData="row.createBy"></span>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" sortable align="center">
        <span slot-scope="{ row }" v-NoData="parseTime(row.createTime)"></span>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <Tooltip icon="el-icon-edit" content="编辑" @click="handleUpdate(row)" />
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body center>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="left">
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" clearable placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="话题排序" prop="sort">
          <el-input-number v-model="form.sort" placeholder="请输入话题排序" :precision="0" controls-position="right"
            :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio-button :label="0">启用</el-radio-button>
            <el-radio-button :label="1">禁用</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm('form')">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="handleCancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  topicTypeList,
  topicTypeAdd,
  topicTypeEdit,
  topicTypeAuth,
} from "@/api/posted";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "TopicType",
  mixins: [commonJs],
  data() {
    return {
      aFn: topicTypeAuth,
      title: "",
      loading: false,
      queryParams: {
        p: 1,
        l: 20,
        name: undefined,
      },
      dataList: [],
      form: {},
      rules: {
        name: [{ required: true, message: "请输入类型名称", trigger: "blur" }],
        sort: [{ required: true, message: "请输入话题排序", trigger: "blur" }],
        status: [{ required: true, message: "请选择状态", trigger: "change" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      topicTypeList(this.queryParams)
        .then((res) => {
          const { list, total } = res.data;
          this.dataList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 解绑
    handleUnbindState(bluetooth) {
      unbindBikeAuth(bluetooth)
        .then(() => {
          this.msgSuccess("解绑成功");
          this.getList();
        })
        .catch(() => {
          this.msgSuccess("解绑失败");
        });
    },
    reset() {
      this.form = {
        name: "",
        sort: "",
        status: 0,
      };
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.title = "添加话题类型";
      this.open = true;
    },
    handleUpdate(row) {
      this.reset();
      this.title = "编辑话题类型";
      this.open = true;
      this.form = Object.assign({}, row);
    },
    handleCancel() {
      this.open = false;
      this.reset();
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.isBtnLoading = true;

          if (this.form.id) {
            topicTypeEdit(this.form)
              .then(() => {
                this.msgSuccess("编辑成功");
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            topicTypeAdd(this.form)
              .then(() => {
                this.msgSuccess("添加成功");
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>