<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('discover.posted.nickName')" prop="userKey">
        <el-input v-model.trim="queryParams.userKey" :placeholder="$t('discover.posted.userPal')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('discover.posted.content')" prop="content">
        <el-input v-model.trim="queryParams.content" :placeholder="$t('discover.posted.contentPal')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('discover.posted.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('form.select') + $t('discover.posted.status')"
          clearable>
          <el-option v-for="item in options" :key="item.value" :label="item.key" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('discover.posted.time')">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="-"
          :start-placeholder="$t('discover.posted.beginTime')" :end-placeholder="$t('discover.posted.endTime')"
          clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("discover.posted.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("discover.posted.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['acc:user:auth']">
          {{ $t("btn.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['acc:user:auth']">
          {{ $t("btn.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table ref="multipleTableRef" v-loading="loading" :data="postedList" row-key="id"
      @selection-change="handleSelectionChange" :height="tableHeight(-0)">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column :label="$t('discover.posted.headName')" align="center" width="60px">
        <template slot-scope="scope">
          <preview-img :imgUrl="scope.row.headName" width="45px" height="45px" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.nickName')" align="center" prop="nickName" width="120" />
      <el-table-column :label="$t('discover.posted.content')" align="center" prop="content"
        :show-overflow-tooltip="true">
        <span slot-scope="scope" v-NoData="scope.row.content"></span>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.userType')" align="center" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.userType == 1 ? 'success' : ''">
            {{ $t("discover.posted.source." + (scope.row.userType + "")) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.likeNum')" align="center" prop="likeNum" width="80" />
      <el-table-column :label="$t('discover.posted.commentNum')" align="center" prop="commentNum" width="80" />
      <el-table-column :label="$t('discover.posted.status')" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">
            {{
              options.filter((item) => item.value == scope.row.status)[0].key
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.topStatus')" align="center" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isTop === 1" type="warning" effect="dark" closable @close="handleToggleTop(scope.row)"
            class="top-tag-clickable">
            <i class="el-icon-bottom"></i>
            {{ $t('discover.posted.topped') }}
          </el-tag>
          <el-button v-else type="text" size="mini" @click="handleToggleTop(scope.row)" class="normal-tag-clickable">
            <i class="el-icon-top"></i>
            {{ $t('discover.posted.setTop') }}
          </el-button>
        </template>
      </el-table-column>

      <el-table-column :label="$t('discover.posted.imgs')" align="center" width="80">
        <template v-slot="{ row }">
          <preview-img :imgUrl="row.imgs" width="60px" height="60px" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.video')" align="center" prop="brandName" width="80">
        <template slot-scope="{ row }">
          <VideoPlayer v-if="row.videoRoute" :src="row.videoRoute" :cover="row.videoCover || ''" :width="60"
            :height="60" :title="row.content || ''" />
          <span v-else>- - -</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.remark.createTime')" align="center" sortable prop="createTime" width="140">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.handle')" align="center" width="160">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-tooltip :content="$t('discover.posted.look')" placement="top">
              <el-button type="text" icon="el-icon-view" size="mini" @click="handleComment(scope.row)">
              </el-button>
            </el-tooltip>

            <el-tooltip :content="$t('discover.posted.detail')" placement="top">
              <el-button type="text" icon="el-icon-tickets" size="mini" @click="handleDetail(scope.row)">
              </el-button>
            </el-tooltip>

            <el-tooltip :content="scope.row.isTop === 1 ? $t('discover.posted.cancelTop') : $t('discover.posted.top')"
              placement="top">
              <el-button type="text" :icon="scope.row.isTop === 1 ? 'el-icon-bottom' : 'el-icon-top'"
                :class="scope.row.isTop === 1 ? 'text-warning' : 'text-primary'" size="mini"
                @click="handleTop(scope.row)">
              </el-button>
            </el-tooltip>

            <el-tooltip :content="$t('discover.posted.delete')" placement="top">
              <el-button type="text" icon="el-icon-delete" class="text-danger" size="mini"
                @click="handleDelete(scope.row)">
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogTableVisible" width="580px">
      <div>
        <div class="flex justify-center">
          <el-badge v-if="detailData" :value="$t('discover.posted.source.' + (detailData.userType + ''))" class="item"
            :type="detailData.userType == 1 ? 'success' : 'primary'">

            <preview-img shape="square" style="width: 100px; height: 100px" :imgUrl="detailData.headName"></preview-img>
          </el-badge>
        </div>
        <div class="text-center" style="margin-top: 10px; font-size: 18px; font-weight: 700">
          {{ detailData.nickName }}
        </div>
        <div class="flex justify-center" style="margin-top: 10px">
          <div style="margin-right: 30px">
            <el-tooltip class="item" effect="dark" :content="$t('discover.posted.likeNum')" placement="top-start">
              <div>
                <span class="el-icon-thumb" style="font-size: 20px; color: #1890ff"></span>
                {{ detailData.likeNum }}
              </div>
            </el-tooltip>
          </div>
          <div>
            <el-tooltip class="item" effect="dark" :content="$t('discover.posted.commentNum')" placement="top-start">
              <div>
                <span class="el-icon-chat-line-square" style="font-size: 20px; color: #13ce66"></span>

                {{ detailData.commentNum }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <el-card shadow="never" style="margin-top: 20px">
        <div>{{ detailData.content }}</div>

        <el-row :gutter="10" class="margin-top-sm">
          <preview-img v-show="detailData.imgs" :imgUrl="detailData.imgs" />
        </el-row>
      </el-card>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="openComment" width="680px">
      <div class="flex">
        <div class="flex justify-center">
          <el-badge v-if="detailData" :value="$t('discover.posted.source.' + (detailData.userType + ''))" class="item"
            :type="detailData.userType == 1 ? 'success' : 'primary'">
            <preview-imgr shape="square" style="width: 50px; height: 50px" :imgUrl="detailData.headName" />
          </el-badge>
        </div>
        <div class="text-center flex align-center" style="margin-left: 20px; font-size: 14px; font-weight: 700">
          {{ detailData.nickName }}
        </div>
      </div>
      <el-card v-show="detailData.content !== '' || detailData.imgs" shadow="never" style="margin-top: 10px">
        <div>{{ detailData.content }}</div>
        <div class="margin-top-sm">
          <preview-img v-show="detailData.imgs" :imgUrl="detailData.imgs" />
        </div>
      </el-card>
      <div class="flex justify-end align-center" style="margin-top: 10px">
        <div>
          <el-tooltip class="item" effect="dark" :content="$t('discover.posted.likeNum')" placement="top-start">
            <div>
              <span class="el-icon-thumb" style="font-size: 20px; color: #1890ff" />
              {{ detailData.likeNum }}
            </div>
          </el-tooltip>
        </div>
        <div style="margin: 0 15px">
          <el-divider direction="vertical"></el-divider>
        </div>
        <div>
          <el-tooltip class="item" effect="dark" :content="$t('discover.posted.commentNum')" placement="top-start">
            <div>
              <span class="el-icon-chat-line-square" style="font-size: 20px; color: #13ce66" />
              {{ detailData.commentNum }}
            </div>
          </el-tooltip>
        </div>
      </div>
      <div style="background: #f2f2f5; margin: 20px -20px 0; position: relative" class="padding-tb-xs"
        v-if="commentTotal > 0">
        <div class="padding-lr row-com" v-for="(item, index) in commentList" :key="index">
          <el-divider class="margin-tb-xs" v-if="index > 0"></el-divider>
          <div style="line-height: 23px">
            <span style="color: #eb7350"> {{ item.nickName }} </span>:
            <span>{{ item.content }}</span>
          </div>
          <div class="time-s flex justify-between">
            <span>
              {{ parseTime(item.createTime) }}
            </span>
            <a v-debounce-click="() => handleStatusChange(item, aFn, getList)">
              {{ $t("queryParams.delete") }}
            </a>
          </div>
        </div>
      </div>
      <pagination v-show="commentTotal > 0" :total="commentTotal" :page.sync="queryCommentParams.p"
        :limit.sync="queryCommentParams.l" @pagination="getComment" />
    </el-dialog>
  </div>
</template>

<script>
import { listPosted, authPosted, listPostedRemark, postedTopAuth, deletePosted } from "@/api/posted";
import { commonJs } from "@/mixinFile/common";
import VideoPlayer from "@/components/VideoPlayer";

export default {
  mixins: [commonJs],
  name: "AccUser",
  components: {
    VideoPlayer
  },
  data() {
    return {
      aFn: authPosted,
      detailData: {},
      dialogTableVisible: false,
      openComment: false,
      // 用户表格数据
      postedList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      options: [
        {
          key: this.$t("btn.startUsing"),
          value: 0,
        },
        {
          key: this.$t("btn.forbidden"),
          value: 1,
        },
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        userKey: undefined,
        content: undefined,
        status: undefined,
      },
      queryCommentParams: {
        p: 1,
        l: 20,
        status: 0,
        id: undefined,
      },
      commentList: [],
      commentTotal: 0,
      // 表单参数
      form: {},
      userDetail: {
        user: {},
        ridingData: {},
        bikeList: [],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listPosted(this.addDateRange(this.queryParams, this.dateRange))
        .then((res) => {
          const { list, total } = res.data;
          this.postedList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleDetail(row) {
      this.detailData = row;
      this.dialogTableVisible = true;
    },
    handleComment(row) {
      this.openComment = true;
      this.detailData = row;
      this.queryCommentParams.p = 1;
      this.queryCommentParams.id = row.id;
      this.getComment();
    },
    getComment() {
      listPostedRemark(this.queryCommentParams).then((response) => {
        this.commentList = response.data.list;
        this.commentTotal = response.data.total;
      });
    },

    // 置顶功能
    handleTop(row) {
      const isTop = row.isTop === 1 ? 0 : 1; // 切换置顶状态
      const message = isTop === 1 ? '确定要置顶此帖子吗？' : '确定要取消置顶此帖子吗？';

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = [{
          id: row.id,
          state: isTop
        }];

        postedTopAuth(data).then(response => {
          if (response.code === 200) {
            this.$message.success(isTop === 1 ? '置顶成功' : '取消置顶成功');
            this.getList();
          } else {
            this.$message.error(response.msg || '操作失败');
          }
        }).catch(() => {
          this.$message.error('操作失败');
        });
      });
    },

    // 置顶状态切换功能（通过状态列直接操作）
    handleToggleTop(row) {
      const isTop = row.isTop === 1 ? 0 : 1; // 切换置顶状态
      const message = isTop === 1 ? '确定要置顶此帖子吗？' : '确定要取消置顶此帖子吗？';

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = [{
          id: row.id,
          state: isTop
        }];

        postedTopAuth(data).then(response => {
          if (response.code === 200) {
            this.$message.success(isTop === 1 ? '置顶成功' : '取消置顶成功');
            this.getList();
          } else {
            this.$message.error(response.msg || '操作失败');
          }
        }).catch(() => {
          this.$message.error('操作失败');
        });
      });
    },

    // 删除帖子
    handleDelete(row) {
      this.$confirm('确定要删除此帖子吗？删除后无法恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePosted(row.id).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.getList();
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(() => {
          this.$message.error('删除失败');
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.row-com {
  .time-s {
    margin-top: 8px;

    span {
      color: #808080;
      font-size: 14px;
    }

    a {
      color: #f56c6c;
      display: none;
    }
  }

  &:hover {
    .time-s {
      a {
        display: inline-block;
      }
    }
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.top-tag-clickable {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
  }

  .el-tag__close {
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.normal-tag-clickable {
  color: #909399;
  transition: all 0.3s ease;

  &:hover {
    color: #409EFF;
    transform: scale(1.05);
  }

  i {
    margin-right: 4px;
  }
}

// 操作按钮样式
.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;

  .el-button {
    padding: 5px;
    min-width: auto;

    &.text-primary {
      color: #409EFF;

      &:hover {
        color: #66b1ff;
      }
    }

    &.text-warning {
      color: #E6A23C;

      &:hover {
        color: #ebb563;
      }
    }

    &.text-danger {
      color: #F56C6C;

      &:hover {
        color: #f78989;
      }
    }
  }
}
</style>
