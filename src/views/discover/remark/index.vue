<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.user.nickName')" prop="key">
        <el-input v-model="queryParams.key" :placeholder="$t('form.input') + $t('acc.user.nickName')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('acc.riding.have.email')" prop="email">
        <el-input v-model="queryParams.email" :placeholder="$t('form.input') + $t('acc.riding.have.email')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('discover.remark.content')" prop="content">
        <el-input v-model.trim="queryParams.content" :placeholder="$t('discover.remark.contentPla')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('discover.posted.time')">
        <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="-" clearable
          :start-placeholder="$t('discover.posted.beginTime')" :end-placeholder="$t('discover.posted.endTime')" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("discover.posted.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("discover.posted.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['acc:user:auth']">
          {{ $t("discover.remark.using") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['acc:user:auth']">
          {{ $t("discover.remark.disabled") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table ref="multipleTableRef" row-key="id" v-loading="loading" :data="markList" :height="tableHeight()"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column :label="$t('discover.remark.index')" align="center" type="index">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.remark.nickName')" align="center" prop="nickName" />
      <el-table-column :label="$t('discover.remark.email')" align="center" prop="email">
        <span slot-scope="scope" v-NoData="scope.row.email"></span>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.content')" prop="toContent" :show-overflow-tooltip="true"
        align="center">
        <span slot-scope="scope" v-NoData="scope.row.toContent"></span>
      </el-table-column>
      <el-table-column :label="$t('discover.remark.content')" prop="content" align="center"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('discover.remark.createTime')" align="center" sortable prop="createTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('discover.remark.status')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)" hasPerminone="['acc:user:query']">
            {{ $t("acc.user.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :title="$t('discover.posted.dialog')" :visible.sync="dialogTableVisible" width="880px" center
      :close-on-click-modal="false">
      <el-card shadow="never" style="margin-top: 10px">
        <div>{{ detailData.content }}</div>
        <el-row :gutter="10" class="margin-top-sm">
          <el-col :span="8" v-for="(item, index) in spImgs(detailData.imgs)" :key="index">
            <preview-img :imgUrl="item" />
          </el-col>
        </el-row>
      </el-card>

      <div style="background: #f2f2f5; margin: 20px -20px 0; position: relative" class="padding-tb-xs">
        <div class="padding-lr row-com">
          <div style="line-height: 23px">
            <span style="color: #eb7350"> {{ detailData.nickName }} </span>:
            <span>{{ detailData.content }}</span>
          </div>
          <div class="time-s flex justify-between">
            <span style="color: #808080">
              {{ parseTime(detailData.createTime) }}
            </span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPostedRemark, authPostedRemark } from "@/api/posted";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  name: "AccUser",
  data() {
    return {
      aFn: authPostedRemark,
      detailData: {},
      dialogTableVisible: false,
      // 用户表格数据
      markList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        id: "",
        key: "",
        email: "",
        phone: "",
        content: ""
      }
    };
  },
  created() {
    let { id } = this.$route.query;
    if (id) {
      this.queryParams.id = id;
    }
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      listPostedRemark(this.addDateRange(this.queryParams, this.dateRange))
        .then(res => {
          const { list, total } = res.data;
          this.markList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    spImgs(val) {
      if (val) {
        return val.split(",");
      }
      return [];
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      this.detailData = row;
    }
  }
};
</script>
