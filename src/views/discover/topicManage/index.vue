<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('base.topicManage.topicName')" prop="topicName">
        <el-input v-model.trim="queryParams.topicName" :placeholder="$t('form.input')" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('base.topicManage.typeName')" prop="typeId">
        <el-select v-model="queryParams.typeId" :placeholder="$t('base.topicManage.pleaseSelectTypeName')" clearable
          style="width: 100%">
          <el-option v-for="item in topicTypeList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("base.feedback.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("base.feedback.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("base.topicManage.add") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" icon="el-icon-check" :disabled="!multipleSelection.length"
          @click="handleBatchAuth(0)">
          {{ $t("base.topicManage.batchEnable") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" icon="el-icon-close" :disabled="!multipleSelection.length"
          @click="handleBatchAuth(1)">
          {{ $t("base.topicManage.batchDisable") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column type="index" width="65" align="center" :label="$t('base.feedback.serialNumber')">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.topicName')" prop="topicName" align="center">
        <span slot-scope="{ row }" v-NoData="row.topicName"></span>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.topicSort')" prop="sort" sortable align="center">
        <span slot-scope="{ row }" v-NoData="row.sort"></span>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.typeName')" prop="typeName" align="center">
        <span slot-scope="{ row }" v-NoData="row.typeName"></span>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.status')" prop="status" align="center">
        <template slot-scope="{ row }">
          <el-tag :type="row.status === 0 ? 'success' : 'danger'">
            {{ row.status === 0 ? $t('base.topicManage.enable') : $t('base.topicManage.disable') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.creator')" prop="createBy" align="center">
        <span slot-scope="{ row }" v-NoData="row.createBy"></span>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.createTime')" prop="createTime" sortable align="center">
        <span slot-scope="{ row }" v-NoData="parseTime(row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('base.topicManage.operation')" align="center">
        <template slot-scope="{ row }">
          <Tooltip icon="el-icon-edit" :content="$t('base.topicManage.edit')" @click="handleUpdate(row)" />

          <Tooltip :icon="row.status === 0 ? 'el-icon-close' : 'el-icon-check'"
            :content="row.status === 0 ? $t('base.topicManage.disable') : $t('base.topicManage.enable')"
            :className="row.status === 0 ? ['text-warning'] : ['text-success']" @click="handleStatusChange(row)" />
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body center>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="left">
        <el-form-item :label="$t('base.topicManage.topicName')" prop="topicName">
          <el-input v-model="form.topicName" clearable :placeholder="$t('base.topicManage.pleaseEnterTopicName')" />
        </el-form-item>
        <el-form-item :label="$t('base.topicManage.typeName')" prop="typeId">
          <el-select v-model="form.typeId" :placeholder="$t('base.topicManage.pleaseSelectTypeName')" clearable
            style="width: 100%">
            <el-option v-for="item in topicTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('base.topicManage.topicSort')" prop="sort">
          <el-input-number v-model="form.sort" :placeholder="$t('base.topicManage.pleaseEnterTopicSort')" :precision="0"
            controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item :label="$t('base.topicManage.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio-button :label="0">{{ $t('base.topicManage.enable') }}</el-radio-button>
            <el-radio-button :label="1">{{ $t('base.topicManage.disable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm('form')">
          {{ $t("dialog.confirm") }}
        </el-button>
        <el-button @click="handleCancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { topicList, topicAdd, topicEdit, topicDelete, topicAuth, topicTypeList } from "@/api/posted";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "TopicManage",
  mixins: [commonJs],
  data() {
    return {
      title: "",
      loading: false,
      queryParams: {
        p: 1,
        l: 20,
        topicName: undefined,
        typeId: undefined,
      },
      dataList: [],
      form: {},
      multipleSelection: [],
      rules: {},
      topicTypeList: [], // 话题类型列表
    };
  },
  created() {
    this.initRules();
    this.getList();
    this.getTopicTypeList();
  },
  watch: {
    // 监听语言变化，重新初始化验证规则
    '$i18n.locale'() {
      this.initRules();
    }
  },
  methods: {
    // 初始化验证规则
    initRules() {
      this.rules = {
        topicName: [
          { required: true, message: this.$t('base.topicManage.pleaseEnterTopicName'), trigger: "blur" },
        ],
        typeId: [
          { required: true, message: this.$t('base.topicManage.pleaseSelectTypeName'), trigger: "change" },
        ],
        sort: [{ required: true, message: this.$t('base.topicManage.pleaseEnterTopicSort'), trigger: "blur" }],
        status: [{ required: true, message: this.$t('base.topicManage.pleaseSelectStatus'), trigger: "change" }],
      };
    },
    getList() {
      this.loading = true;
      topicList(this.queryParams)
        .then((res) => {
          const { list, total } = res.data;
          this.dataList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取话题类型列表
    getTopicTypeList() {
      topicTypeList({ p: 1, l: 999 })
        .then((res) => {
          this.topicTypeList = res.data.list || [];
        })
        .catch(() => {
          this.topicTypeList = [];
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 解绑
    handleUnbindState(bluetooth) {
      unbindBikeAuth(bluetooth)
        .then(() => {
          this.msgSuccess("解绑成功");
          this.getList();
        })
        .catch(() => {
          this.msgSuccess("解绑失败");
        });
    },
    reset() {
      this.form = {
        topicName: "",
        typeId: "",
        sort: "",
        status: 0,
      };
      this.resetForm("form");
    },
    // 选择变化处理
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    // 状态切换处理
    handleStatusChange(row) {
      const status = row.status === 0 ? 1 : 0;
      const statusText = status === 0 ? this.$t('base.topicManage.enable') : this.$t('base.topicManage.disable');

      this.$confirm(
        `确定要${statusText}该话题吗？`,
        '提示',
        {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          type: 'warning'
        }
      ).then(() => {
        topicAuth([{ id: row.id, status: status }])
          .then(() => {
            this.msgSuccess(this.$t('base.topicManage.operationSuccess'));
            this.getList();
          })
          .catch(() => {
            this.msgError(this.$t('base.topicManage.operationFailed'));
          });
      });
    },
    // 批量启用/禁用处理
    handleBatchAuth(status) {
      if (this.multipleSelection.length === 0) {
        this.msgWarning(this.$t('base.topicManage.pleaseSelectData'));
        return;
      }

      const statusText = status === 0 ? this.$t('base.topicManage.enable') : this.$t('base.topicManage.disable');

      this.$confirm(
        this.$t('base.topicManage.batchOperationConfirm'),
        `批量${statusText}`,
        {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          type: 'warning'
        }
      ).then(() => {
        const data = this.multipleSelection.map(item => ({
          id: item.id,
          status: status
        }));

        topicAuth(data)
          .then(() => {
            this.msgSuccess(this.$t('base.topicManage.operationSuccess'));
            this.getList();
            this.multipleSelection = [];
          })
          .catch(() => {
            this.msgError(this.$t('base.topicManage.operationFailed'));
          });
      });
    },
    handleAdd() {
      this.reset();
      this.title = this.$t('base.topicManage.addTopicType');
      this.open = true;
    },
    handleUpdate(row) {
      this.reset();
      this.title = this.$t('base.topicManage.editTopicType');
      this.open = true;
      this.form = Object.assign({}, row);
    },
    handleDelete(delId) {
      topicDelete(delId)
        .then(() => {
          this.msgSuccess(this.$t('base.topicManage.deleteSuccess'));
          this.getList();
        })
        .catch(() => {
          this.msgError(this.$t('base.topicManage.deleteFailed'));
        });
    },
    handleCancel() {
      this.open = false;
      this.reset();
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.isBtnLoading = true;

          if (this.form.id) {
            topicEdit(this.form)
              .then(() => {
                this.msgSuccess(this.$t('base.topicManage.editSuccess'));
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            topicAdd(this.form)
              .then(() => {
                this.msgSuccess(this.$t('base.topicManage.addSuccess'));
                this.open = false;
                this.getList();
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    },
  },
};
</script>