<template>
  <div class="app-container">
    <transition name="slide-fade">
      <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent v-show="showSearch">
        <el-form-item :label="$t('bike.bike.computerName')" prop="key">
          <el-input v-model="queryParams.key" :placeholder="$t('bike.computer.meterNameInput')" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
            {{ $t("bike.brand.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
            {{ $t("bike.brand.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
    </transition>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd" hasPerminone="['bike:brand:add']">
          {{ $t("bike.brand.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)"
          hasPerminone="['bike:brand:auth']">
          {{ $t("bike.brand.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" v-debounce-click="() => handleAuth(1, aFn, getList)"
          hasPerminone="['bike:brand:auth']">
          {{ $t("bike.brand.forbidden") }}
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table ref="multipleTableRef" v-loading="loading" row-key="id" :height="tableHeight()" :data="brandList"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column type="index" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.computerName')" prop="name" align="center" />
      <el-table-column :label="$t('bike.computer.model')" prop="model" align="center" />
      <el-table-column :label="$t('bike.bind.bindType')" prop="bindType" align="center">
        <template slot-scope="{ row }">
          {{ bindTypeList[row.bindType] }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.imgs')" prop="img" align="center">
        <template v-slot="{ row }">
          <preview-img :imgUrl="row.img" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.brand.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.brand.createBy')" align="center" prop="createBy" />
      <el-table-column :label="$t('bike.brand.createTime')" align="center" sortable prop="createTime">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.brand.operation')" align="center" class-name="small-padding fixed-width"
        width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row)">
            {{ $t("bike.brand.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body center>
      <el-form ref="form" label-position="top" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('bike.bike.computerName')" prop="name">
          <el-input v-model.trim="form.name" clearable
            :placeholder="$t('bike.info.input') + $t('bike.bike.computerName')" />
        </el-form-item>
        <el-form-item :label="$t('bike.computer.model')" prop="model">
          <el-input v-model.trim="form.model" clearable
            :placeholder="$t('bike.info.input') + $t('bike.computer.model')" />
        </el-form-item>
        <el-form-item :label="$t('bike.bind.bindType')" prop="bindType">
          <el-radio-group v-model="form.bindType">
            <el-radio :label="key" v-for="(value, key) in bindTypeList" :key="key">
              {{ value }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :label="$t('discover.posted.imgs')" prop="img">
          <el-upload-sortable v-model="form.img" :imgW="80" :imgH="80" :isLimit="1" :max="1" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("bike.brand._confirm") }}
        </el-button>
        <el-button @click="open = false">
          {{ $t("bike.brand._cancel") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  computerBindAdd,
  computerBindUpdate,
  computerBindAuth,
  computerBindList
} from "@/api/bike/computerBind";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      aFn: computerBindAuth,
      // 用户表格数据
      brandList: [],
      bindTypeList: {
        1: this.$t("bike.bind.ScanCode"),
        2: this.$t("bike.bind.bluetootCh")
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined
      },
      // 表单参数
      form: { bindType: "1" },
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message:
              this.$t("bike.info.input") + this.$t("bike.bike.computerName"),
            trigger: "blur"
          }
        ],
        model: [
          {
            required: true,
            message:
              this.$t("bike.info.input") + this.$t("bike.computer.model"),
            trigger: "blur"
          }
        ],
        img: [
          {
            required: true,
            message: this.$t("base.feedback.pleaseUploadPictures"),
            trigger: "blur"
          }
        ],
        bindType: [
          {
            required: true,
            message: this.$t("bike.info.select"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    "form.img"(val) {
      if (val) {
        this.$refs["form"].clearValidate("img");
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      computerBindList(this.queryParams)
        .then(response => {
          this.brandList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 表单重置
    reset() {
      this.form = {
        bindType: "1"
      };
      this.resetForm("form");
    },
    handleAdd() {
      this.open = true;
      this.title = this.$t("system.computer.handleAdd");
      this.reset();
    },
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = this.$t("system.computer.handleUpdate");
      this.form = Object.assign({}, row);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.id !== undefined) {
            computerBindUpdate(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.brand.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            computerBindAdd(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("bike.brand.addSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        }
      });
    }
  }
};
</script>
