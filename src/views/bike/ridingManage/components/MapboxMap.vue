<template>
  <div class="mapbox-container">
    <!-- 地图容器 -->
    <div :id="mapContainerId" class="map-container"></div>

    <!-- 搜索框 -->
    <div class="map-search">
      <el-autocomplete v-model="searchQuery" :fetch-suggestions="searchPlaces" :placeholder="t('map.searchPlaceholder')"
        class="search-input" :trigger-on-focus="false" @select="onPlaceSelected" @keyup.enter.native="onSearchEnter"
        clearable :loading="searchLoading">
        <template slot="prepend">
          <i class="el-icon-search"></i>
        </template>
        <template slot-scope="{ item }">
          <div class="search-suggestion-item">
            <div class="place-name">{{ item.value }}</div>
            <div class="place-address">{{ item.address }}</div>
          </div>
        </template>
      </el-autocomplete>
    </div>

    <!-- 控制面板 - 预览模式下隐藏 -->
    <div class="map-controls">
      <el-button v-if="!previewMode" type="primary" size="small" icon="el-icon-plus" @click="addWaypoint"
        :disabled="!isMapLoaded">
        {{ t('map.addWaypoint') }}
      </el-button v-if="!previewMode">
      <el-button type="warning" size="small" icon="el-icon-delete" @click="clearAllWaypoints"
        :disabled="waypoints.length === 0">
        {{ t('map.clearAll') }}
      </el-button>
      <el-button type="info" size="small" icon="el-icon-refresh" @click="resetMap" :disabled="!isMapLoaded">
        {{ t('map.reset') }}
      </el-button>
    </div>

    <!-- 途径点列表 -->
    <div class="waypoints-panel" v-if="waypoints.length > 0">
      <div class="panel-header">
        <h4>{{ t('map.waypointsList') }} ({{ waypoints.length }})</h4>
      </div>
      <div class="waypoints-list">
        <div v-for="(waypoint, index) in waypoints" :key="waypoint.id" class="waypoint-item"
          :class="{ 'active': selectedWaypointIndex === index }" @click="selectWaypoint(index)">
          <div class="waypoint-info" style="display: flex; align-items: center;">
            <div class="waypoint-index"
              style="width: 28px; text-align: right; color: #1890ff; font-weight: bold; margin-right: 8px;">
              {{ index + 1 }}.
            </div>
            <div>
              <div class="waypoint-name">
                {{ waypoint.name || `${t('map.waypoint')} ${index + 1}` }}
              </div>
              <div class="waypoint-coords" style="font-size: 12px; color: #909399;">
                {{ waypoint.latitude.toFixed(6) }}, {{ waypoint.longitude.toFixed(6) }}
              </div>
            </div>
          </div>
          <div v-if="!previewMode" class="waypoint-actions">
            <el-button v-if="!waypoint.name" type="text" size="mini" icon="el-icon-location"
              @click.stop="updateWaypointPlaceName(index)" title="获取地名">
              获取地名
            </el-button>
            <el-button type="text" size="mini" icon="el-icon-edit" @click.stop="editWaypoint(index)">
              {{ t('common.edit') }}
            </el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" class="danger-text"
              @click.stop="removeWaypoint(index)">
              {{ t('common.delete') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 途径点编辑对话框 -->
    <el-dialog :title="isEditWaypoint ? t('map.editWaypoint') : t('map.addWaypoint')"
      :visible.sync="waypointDialogVisible" width="500px" :close-on-click-modal="false" append-to-body top="15vh">
      <el-form ref="waypointForm" :model="waypointForm" :rules="waypointRules" label-width="120px">
        <el-form-item :label="t('map.waypointName')" prop="name">
          <el-input v-model="waypointForm.name" :placeholder="t('map.waypointNamePlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('map.latitude')" prop="latitude">
          <el-input-number v-model="waypointForm.latitude" :precision="6" :step="0.000001" style="width: 100%"
            @change="onCoordinateChange" />
        </el-form-item>
        <el-form-item :label="t('map.longitude')" prop="longitude">
          <el-input-number v-model="waypointForm.longitude" :precision="6" :step="0.000001" style="width: 100%"
            @change="onCoordinateChange" />
        </el-form-item>
        <el-form-item>
          <el-button type="text" size="small" icon="el-icon-location" @click="getPlaceNameForForm"
            :disabled="!waypointForm.longitude || !waypointForm.latitude">
            {{ t('map.getPlaceNameForForm') }}
          </el-button>
        </el-form-item>
        <el-form-item :label="t('map.sort')" prop="sort">
          <el-input-number v-model="waypointForm.sort" :min="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="waypointDialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="saveWaypoint">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 使用 CDN 版本的 Mapbox GL JS
// import mapboxgl from 'mapbox-gl'
// import 'mapbox-gl/dist/mapbox-gl.css'

export default {
  name: 'MapboxMap',
  props: {
    // 初始途径点数据
    initialWaypoints: {
      type: Array,
      default: () => []
    },
    // 活动ID
    activityId: {
      type: String,
      default: ''
    },
    // 地图中心点
    center: {
      type: Object,
      default: () => ({ lng: 116.3974, lat: 39.9093 }) // 默认北京
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10
    },
    // 预览模式 - 禁用所有编辑功能
    previewMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      map: null,
      mapboxgl: null, // 存储 mapboxgl 对象
      mapContainerId: 'mapbox-map-' + Math.random().toString(36).substr(2, 9), // 动态生成唯一ID
      isMapLoaded: false,
      waypoints: [],
      selectedWaypointIndex: -1,
      waypointDialogVisible: false,
      isEditWaypoint: false,
      editingWaypointIndex: -1,
      waypointForm: {
        id: '',
        name: '',
        latitude: 0,
        longitude: 0,
        sort: 0,
        activityId: '',
        createTime: null
      },
      waypointRules: {
        name: [
          { required: true, message: this.t('validation.waypointNameRequired'), trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: this.t('validation.latitudeRequired'), trigger: 'blur' }
        ],
        longitude: [
          { required: true, message: this.t('validation.longitudeRequired'), trigger: 'blur' }
        ]
      },
      markers: [], // 存储地图标记
      coordinateChangeTimer: null, // 坐标改变防抖定时器
      resizeTimer: null, // 窗口大小变化防抖定时器
      isUpdatingWaypoints: false, // 防止watch循环的标志位
      // Mapbox配置
      mapboxConfig: {
        // 注意：这里需要使用公共访问令牌(pk.*)，不是秘密令牌(sk.*)
        // 建议将此令牌移至环境变量中以提高安全性
        accessToken: process.env.VUE_APP_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYWRkbW90b3IiLCJhIjoiY2x6dGltaDFzMmgybzJtb2NtNmsxYTIxaCJ9.b3yBd9nkM3EDNzebk_gVDA',
        style: 'mapbox://styles/mapbox/streets-v11'
      },
      // 新增自适应相关
      lastVisible: false,
      visibilityCheckTimer: null,
      // 字段模板
      waypointTemplate: {
        activityId: '',
        createTime: 0,
        id: '',
        latitude: 0,
        longitude: 0,
        name: '',
        sort: 0
      },
      lastRenderedWaypoints: [],
      searchQuery: '',
      searchLoading: false,
      currentLocationMarker: null // 当前位置标记
    }
  },
  watch: {
    waypoints: {
      handler(newVal) {
        // 只在地图加载且数据有效时处理
        if (!this.isMapLoaded) return;
        if (!Array.isArray(newVal) || newVal.length === 0) {
          this.clearMap(); // 使用新的 clearMap 方法
          return;
        }
        // 防止无限循环
        if (this.isSameWaypoints(newVal, this.lastRenderedWaypoints)) return;
        this.loadWaypoints(newVal);
        this.lastRenderedWaypoints = JSON.parse(JSON.stringify(newVal));
      },
      deep: true,
      immediate: true
    },
    // 监听initialWaypoints变化，自动回显
    initialWaypoints: {
      handler(newVal) {
        if (!this.previewMode) {
          this.loadWaypoints(newVal);
        }
      },
      deep: true
    },
    // 监听全局语言变化
    '$i18n.locale': function (newLang) {
      this.setMapLanguage(newLang)
    },
    // 监听activityId变化，预览模式下自动请求活动途径点
    activityId: function (newId) {
      if (this.previewMode && newId) {
        this.fetchActivityWaypoints(newId)
      }
    }
  },
  mounted() {
    this.loadMapboxGL()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)

    // 为弹窗按钮创建全局函数
    var self = this
    window.editWaypointFromPopup = function (index) {
      self.editWaypoint(index)
    }
    // 启动可见性检测
    this.startVisibilityCheck()
    // 地图加载后设置地名语言
    this.$nextTick(() => {
      setTimeout(() => {
        this.setMapLanguage(this.$i18n.locale)
      }, 500)
    })
    // 预览模式下自动请求活动途径点
    if (this.previewMode && this.activityId) {
      this.fetchActivityWaypoints(this.activityId)
    }
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
    // 清理当前位置标记
    if (this.currentLocationMarker) {
      this.currentLocationMarker.remove()
    }
    // 清理定时器
    if (this.coordinateChangeTimer) {
      clearTimeout(this.coordinateChangeTimer)
    }
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize)
    // 清理全局函数
    if (window.editWaypointFromPopup) {
      delete window.editWaypointFromPopup
    }
    // 停止可见性检测
    this.stopVisibilityCheck()
  },
  methods: {
    // 国际化文本获取
    t(key) {
      // 优先用全局i18n
      if (this.$t) return this.$t(key)
      // 兜底本地字典
      const translations = {
        'map.addWaypoint': '添加途径点',
        'map.clearAll': '清空所有',
        'map.reset': '重置地图',
        'map.waypointsList': '途径点列表',
        'map.waypoint': '途径点',
        'map.editWaypoint': '编辑途径点',
        'map.waypointName': '途径点名称',
        'map.waypointNamePlaceholder': '请输入途径点名称',
        'map.latitude': '纬度',
        'map.longitude': '经度',
        'map.sort': '排序',
        'validation.waypointNameRequired': '途径点名称不能为空',
        'validation.latitudeRequired': '纬度不能为空',
        'validation.longitudeRequired': '经度不能为空',
        'common.edit': '编辑',
        'common.delete': '删除',
        'common.save': '保存',
        'common.cancel': '取消',
        'map.previewMode': '地图预览模式',
        'map.searchPlaceholder': '搜索地点...',
        'map.addWaypointConfirm': '是否将"{name}"添加为途径点？',
        'map.addWaypointTitle': '添加途径点',
        'map.add': '添加',
        'map.locationFound': '已定位到: {name}',
        'map.noResultsFound': '未找到相关地点',
        'map.invalidLocation': '无效的地点信息'
      }
      return translations[key] || key
    },

    // 处理窗口大小变化
    handleResize: function () {
      var self = this
      if (this.map && this.isMapLoaded) {
        // 使用防抖避免频繁调用
        clearTimeout(this.resizeTimer)
        this.resizeTimer = setTimeout(function () {
          self.map.resize()
        }, 100)
      }
    },

    // 动态加载 Mapbox GL JS
    loadMapboxGL() {

      // 检查是否已经加载
      if (window.mapboxgl) {
        this.mapboxgl = window.mapboxgl
        this.initMap()
        this.loadInitialWaypoints()
        return
      }

      // 加载 CSS
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
      document.head.appendChild(link)

      // 加载 JS
      const script = document.createElement('script')
      script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'
      script.onload = () => {
        this.mapboxgl = window.mapboxgl
        this.initMap()
        this.loadInitialWaypoints()
      }
      script.onerror = (error) => {
        this.$message.error('地图库加载失败，请检查网络连接')
      }
      document.head.appendChild(script)
    },

    // 初始化地图
    initMap() {
      var self = this
      try {
        console.log('开始初始化 Mapbox 地图...')
        console.log('Access Token:', this.mapboxConfig.accessToken)

        this.mapboxgl.accessToken = this.mapboxConfig.accessToken

        this.map = new this.mapboxgl.Map({
          container: this.mapContainerId,
          style: this.mapboxConfig.style,
          center: [this.center.lng, this.center.lat],
          zoom: this.zoom,
          // 添加这些配置来改善地图交互体验
          pitchWithRotate: false,
          dragRotate: false,
          touchZoomRotate: false,
          attributionControl: false,
          // 优化拖拽性能
          antialias: true,
          fadeDuration: 0
        })

        // 添加地图控件
        this.map.addControl(new this.mapboxgl.NavigationControl(), 'top-right')
        this.map.addControl(new this.mapboxgl.FullscreenControl(), 'top-right')
        this.map.addControl(new this.mapboxgl.GeolocateControl({
          positionOptions: {
            enableHighAccuracy: true
          },
          trackUserLocation: true
        }), 'top-right')

        // 地图加载完成
        this.map.on('load', async function () {
          console.log('地图加载完成 - 容器ID:', self.mapContainerId, '预览模式:', self.previewMode)
          self.isMapLoaded = true
          self.$emit('map-loaded')

          // 地图加载完成后重新计算尺寸
          setTimeout(async function () {
            self.map.resize()
            // 地图加载完成后，尝试加载途径点数据
            if (self.waypoints.length > 0) {
              console.log('地图已加载，开始刷新标记')
              self.clearAllMarkers()
              self.waypoints.forEach(function (waypoint, index) {
                self.addMarker(waypoint, index)
              }.bind(this))
              // 刷新真实规划路线
              await self.refreshRouteLine()
              // 地图加载完成后自动定位到轨迹视图
              self.fitBoundsToWaypoints()
            } else {
              // 没有轨迹时尝试地理定位
              self.tryGeolocation()
            }
          }, 100)
        })

        // 监听地图移动开始，减少不必要的重绘
        this.map.on('movestart', function () {
          self.map.getContainer().style.cursor = 'grabbing'
        })

        // 监听地图移动结束
        this.map.on('moveend', function () {
          self.map.getContainer().style.cursor = 'grab'
        })

        // 地图错误处理
        this.map.on('error', function (e) {
          console.error('地图加载错误:', e)
          self.$message.error('地图加载失败，请检查网络连接和 Access Token')
        })

        // 点击地图添加途径点 - 预览模式下禁用
        this.map.on('click', function (e) {
          if (self.isMapLoaded && !self.previewMode) {
            self.addWaypointAtLocation(e.lngLat)
          }
        })

        console.log('地图初始化完成')
      } catch (error) {
        console.error('地图初始化失败:', error)
        this.$message.error('地图初始化失败: ' + error.message)
      }
    },

    // 加载途径点数据（通用方法）
    loadWaypoints: async function (waypointsData) {
      var sourceData = waypointsData || this.initialWaypoints
      console.log('加载途径点数据 - 容器ID:', this.mapContainerId, '数据长度:', sourceData ? sourceData.length : 0, '地图已加载:', this.isMapLoaded)

      if (sourceData && sourceData.length > 0) {
        // 设置标志位防止循环调用
        this.isUpdatingWaypoints = true

        this.waypoints = []
        // 复制数据并补全字段
        for (var i = 0; i < sourceData.length; i++) {
          this.waypoints.push(Object.assign({}, this.waypointTemplate, sourceData[i]))
        }

        // 确保排序正确
        this.sortWaypoints()

        // 如果地图已加载，则刷新标记
        if (this.isMapLoaded) {
          console.log('地图已加载，开始刷新标记')
          this.clearAllMarkers()
          this.waypoints.forEach(function (waypoint, index) {
            this.addMarker(waypoint, index)
          }.bind(this))
          // 刷新真实规划路线
          await this.refreshRouteLine()
          // 自动定位到轨迹视图
          this.fitBoundsToWaypoints()
        } else {
          console.log('地图未加载，等待地图加载完成')
        }

        // 清除标志位
        this.isUpdatingWaypoints = false
      } else {
        console.log('没有途径点数据需要加载，尝试地理定位')
        // 清空所有标记
        this.waypoints = []
        this.clearAllMarkers();
        // 清空路线
        await this.refreshRouteLine()
        // 没有轨迹时使用地理定位
        if (this.isMapLoaded) {
          this.tryGeolocation()
        }
      }
    },

    // 加载初始途径点数据
    loadInitialWaypoints: function () {
      this.loadWaypoints()
    },

    // 在指定位置添加途径点
    addWaypointAtLocation: function (lngLat) {
      var self = this
      var loading = this.$loading({
        lock: true,
        text: '正在获取地址信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 先获取地名，然后打开编辑对话框让用户确认
      this.getPlaceNameFromCoordinates(lngLat.lng, lngLat.lat)
        .then(function (placeName) {
          loading.close()
          // 设置表单数据但不添加到列表
          self.waypointForm = {
            id: self.generateId(),
            name: placeName || '',
            latitude: lngLat.lat,
            longitude: lngLat.lng,
            sort: self.waypoints.length,
            activityId: self.activityId,
            createTime: Date.now()
          }

          // 标记为新建途径点
          self.isEditWaypoint = false
          self.editingWaypointIndex = -1
          self.waypointDialogVisible = true
        })
        .catch(function (error) {
          console.error('获取地址失败:', error)
          loading.close()

          // 即使获取地名失败也先打开对话框
          self.waypointForm = {
            id: self.generateId(),
            name: '',
            latitude: lngLat.lat,
            longitude: lngLat.lng,
            sort: self.waypoints.length,
            activityId: self.activityId,
            createTime: Date.now()
          }

          self.isEditWaypoint = false
          self.editingWaypointIndex = -1
          self.waypointDialogVisible = true
          self.$message.warning('无法获取地址信息，请手动输入名称')
        })
    },

    // 添加途径点（手动按钮）
    addWaypoint: function () {
      var center = this.map.getCenter()
      this.addWaypointAtLocation(center)
    },

    // 添加地图标记 - 官方API + 自定义序号显示
    addMarker: function (waypoint, index) {
      var self = this

      // 创建简单的序号标记元素
      var el = document.createElement('div')
      el.style.cssText = `
        width: 30px; 
        height: 30px; 
        background: #1890ff; 
        color: white; 
        border-radius: 50%; 
        display: flex; 
        align-items: center; 
        justify-content: center; 
        font-weight: bold; 
        font-size: 14px; 
        cursor: pointer; 
        border: 2px solid white; 
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        transition: background-color 0.2s ease, box-shadow 0.2s ease;
      `
      el.textContent = index + 1

      // 使用官方Marker API + 自定义元素，设置正确的锚点
      var marker = new this.mapboxgl.Marker({
        element: el,
        draggable: false,
        anchor: 'center'  // 确保锚点为中心
      })
        .setLngLat([waypoint.longitude, waypoint.latitude])
        .addTo(this.map)

      // 添加弹窗
      var popup = new this.mapboxgl.Popup({
        offset: 25,
        closeButton: true,
        closeOnClick: false
      })

      // 根据是否为预览模式显示不同的弹窗内容
      var popupContent = '<div style="padding: 8px; font-size: 12px;">' +
        '<div style="margin-bottom: 8px;"><strong>' + (waypoint.name || '途径点 ' + (index + 1)) + '</strong></div>' +
        '<div style="margin-bottom: 8px; color: #666;">坐标: ' + waypoint.longitude.toFixed(6) + ', ' + waypoint.latitude.toFixed(6) + '</div>'

      if (!this.previewMode) {
        popupContent += '<div style="text-align: center;"><button onclick="window.editWaypointFromPopup(' + index + ')" style="background: #1890ff; color: white; border: none; padding: 4px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">编辑</button></div>'
      } else {
        popupContent += '<div style="text-align: center; color: #666; font-size: 11px;">预览模式</div>'
      }

      popupContent += '</div>'
      popup.setHTML(popupContent)

      marker.setPopup(popup)

      // 创建悬停信息提示框
      var hoverTooltip = document.createElement('div')
      hoverTooltip.className = 'marker-hover-tooltip'
      hoverTooltip.style.cssText = `
        position: fixed !important;
        background: rgba(0, 0, 0, 0.9) !important;
        color: white !important;
        padding: 10px 14px !important;
        border-radius: 8px !important;
        font-size: 12px !important;
        white-space: nowrap !important;
        z-index: 9999 !important;
        pointer-events: none !important;
        opacity: 0;
        transition: opacity 0.3s ease !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
        backdrop-filter: blur(8px) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        max-width: 250px !important;
        visibility: hidden;
      `

      // 本地格式化时间函数
      var formatTime = function (timestamp) {
        if (!timestamp) return '';
        // 处理秒级时间戳转换为毫秒
        const msTimestamp = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
        const date = new Date(msTimestamp);
        // 格式化日期为 YYYY-MM-DD HH:mm:ss
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      // 悬停信息内容
      var tooltipContent = `
        <div style="margin-bottom: 6px; font-weight: 600; color: #fff; font-size: 13px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 4px;">
          📍 ${waypoint.name || self.t('map.waypoint') + ' ' + (index + 1)}
        </div>
        <div style="color: #e0e0e0; font-size: 11px; line-height: 1.4;">
          <div style="margin-bottom: 2px;">🔢 ${self.t('map.index')}: ${index + 1}</div>
          <div style="margin-bottom: 2px;">📊 ${self.t('map.sort')}: ${waypoint.sort !== undefined ? waypoint.sort : self.t('map.notSet')}</div>
          <div style="margin-bottom: 2px;">🌐 ${self.t('map.longitude')}: ${waypoint.longitude.toFixed(6)}</div>
          <div style="margin-bottom: 2px;">🌐 ${self.t('map.latitude')}: ${waypoint.latitude.toFixed(6)}</div>
          ${waypoint.createTime ? `<div style="margin-bottom: 2px;">🕒 ${self.t('map.created')}: ${formatTime(waypoint.createTime)}</div>` : ''}
        </div>
      `
      hoverTooltip.innerHTML = tooltipContent
      document.body.appendChild(hoverTooltip)

      // 点击标记事件 - 预览模式下禁用编辑，只允许查看
      el.addEventListener('click', function (e) {
        e.stopPropagation()  // 阻止事件冒泡到地图
        if (!self.previewMode) {
          self.editWaypoint(index)  // 直接调用编辑功能
        } else {
          // 预览模式下只选中途径点
          self.selectWaypoint(index)
        }
      })

      // 悬停效果 - 显示详细信息
      var hoverTimeout
      el.addEventListener('mouseenter', function (e) {
        console.log('Marker hover enter:', index, 'element visible:', el.offsetParent !== null)

        // 清除之前的延迟
        clearTimeout(hoverTimeout)

        // 延迟显示提示框，避免快速移动时的闪烁
        hoverTimeout = setTimeout(function () {
          // 检查标记是否仍然可见
          if (!el.offsetParent) {
            console.warn('Marker element became invisible during hover:', index)
            return
          }

          console.log('Showing tooltip for marker:', index)

          // 更新标记样式 - 只改变颜色和阴影，避免影响布局
          el.style.background = '#40a9ff'  // 更亮的蓝色
          el.style.boxShadow = '0 4px 12px rgba(0,0,0,0.4)'  // 增强阴影效果

          // 显示悬停提示框
          hoverTooltip.style.visibility = 'visible'
          hoverTooltip.style.opacity = '1'

          // 计算提示框位置
          var rect = el.getBoundingClientRect()
          console.log('Marker rect:', rect)

          // 默认显示在标记上方
          var tooltipTop = rect.top - hoverTooltip.offsetHeight - 10
          var tooltipLeft = rect.left + rect.width / 2 - hoverTooltip.offsetWidth / 2

          console.log('Initial tooltip position:', tooltipLeft, tooltipTop)

          // 检查上方空间是否足够
          if (tooltipTop < 10) {
            // 上方空间不足，显示在下方
            tooltipTop = rect.bottom + 10
            hoverTooltip.classList.add('tooltip-above')
          } else {
            hoverTooltip.classList.remove('tooltip-above')
          }

          hoverTooltip.style.left = tooltipLeft + 'px'
          hoverTooltip.style.top = tooltipTop + 'px'

          // 确保提示框不超出视窗左右边界
          var tooltipRect = hoverTooltip.getBoundingClientRect()
          console.log('Tooltip rect:', tooltipRect)

          if (tooltipRect.left < 10) {
            hoverTooltip.style.left = '10px'
          }
          if (tooltipRect.right > window.innerWidth - 10) {
            hoverTooltip.style.left = (window.innerWidth - tooltipRect.width - 10) + 'px'
          }

          console.log('Final tooltip position:', hoverTooltip.style.left, hoverTooltip.style.top)
        }, 150) // 150ms 延迟
      })

      el.addEventListener('mouseleave', function () {
        console.log('Marker hover leave:', index)

        // 清除延迟
        clearTimeout(hoverTimeout)

        // 恢复标记样式
        el.style.background = '#1890ff'  // 原始蓝色
        el.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)'  // 原始阴影

        // 隐藏悬停提示框
        hoverTooltip.style.opacity = '0'
        setTimeout(function () {
          hoverTooltip.style.visibility = 'hidden'
        }, 300) // 等待过渡动画完成
      })

      // 存储标记和提示框的引用，以便后续清理
      this.markers.push({
        marker: marker,
        tooltip: hoverTooltip
      })
    },

    // 选择途径点
    selectWaypoint(index) {
      this.selectedWaypointIndex = index
      const waypoint = this.waypoints[index]

      // 移动地图到该点
      this.map.flyTo({
        center: [waypoint.longitude, waypoint.latitude],
        zoom: 15,
        duration: 1000
      })
    },

    // 编辑途径点
    editWaypoint: function (index) {
      this.isEditWaypoint = true
      this.editingWaypointIndex = index

      // 复制对象属性
      var waypoint = this.waypoints[index]
      this.waypointForm = {}
      for (var key in waypoint) {
        this.waypointForm[key] = waypoint[key]
      }

      this.waypointDialogVisible = true
    },

    // 删除途径点
    removeWaypoint: async function (index) {
      var self = this
      this.$confirm('确定要删除这个途径点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async function () {
        // 设置标志位防止循环调用
        self.isUpdatingWaypoints = true

        // 移除地图标记
        if (self.markers[index]) {
          self.markers[index].marker.remove()
          // 清理悬停提示框
          if (self.markers[index].tooltip && self.markers[index].tooltip.parentNode) {
            self.markers[index].tooltip.parentNode.removeChild(self.markers[index].tooltip)
          }
          self.markers.splice(index, 1)
        }

        // 移除数据
        self.waypoints.splice(index, 1)

        // 重新排序和渲染
        self.sortWaypoints()
        self.clearAllMarkers()
        self.waypoints.forEach(function (waypoint, index) {
          self.addMarker(waypoint, index)
        })
        // 关键：刷新骑行路线
        await self.refreshRouteLine()

        // 清除标志位
        self.isUpdatingWaypoints = false
        self.$emit('waypoints-change', self.waypoints)
        self.$message.success('删除成功')
      })
    },

    // 保存途径点
    saveWaypoint: async function () {
      var self = this
      this.$refs.waypointForm.validate(async function (valid) {
        if (valid) {
          // 设置标志位防止循环调用
          self.isUpdatingWaypoints = true

          if (self.isEditWaypoint) {
            // 更新现有途径点
            var oldWaypoint = self.waypoints[self.editingWaypointIndex]
            var oldSort = oldWaypoint.sort

            // 更新所有属性，补全字段
            for (var key in self.waypointForm) {
              oldWaypoint[key] = self.waypointForm[key]
            }
            for (var k in self.waypointTemplate) {
              if (!(k in oldWaypoint)) oldWaypoint[k] = self.waypointTemplate[k]
            }

            // 如果排序发生了变化，需要重新排序整个数组
            if (oldSort !== self.waypointForm.sort) {
              self.sortWaypoints(false)
            }

            // 重新渲染所有标记以更新序号，但不重新排序
            self.clearAllMarkers()
            self.waypoints.forEach(function (waypoint, index) {
              self.addMarker(waypoint, index)
            })
            // 关键：刷新骑行路线
            await self.refreshRouteLine()
          } else {
            // 添加新途径点，补全字段
            var newWaypoint = Object.assign({}, self.waypointTemplate, self.waypointForm)
            self.waypoints.push(newWaypoint)

            // 新增后也需要排序
            self.sortWaypoints()
            await self.refreshMarkers()
          }

          // 清除标志位
          self.isUpdatingWaypoints = false
          self.waypointDialogVisible = false
          // self.$message.success(self.isEditWaypoint ? '更新成功' : '添加成功')
          // self.$emit('waypoints-change', self.waypoints)
        }
      })
    },

    // 清空所有途径点
    clearAllWaypoints: function () {
      var self = this
      this.$confirm('确定要清空所有途径点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        // 设置标志位防止循环调用
        self.isUpdatingWaypoints = true
        self.waypoints = []
        self.clearAllMarkers()
        self.refreshRouteLine() // 添加路线清除
        // 清除标志位
        self.isUpdatingWaypoints = false
        self.$message.success('清空成功')
      })
    },

    // 重置地图
    resetMap() {
      this.map.flyTo({
        center: [this.center.lng, this.center.lat],
        zoom: this.zoom,
        duration: 1000
      })
    },

    // 清空所有标记
    clearAllMarkers() {
      this.markers.forEach(item => {
        item.marker.remove()
        // 清理悬停提示框
        if (item.tooltip && item.tooltip.parentNode) {
          item.tooltip.parentNode.removeChild(item.tooltip)
        }
      })
      this.markers = []
    },

    // 清空地图（标记和路线）
    async clearMap() {
      this.clearAllMarkers()
      await this.refreshRouteLine()
    },

    // 刷新标记
    async refreshMarkers() {
      this.clearAllMarkers()
      // 确保waypoints已按sort字段排序后再添加标记
      this.sortWaypoints()
      this.waypoints.forEach(function (waypoint, index) {
        this.addMarker(waypoint, index)
      }.bind(this))
      // 刷新真实规划路线
      await this.refreshRouteLine()
    },

    // 按排序字段对途径点进行排序
    sortWaypoints: function (reassignIndex) {
      // reassignIndex: 是否重新分配索引，默认为true
      reassignIndex = reassignIndex !== false

      this.waypoints.sort(function (a, b) {
        // 按sort字段升序排列
        var sortA = typeof a.sort === 'number' ? a.sort : 0
        var sortB = typeof b.sort === 'number' ? b.sort : 0
        return sortA - sortB
      })

      // 如果需要重新分配索引确保连续性
      if (reassignIndex) {
        for (var i = 0; i < this.waypoints.length; i++) {
          this.waypoints[i].sort = i
        }
      }

      console.log('途径点已重新排序:', this.waypoints.map(function (w, i) {
        return { index: i, name: w.name || '途径点' + (i + 1), sort: w.sort }
      }))
    },

    // 为现有途径点获取地名
    updateWaypointPlaceName: function (index) {
      var self = this
      var waypoint = this.waypoints[index]
      if (waypoint && !waypoint.name) {
        this.getPlaceNameFromCoordinates(waypoint.longitude, waypoint.latitude)
          .then(function (placeName) {
            if (placeName) {
              waypoint.name = placeName
              // 只重新渲染标记，不重新排序
              self.clearAllMarkers()
              self.waypoints.forEach(function (waypoint, index) {
                self.addMarker(waypoint, index)
              })
            }
          })
          .catch(function (error) {
            console.error('更新地名失败:', error)
          })
      }
    },

    // 生成唯一ID
    generateId() {
      return 'waypoint_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    // 通过坐标获取地名（逆地理编码）
    getPlaceNameFromCoordinates: function (lng, lat) {
      var self = this
      var accessToken = this.mapboxConfig.accessToken
      var lang = this.$i18n && this.$i18n.locale ? this.$i18n.locale : 'zh'
      var langParam = lang === 'en' ? 'en' : 'zh-CN'
      function tryDetailedSearch() {
        var url = 'https://api.mapbox.com/geocoding/v5/mapbox.places/' + lng + ',' + lat + '.json?access_token=' + accessToken + '&language=' + langParam + '&types=address,poi'
        return fetch(url)
          .then(function (response) { return response.json() })
          .then(function (data) {
            if (data.features && data.features.length > 0) {
              var detailedPlace = self.selectBestFeature(data.features, ['address', 'poi'])
              if (detailedPlace) {
                return self.formatPlaceName(detailedPlace)
              }
            }
            return null
          })
      }
      function tryRegionalSearch() {
        var url = 'https://api.mapbox.com/geocoding/v5/mapbox.places/' + lng + ',' + lat + '.json?access_token=' + accessToken + '&language=' + langParam + '&types=neighborhood,locality,place'
        return fetch(url)
          .then(function (response) { return response.json() })
          .then(function (data) {
            if (data.features && data.features.length > 0) {
              var generalPlace = self.selectBestFeature(data.features, ['neighborhood', 'locality', 'place'])
              if (generalPlace) {
                return self.formatPlaceName(generalPlace)
              }
            }
            return null
          })
      }
      function tryGeneralSearch() {
        var url = 'https://api.mapbox.com/geocoding/v5/mapbox.places/' + lng + ',' + lat + '.json?access_token=' + accessToken + '&language=' + langParam
        return fetch(url)
          .then(function (response) { return response.json() })
          .then(function (data) {
            if (data.features && data.features.length > 0) {
              var anyPlace = self.selectBestFeature(data.features)
              if (anyPlace) {
                return self.formatPlaceName(anyPlace)
              }
            }
            return null
          })
      }
      return tryDetailedSearch()
        .then(function (result) {
          if (result) return result
          return tryRegionalSearch()
        })
        .then(function (result) {
          if (result) return result
          return tryGeneralSearch()
        })
        .catch(function (error) {
          console.error('获取地名失败:', error)
          return null
        })
    },

    // 选择最佳特征
    selectBestFeature: function (features, allowedTypes) {
      if (!features || features.length === 0) {
        return null
      }

      var priorityTypes = {
        'address': 1,
        'poi': 2,
        'neighborhood': 3,
        'locality': 4,
        'place': 5,
        'district': 6,
        'region': 7,
        'country': 8
      }

      var bestFeature = null
      var bestPriority = 999

      for (var i = 0; i < features.length; i++) {
        var feature = features[i]
        var featureTypes = feature.place_type || []

        // 检查类型是否匹配
        if (allowedTypes) {
          var typeMatched = false
          for (var j = 0; j < featureTypes.length; j++) {
            if (allowedTypes.indexOf(featureTypes[j]) !== -1) {
              typeMatched = true
              break
            }
          }
          if (!typeMatched) {
            continue
          }
        }

        // 计算优先级
        var currentPriority = 999
        for (var k = 0; k < featureTypes.length; k++) {
          var type = featureTypes[k]
          if (priorityTypes[type] && priorityTypes[type] < currentPriority) {
            currentPriority = priorityTypes[type]
          }
        }

        // 选择最高优先级的特征
        if (currentPriority < bestPriority) {
          bestPriority = currentPriority
          bestFeature = feature
        }
      }

      return bestFeature
    },

    // 格式化地名
    formatPlaceName: function (feature) {
      if (!feature) return null

      var placeName = this.extractBestPlaceName(feature)

      if (placeName) {
        // 移除国家信息
        placeName = placeName.replace(/,?\s*中国$/, '').replace(/,?\s*China$/, '')
        // 简化地址
        placeName = this.simplifyAddress(placeName)
      }

      return placeName
    },

    // 提取最佳地名
    extractBestPlaceName: function (feature) {
      return feature.text_zh || feature.text || feature.place_name_zh || feature.place_name || null
    },

    // 简化地址信息
    simplifyAddress: function (address) {
      if (!address) return address

      // 移除邮编和国家信息
      address = address.replace(/^\d{6}\s*/, '')

      // 分割地址部分
      var parts = address.split(/[,，]/)
      if (parts.length > 1) {
        // 过滤有效部分
        var validParts = []
        for (var i = 0; i < parts.length; i++) {
          var part = parts[i].trim()
          if (part.length > 1 && part !== '中国' && part !== 'China' && !/^\d+$/.test(part)) {
            validParts.push(part)
          }
        }

        if (validParts.length > 0) {
          // 优先选择包含具体信息的部分
          for (var j = 0; j < validParts.length; j++) {
            if (validParts[j].indexOf('号') !== -1 ||
              validParts[j].indexOf('街') !== -1 ||
              validParts[j].indexOf('路') !== -1 ||
              validParts[j].indexOf('小区') !== -1) {
              return validParts[j]
            }
          }
          return validParts[0]
        }
      }

      return address
    },

    // 获取途径点数据
    getWaypoints() {
      return this.waypoints
    },

    // 设置途径点数据
    setWaypoints(waypoints) {
      this.waypoints = [...waypoints]
      this.refreshMarkers()
    },

    // 坐标改变时的处理
    onCoordinateChange: function () {
      var self = this
      clearTimeout(this.coordinateChangeTimer)
      this.coordinateChangeTimer = setTimeout(function () {
        if (!self.waypointForm.name && self.waypointForm.longitude && self.waypointForm.latitude) {
          self.getPlaceNameForForm()
        }
      }, 1000)
    },

    // 为表单获取地名
    getPlaceNameForForm: function () {
      var self = this
      if (!this.waypointForm.longitude || !this.waypointForm.latitude) {
        this.$message.warning('请先输入有效的经纬度坐标')
        return
      }

      var loading = this.$loading({
        target: '.el-dialog',
        lock: true,
        text: '正在获取地址信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.7)'
      })

      this.getPlaceNameFromCoordinates(this.waypointForm.longitude, this.waypointForm.latitude)
        .then(function (placeName) {
          if (placeName) {
            self.waypointForm.name = placeName
            self.$message.success('获取地名成功')
          } else {
            self.$message.warning('未找到对应的地址信息')
          }
          loading.close()
        })
        .catch(function (error) {
          console.error('获取地名失败:', error)
          self.$message.error('获取地名失败，请检查网络连接')
          loading.close()
        })
    },

    // 新增：弹窗可见性检测，自动resize地图
    startVisibilityCheck() {
      this.visibilityCheckTimer = setInterval(() => {
        const visible = !!this.$el.offsetParent;
        if (this.map && visible && !this.lastVisible) {
          this.map.resize();
        }
        this.lastVisible = visible;
      }, 300);
    },
    stopVisibilityCheck() {
      if (this.visibilityCheckTimer) {
        clearInterval(this.visibilityCheckTimer);
        this.visibilityCheckTimer = null;
      }
    },

    // 设置地图地名语言
    setMapLanguage(lang) {
      if (!this.map) return;
      // Mapbox官方样式的label层名通常以-label结尾
      const labelLayers = [
        'country-label', 'state-label', 'settlement-label', 'settlement-subdivision-label',
        'airport-label', 'poi-label', 'water-point-label', 'water-line-label',
        'natural-point-label', 'natural-line-label', 'waterway-label', 'road-label'
      ];
      labelLayers.forEach(layerId => {
        if (this.map.getLayer(layerId)) {
          this.map.setLayoutProperty(
            layerId,
            'text-field',
            ['get', lang === 'zh' ? 'name_zh' : 'name_en']
          );
        }
      });
    },
    // 预览模式下自动请求活动途径点
    fetchActivityWaypoints(id) {
      var self = this
      // 你可以根据实际API替换fetch为axios等
      fetch('/activity/info/' + id)
        .then(function (res) { return res.json() })
        .then(function (resp) {
          var arr = (resp.data && resp.data.list) ? resp.data.list : []
          self.loadWaypoints(arr)
        })
        .catch(function (err) {
          self.$message && self.$message.error('获取活动途径点失败')
        })
    },

    // 获取真实规划路线
    async getPlannedRouteLine() {
      if (!this.waypoints || this.waypoints.length < 2) return null;
      const coords = this.waypoints
        .filter(wp => typeof wp.longitude === 'number' && typeof wp.latitude === 'number')
        .sort((a, b) => (a.sort || 0) - (b.sort || 0))
        .map(wp => `${wp.longitude},${wp.latitude}`);
      if (coords.length < 2) return null;
      const accessToken = this.mapboxConfig.accessToken;
      // 可选：driving/walking/cycling
      const profile = 'cycling';
      const url = `https://api.mapbox.com/directions/v5/mapbox/${profile}/${coords.join(';')}?geometries=geojson&access_token=${accessToken}`;
      try {
        const resp = await fetch(url);
        const data = await resp.json();
        if (data.routes && data.routes[0] && data.routes[0].geometry) {
          return data.routes[0].geometry;
        }
      } catch (e) {
        console.error('获取规划路线失败', e);
      }
      return null;
    },

    // 刷新真实规划路线
    async refreshRouteLine() {
      if (!this.map) return;
      // 获取规划路线
      const geometry = await this.getPlannedRouteLine();
      if (!geometry || !geometry.coordinates || geometry.coordinates.length < 2) {
        // 没有路线时移除
        if (this.map.getLayer('route-line-layer')) {
          this.map.removeLayer('route-line-layer');
          this.map.removeSource('route-line');
        }
        return;
      }
      const geojson = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: geometry
          }
        ]
      };
      if (this.map.getSource('route-line')) {
        this.map.getSource('route-line').setData(geojson);
      } else {
        this.map.addSource('route-line', {
          type: 'geojson',
          data: geojson
        });
        this.map.addLayer({
          id: 'route-line-layer',
          type: 'line',
          source: 'route-line',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#1890ff',
            'line-width': 4
          }
        });
      }
    },

    isSameWaypoints(a, b) {
      return JSON.stringify(a) === JSON.stringify(b);
    },

    // 搜索地点
    searchPlaces(queryString, callback) {
      if (!queryString || queryString.length < 2) {
        callback([]);
        return;
      }

      this.searchLoading = true;
      const accessToken = this.mapboxConfig.accessToken;
      const lang = this.$i18n && this.$i18n.locale ? this.$i18n.locale : 'zh';
      const langParam = lang === 'en' ? 'en' : 'zh-CN';

      // 构建搜索URL，优先搜索中国地区
      const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(queryString)}.json?access_token=${accessToken}&language=${langParam}&country=CN&types=poi,address,place&limit=10`;

      fetch(url)
        .then(response => response.json())
        .then(data => {
          this.searchLoading = false;
          if (data.features && data.features.length > 0) {
            const suggestions = data.features.map(feature => ({
              value: this.formatSearchResultName(feature),
              address: this.formatSearchResultAddress(feature),
              longitude: feature.center[0],
              latitude: feature.center[1],
              place_name: feature.place_name,
              feature: feature
            }));
            callback(suggestions);
          } else {
            callback([]);
          }
        })
        .catch(error => {
          this.searchLoading = false;
          callback([]);
        });
    },

    // 格式化搜索结果名称
    formatSearchResultName(feature) {
      // 优先使用中文名称
      if (feature.text_zh) {
        return feature.text_zh;
      }
      if (feature.text) {
        return feature.text;
      }
      // 如果没有具体名称，使用地址的前半部分
      const placeName = feature.place_name || '';
      const parts = placeName.split(',');
      return parts[0] || placeName;
    },

    // 格式化搜索结果地址
    formatSearchResultAddress(feature) {
      const placeName = feature.place_name || '';
      const parts = placeName.split(',');

      // 移除国家信息
      let address = parts.filter(part =>
        !part.includes('中国') &&
        !part.includes('China') &&
        part.trim().length > 0
      ).join(', ');

      // 如果地址太长，截取前几个部分
      if (address.length > 50) {
        const addressParts = address.split(',');
        address = addressParts.slice(0, 3).join(', ');
      }

      return address;
    },

    // 处理选择地点
    onPlaceSelected(item) {
      if (!item || !item.longitude || !item.latitude) {
        this.$message.warning(this.t('map.invalidLocation'));
        return;
      }

      // 移动地图到选中的位置
      this.map.flyTo({
        center: [item.longitude, item.latitude],
        zoom: 15,
        duration: 1000
      });

      // 如果不是预览模式，询问是否添加为途径点
      if (!this.previewMode) {
        this.$confirm(
          this.t('map.addWaypointConfirm').replace('{name}', item.value),
          this.t('map.addWaypointTitle'),
          {
            confirmButtonText: this.t('map.add'),
            cancelButtonText: this.t('common.cancel'),
            type: 'info'
          }
        ).then(() => {
          this.addWaypointAtLocation({
            lng: item.longitude,
            lat: item.latitude
          });
          // 清空搜索框
          this.searchQuery = '';
        }).catch(() => {
          // 用户取消，只移动地图位置
        });
      } else {
        // 预览模式下只移动地图
        this.$message.info(this.t('map.locationFound').replace('{name}', item.value));
      }
    },

    // 处理搜索框回车
    onSearchEnter() {
      if (this.searchQuery.trim()) {
        // 触发搜索
        this.searchPlaces(this.searchQuery, (suggestions) => {
          if (suggestions.length > 0) {
            // 自动选择第一个结果
            this.onPlaceSelected(suggestions[0]);
          } else {
            this.$message.warning(this.t('map.noResultsFound'));
          }
        });
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleString();
    },

    // 自动定位到轨迹视图
    fitBoundsToWaypoints() {
      if (!this.map || !this.waypoints || this.waypoints.length === 0) {
        return;
      }

      // 如果只有一个点，直接定位到该点
      if (this.waypoints.length === 1) {
        const waypoint = this.waypoints[0];
        this.map.flyTo({
          center: [waypoint.longitude, waypoint.latitude],
          zoom: 15,
          duration: 1000
        });
        return;
      }

      // 计算所有途径点的边界
      let minLng = Infinity, maxLng = -Infinity;
      let minLat = Infinity, maxLat = -Infinity;

      this.waypoints.forEach(waypoint => {
        if (typeof waypoint.longitude === 'number' && typeof waypoint.latitude === 'number') {
          minLng = Math.min(minLng, waypoint.longitude);
          maxLng = Math.max(maxLng, waypoint.longitude);
          minLat = Math.min(minLat, waypoint.latitude);
          maxLat = Math.max(maxLat, waypoint.latitude);
        }
      });

      // 检查边界是否有效
      if (isFinite(minLng) && isFinite(maxLng) && isFinite(minLat) && isFinite(maxLat)) {
        // 添加一些边距，避免标记贴边
        const padding = 0.01; // 约1km的边距
        const bounds = [
          [minLng - padding, minLat - padding], // 西南角
          [maxLng + padding, maxLat + padding]  // 东北角
        ];

        // 使用fitBounds方法自动调整视图
        this.map.fitBounds(bounds, {
          padding: 50, // 像素边距
          duration: 1000, // 动画时长
          maxZoom: 16 // 最大缩放级别，避免过度放大
        });

        console.log('已自动定位到轨迹视图:', bounds);
      }
    },

    // 尝试地理定位
    tryGeolocation() {
      if (!this.map || !navigator.geolocation) {
        console.log('浏览器不支持地理定位');
        return;
      }

      console.log('开始地理定位...');

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log('地理定位成功:', latitude, longitude);

          // 定位到当前位置
          this.map.flyTo({
            center: [longitude, latitude],
            zoom: 13,
            duration: 1000
          });

          // 可选：添加当前位置标记
          this.addCurrentLocationMarker(longitude, latitude);
        },
        (error) => {
          console.warn('地理定位失败:', error.message);
          // 定位失败时使用默认中心点
          this.map.flyTo({
            center: [this.center.lng, this.center.lat],
            zoom: this.zoom,
            duration: 1000
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5分钟内的缓存位置可用
        }
      );
    },

    // 添加当前位置标记
    addCurrentLocationMarker(lng, lat) {
      // 移除之前的当前位置标记
      if (this.currentLocationMarker) {
        this.currentLocationMarker.remove();
      }

      // 创建当前位置标记元素
      const el = document.createElement('div');
      el.style.cssText = `
        width: 20px; 
        height: 20px; 
        background: #52c41a; 
        border: 3px solid white; 
        border-radius: 50%; 
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        position: relative;
      `;

      // 添加脉冲动画效果
      const pulse = document.createElement('div');
      pulse.style.cssText = `
        position: absolute;
        top: -10px;
        left: -10px;
        width: 40px;
        height: 40px;
        border: 2px solid #52c41a;
        border-radius: 50%;
        animation: pulse 2s infinite;
        opacity: 0.6;
      `;
      el.appendChild(pulse);

      // 创建标记
      this.currentLocationMarker = new this.mapboxgl.Marker({
        element: el,
        anchor: 'center'
      })
        .setLngLat([lng, lat])
        .addTo(this.map);

      // 添加弹窗
      const popup = new this.mapboxgl.Popup({
        offset: 25,
        closeButton: true
      }).setHTML(`
        <div style="padding: 8px; font-size: 12px; text-align: center;">
          <div style="margin-bottom: 4px; color: #52c41a; font-weight: bold;">📍 当前位置</div>
          <div style="color: #666; font-size: 11px;">${lng.toFixed(6)}, ${lat.toFixed(6)}</div>
        </div>
      `);

      this.currentLocationMarker.setPopup(popup);
    }
  }
}
</script>

<style lang="scss" scoped>
.mapbox-container {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  // 防止容器尺寸变化
  min-height: 600px;
  max-height: 600px;
}

.map-container {
  width: 100%;
  height: 100%;
  // 确保地图容器有固定的尺寸
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // 防止地图容器被其他元素影响
  touch-action: none;
  // 优化渲染性能
  will-change: transform;
}

.map-controls {
  position: absolute;
  top: 10px;
  left: 320px;
  /* 调整位置，避免与搜索框重叠 */
  z-index: 1000;
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.map-preview-tip {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  backdrop-filter: blur(10px);
  font-size: 14px;

  i {
    font-size: 16px;
  }
}

.waypoints-panel {
  position: absolute;
  top: 10px;
  right: 50px;
  width: 300px;
  max-height: 400px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;

  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #f5f7fa;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .waypoints-list {
    max-height: 320px;
    overflow-y: auto;

    .waypoint-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f5f7fa;
      }

      &.active {
        background: #e6f7ff;
        border-left: 3px solid #1890ff;
      }

      .waypoint-info {
        margin-bottom: 8px;

        .waypoint-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .waypoint-coords {
          font-size: 12px;
          color: #909399;
        }
      }

      .waypoint-actions {
        display: flex;
        gap: 8px;

        .danger-text {
          color: #f56c6c;
        }
      }
    }
  }
}

// 使用内联样式的标记，无需额外CSS
// 如果需要全局样式调整，可以添加：
// /deep/ .mapboxgl-popup-content {
//   border-radius: 6px;
//   font-family: inherit;
// }

// 确保地图标记的可见性
::v-deep .mapboxgl-marker {
  z-index: 1 !important;

  &:hover {
    z-index: 10 !important;
  }
}

// 确保标记元素始终可见
::v-deep .mapboxgl-marker div {
  position: relative !important;
  z-index: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
}

// 响应式设计
@media (max-width: 768px) {
  .mapbox-container {
    height: 400px;
  }

  .map-search {
    width: calc(100% - 20px);
    top: 10px;
    left: 10px;
    right: 10px;

  }

  .map-controls {
    position: absolute;
    top: 70px;
    /* 调整位置，避免与搜索框重叠 */
    left: 10px;
    right: 10px;
    flex-wrap: wrap;
    gap: 4px;
  }

  .waypoints-panel {
    width: 100%;
    max-height: 200px;
    top: auto;
    bottom: 10px;
    right: 10px;
  }
}

@media (max-width: 480px) {
  .map-controls {
    top: 80px;

    .el-button {
      font-size: 12px;
      padding: 6px 10px;
    }
  }

  .search-suggestion-item {
    padding: 10px 12px;

    .place-name {
      font-size: 13px;
    }

    .place-address {
      font-size: 11px;
    }
  }
}

.map-search {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 48px;
  display: flex;
  align-items: center;

  .search-input {
    width: 100%;

    ::v-deep .el-input__inner {
      border: none;
      background: transparent;
      padding: 12px 16px;
      font-size: 14px;

      &:focus {
        box-shadow: none;
      }
    }

    ::v-deep .el-input-group__prepend {
      background: transparent;
      border: none;
      padding: 0 12px;

      i {
        color: #909399;
        font-size: 16px;
      }
    }
  }
}

.search-suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f5f7fa;
  }

  .place-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .place-address {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

// 悬停提示框样式
.marker-hover-tooltip {
  position: fixed !important;
  background: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  padding: 10px 14px !important;
  border-radius: 8px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  z-index: 9999 !important;
  pointer-events: none !important;
  opacity: 0;
  transition: opacity 0.3s ease !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  max-width: 250px !important;
  visibility: hidden;

  &::before {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(0, 0, 0, 0.9);
  }

  &.tooltip-above::before {
    bottom: auto;
    top: -6px;
    border-top: none;
    border-bottom: 6px solid rgba(0, 0, 0, 0.9);
  }
}
</style>