<template>
  <div class="team-activity-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="mb16">
      <el-form-item :label="$t('bike.ridingManage.activityName')" prop="activityName">
        <el-input v-model.trim="queryParams.activityName" clearable @keyup.enter.native="handleQuery"
          style="width: 200px" :placeholder="$t('bike.ridingManage.placeholders.activityName')" />
      </el-form-item>
      <el-form-item :label="$t('bike.ridingManage.status')" prop="status">
        <el-select v-model="queryParams.status" clearable @change="handleQuery" style="width: 120px"
          :placeholder="$t('form.select') + $t('bike.ridingManage.status')">
          <el-option :label="$t('bike.ridingManage.activityStatus.ongoing')" value="1" />
          <el-option :label="$t('bike.ridingManage.activityStatus.pending')" value="2" />
          <el-option :label="$t('bike.ridingManage.activityStatus.ended')" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t('bike.ridingManage.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t('bike.ridingManage.reset') }}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row class="mb8">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
        {{ $t('bike.ridingManage.add') }}
      </el-button>
      <el-button type="danger" icon="el-icon-delete" :disabled="multiple" @click="handleDeleteBatch">
        {{ $t('bike.ridingManage.delete') }}
      </el-button>
      <el-button type="warning" icon="el-icon-download" @click="handleExport">
        {{ $t('bike.ridingManage.export') }}
      </el-button>
    </el-row>

    <!-- 活动列表 -->
    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :height="tableHeight">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="65" :label="$t('bike.ridingManage.index')" align="center" />
      <el-table-column :label="$t('bike.ridingManage.activityName')" prop="activityName" align="center" />
      <el-table-column :label="$t('bike.ridingManage.organizer')" prop="organizer" align="center" />
      <el-table-column :label="$t('bike.ridingManage.startTime')" prop="startTime" align="center">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.ridingManage.endTime')" prop="endTime" align="center">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.ridingManage.participants')" prop="participants" align="center" />
      <el-table-column :label="$t('bike.ridingManage.status')" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 1" type="success">{{ $t('bike.ridingManage.activityStatus.ongoing')
            }}</el-tag>
          <el-tag v-else-if="scope.row.status == 2" type="warning">{{ $t('bike.ridingManage.activityStatus.pending')
            }}</el-tag>
          <el-tag v-else type="danger">{{ $t('bike.ridingManage.activityStatus.ended') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.ridingManage.operation')" align="center" width="200px">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
            {{ $t('bike.ridingManage.edit') }}
          </el-button>
          <el-button type="text" class="text-red" icon="el-icon-delete" @click="handleDelete(scope.row)">
            {{ $t('bike.ridingManage.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 编辑对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="$t('bike.ridingManage.activityName')" prop="activityName">
          <el-input v-model="form.activityName" :placeholder="$t('bike.ridingManage.placeholders.activityName')" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.organizer')" prop="organizer">
          <el-input v-model="form.organizer" :placeholder="$t('bike.ridingManage.placeholders.organizer')" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.description')" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="4"
            :placeholder="$t('bike.ridingManage.placeholders.description')" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.startTime')" prop="startTime">
          <el-date-picker v-model="form.startTime" type="datetime"
            :placeholder="$t('bike.ridingManage.placeholders.startTime')" style="width: 100%" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.endTime')" prop="endTime">
          <el-date-picker v-model="form.endTime" type="datetime"
            :placeholder="$t('bike.ridingManage.placeholders.endTime')" style="width: 100%" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.maxParticipants')" prop="maxParticipants">
          <el-input-number v-model="form.maxParticipants" :min="1" :max="1000" style="width: 100%" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.statusLabel')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">{{ $t('bike.ridingManage.activityStatus.ongoing') }}</el-radio>
            <el-radio :label="2">{{ $t('bike.ridingManage.activityStatus.pending') }}</el-radio>
            <el-radio :label="0">{{ $t('bike.ridingManage.activityStatus.ended') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('bike.ridingManage.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('bike.ridingManage.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTeamActivity, getTeamActivity, addTeamActivity, updateTeamActivity, delTeamActivity, exportTeamActivity } from "@/api/bike/ridingManage";
import { parseTime } from "@/utils";

export default {
  name: "TeamActivity",
  props: {
    // 表格高度
    tableHeight: {
      type: [String, Number],
      default: 400
    }
  },
  data() {
    return {
      loading: true,
      dataList: [],
      total: 0,
      open: false,
      title: "",
      ids: [],
      multiple: true,
      queryParams: {
        p: 1,
        l: 20,
        activityName: null,
        status: null
      },
      form: {},
      rules: {
        activityName: [
          { required: true, message: this.$t('bike.ridingManage.validations.activityNameRequired'), trigger: "blur" }
        ],
        organizer: [
          { required: true, message: this.$t('bike.ridingManage.validations.organizerRequired'), trigger: "blur" }
        ],
        startTime: [
          { required: true, message: this.$t('bike.ridingManage.validations.startTimeRequired'), trigger: "change" }
        ],
        endTime: [
          { required: true, message: this.$t('bike.ridingManage.validations.endTimeRequired'), trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,

    /** 查询列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      setTimeout(() => {
        this.dataList = [
          {
            id: 1,
            activityName: "周末骑行活动",
            organizer: "管理员",
            startTime: new Date("2024-01-15 09:00:00"),
            endTime: new Date("2024-01-15 17:00:00"),
            participants: 15,
            maxParticipants: 20,
            status: 1,
            description: "周末骑行活动，欢迎大家参加"
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 1000);
    },

    /** 搜索 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },

    /** 重置 */
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.multiple = !selection.length;
    },

    /** 新增 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('bike.ridingManage.addTeamActivity');
    },

    /** 修改 */
    handleUpdate(row) {
      this.reset();
      this.form = { ...row };
      this.open = true;
      this.title = this.$t('bike.ridingManage.editTeamActivity');
    },

    /** 删除 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(this.$t('bike.ridingManage.deleteActivityConfirm'), this.$t('bike.ridingManage.warning'), {
        confirmButtonText: this.$t('bike.ridingManage.confirm'),
        cancelButtonText: this.$t('bike.ridingManage.cancel'),
        type: "warning"
      }).then(() => {
        this.$message.success(this.$t('bike.ridingManage.deleteSuccess'));
        this.getList();
      });
    },

    /** 批量删除 */
    handleDeleteBatch() {
      this.handleDelete();
    },

    /** 导出 */
    handleExport() {
      this.$message.success(this.$t('bike.ridingManage.deleteSuccess'));
    },

    /** 提交 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$message.success(this.form.id ? this.$t('bike.ridingManage.updateSuccess') : this.$t('bike.ridingManage.addSuccess'));
          this.open = false;
          this.getList();
        }
      });
    },

    /** 取消 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 重置表单 */
    reset() {
      this.form = {
        id: null,
        activityName: null,
        organizer: null,
        description: null,
        startTime: null,
        endTime: null,
        maxParticipants: 10,
        status: 1
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    }
  }
};
</script>

<style scoped>
.team-activity-container {
  padding: 0;
}

.mb8 {
  margin-bottom: 8px;
}

.mb16 {
  margin-bottom: 16px;
}

.text-red {
  color: #F56C6C;
}
</style>