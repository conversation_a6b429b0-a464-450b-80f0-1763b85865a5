<template>
  <div>
    <!-- 搜索表单 -->
    <!-- <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('bike.ridingManage.announcement.title')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('bike.ridingManage.announcement.placeholders.searchTitle')"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t('bike.ridingManage.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t('bike.ridingManage.reset') }}
        </el-button>
      </el-form-item>
    </el-form> -->

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">{{ $t('bike.ridingManage.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" :disabled="multiple" @click="handleDelete">{{
          $t('bike.ridingManage.delete') }}</el-button>
      </el-col>
    </el-row>

    <!-- 公告列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange" :height="tableHeight(-80)">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="65" :label="$t('bike.ridingManage.index')" align="center" />
      <el-table-column :label="$t('bike.ridingManage.announcement.title')" prop="title" align="center"
        show-overflow-tooltip />
      <el-table-column :label="$t('bike.ridingManage.announcement.content')" prop="content" align="center"
        show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.content && scope.row.content.length > 50 ? scope.row.content.substring(0, 50) + '...' :
            scope.row.content }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.ridingManage.announcement.createTime')" prop="createTime" align="center"
        width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.ridingManage.operation')" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
            {{ $t('bike.ridingManage.edit') }}
          </el-button>
          <el-button type="text" class="text-red" icon="el-icon-delete" @click="handleDelete(scope.row)">
            {{ $t('bike.ridingManage.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 编辑对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="$t('bike.ridingManage.announcement.title')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('bike.ridingManage.announcement.placeholders.title')" />
        </el-form-item>
        <el-form-item :label="$t('bike.ridingManage.announcement.content')" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="8"
            :placeholder="$t('bike.ridingManage.announcement.placeholders.content')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('dialog.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('dialog.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDisclaimerList, addDisclaimer, updateDisclaimer, deleteDisclaimer } from "@/api/bike/ridingManage";

export default {
  name: "Announcement",

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 弹窗标题
      title: '',
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        title: null
      },
      // 表单参数
      form: {
        id: null,
        title: '',
        content: ''
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: this.$t('bike.ridingManage.announcement.validations.titleRequired'), trigger: "blur" }
        ],
        content: [
          { required: true, message: this.$t('bike.ridingManage.announcement.validations.contentRequired'), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      console.log('发送骑行声明公告请求:', this.queryParams); // 添加调试日志
      getDisclaimerList(this.queryParams).then(response => {
        console.log('接收骑行声明公告响应:', response); // 添加调试日志
        const { data } = response;
        this.list = data.list || [];
        this.total = data.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('骑行声明公告请求失败:', error); // 添加错误日志
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        title: '',
        content: ''
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('bike.ridingManage.announcement.add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = { ...row };
      this.open = true;
      this.title = this.$t('bike.ridingManage.announcement.edit');
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = { ...this.form };
          console.log('提交骑行声明公告数据:', formData); // 添加调试日志

          if (formData.id) {
            updateDisclaimer(formData).then(() => {
              this.$message.success(this.$t('dialog.updateSuccess'));
              this.open = false;
              this.getList();
            });
          } else {
            addDisclaimer(formData).then(() => {
              this.$message.success(this.$t('dialog.addSuccess'));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(this.$t('bike.ridingManage.announcement.deleteConfirm'), this.$t('bike.ridingManage.warning'), {
        confirmButtonText: this.$t('dialog.confirm'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: "warning"
      }).then(() => {
        deleteDisclaimer(Array.isArray(ids) ? ids : [ids]).then(() => {
          this.$message.success(this.$t('dialog.deleteSuccess'));
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped>
.announcement-container {
  padding: 0;
}

.mb8 {
  margin-bottom: 8px;
}

.mb16 {
  margin-bottom: 16px;
}

.text-red {
  color: #F56C6C;
}

.text-gray {
  color: #909399;
  font-size: 12px;
}
</style>