<template>
  <div class="app-container">
    <!-- Tab标签页 -->
    <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
      <!-- 组队活动管理 Tab -->
      <el-tab-pane :label="$t('bike.ridingManage.teamActivity')" name="teamActivity">
        <div class="tab-content">
          <!-- 搜索表单 -->
          <!-- 操作按钮 -->
          <el-row class="mb8 flex  flex-start">
            <el-col :span="18">
              <el-form :model="teamQueryParams" ref="teamQueryForm" :inline="true" class="mb16">
                <el-form-item :label="$t('bike.ridingManage.activityName')" prop="titleName">
                  <el-input v-model.trim="teamQueryParams.titleName" clearable @keyup.enter.native="handleTeamQuery"
                    style="width: 200px" :placeholder="$t('form.input') + $t('bike.ridingManage.activityName')" />
                </el-form-item>
                <el-form-item :label="$t('bike.ridingManage.timeRange')" prop="timeRange">
                  <el-date-picker v-model="teamQueryParams.timeRange" type="datetimerange"
                    :range-separator="$t('bike.ridingManage.to')" :start-placeholder="$t('bike.ridingManage.startTime')"
                    :end-placeholder="$t('bike.ridingManage.endTime')" style="width: 380px"
                    value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" :clearable="true"
                    @change="handleTimeRangeChange" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" @click="handleTeamQuery">
                    {{ $t("queryParams.search") }}
                  </el-button>
                  <el-button icon="el-icon-refresh" @click="resetTeamQuery">
                    {{ $t("queryParams.reset") }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="6">
              <div class="flex justify-end">
                <el-button type="primary" icon="el-icon-plus" @click="handleTeamAdd">
                  {{ $t("queryParams.add") }}
                </el-button>
                <el-button type="danger" icon="el-icon-delete" :disabled="teamMultiple" @click="handleTeamDeleteBatch">
                  {{ $t("queryParams.delete") }}
                </el-button>
              </div>
            </el-col>
          </el-row>
          <!-- 组队活动列表 -->
          <el-table ref="teamTable" v-loading="teamLoading" :data="teamList" :height="tableHeight(-100)"
            @selection-change="handleTeamSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" width="65" :label="$t('acc.msg.msg.serialNumber')" align="center">
              <template slot-scope="scope">
                {{ (teamQueryParams.pageNum - 1) * teamQueryParams.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.activityName')" prop="titleName" align="center"
              show-overflow-tooltip />
            <el-table-column :label="$t('bike.ridingManage.cover')" prop="cover" align="center" width="120px">
              <template slot-scope="scope">
                <preview-img v-if="scope.row.cover" :imgUrl="scope.row.cover"
                  style="width: 60px; height: 40px; border-radius: 4px;" fit="cover"
                  :preview-src-list="[scope.row.cover]" />
                <span v-else class="text-gray">{{ $t('bike.ridingManage.noCover') }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.type')" prop="type" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.type == 1" type="success">{{ $t('bike.ridingManage.activityType.official')
                  }}</el-tag>
                <el-tag v-else type="info">{{ $t('bike.ridingManage.activityType.personal') }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.cyclingType')" prop="cyclingType" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.cyclingType == 1" type="primary">{{
                  $t('bike.ridingManage.cyclingTypes.distance') }}</el-tag>
                <el-tag v-else-if="scope.row.cyclingType == 2" type="warning">{{
                  $t('bike.ridingManage.cyclingTypes.route') }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.startTime')" prop="startTime" align="center">
              <template slot-scope="scope">
                <span v-NoData="parseTime(scope.row.startTime)"></span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.endTime')" prop="endTime" align="center">
              <template slot-scope="scope">
                <span v-NoData="parseTime(scope.row.endTime)"></span>
              </template>
            </el-table-column>
            <!-- <el-table-column :label="$t('bike.ridingManage.activityStatusLabel')" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.activityStatus == 0" type="info">{{
                  $t('bike.ridingManage.activityStatus.registration') }}</el-tag>
                <el-tag v-else-if="scope.row.activityStatus == 1" type="success">{{
                  $t('bike.ridingManage.activityStatus.registered') }}</el-tag>
                <el-tag v-else-if="scope.row.activityStatus == 2" type="warning">{{
                  $t('bike.ridingManage.activityStatus.ongoing') }}</el-tag>
                <el-tag v-else type="danger">{{ $t('bike.ridingManage.activityStatus.ended') }}</el-tag>
              </template>
            </el-table-column> -->
            <el-table-column :label="$t('bike.ridingManage.status')" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == 0" type="success">{{ $t('bike.ridingManage.statusType.normal')
                  }}</el-tag>
                <el-tag v-else type="danger">{{ $t('bike.ridingManage.statusType.disabled') }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column :label="$t('bike.ridingManage.rank.title')" align="center">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-trophy" @click="handleActivityRank(scope.row)">
                  {{ $t('bike.ridingManage.rank.title') }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column :label="$t('bike.ridingManage.operation')" align="center"
              class-name="small-padding fixed-width" width="170px">
              <template slot-scope="scope">
                <!-- 个人类型不支持编辑 -->
                <el-tooltip v-if="scope.row.type !== 2" :content="$t('queryParams.update')" placement="top">
                  <el-button type="text" icon="el-icon-edit" size="mini" @click="handleTeamUpdate(scope.row)"
                    hasPermi="['bike:team:edit']">
                  </el-button>
                </el-tooltip>
                <el-tooltip :content="$t('bike.ridingManage.viewDetail')" placement="top">
                  <el-button type="text" icon="el-icon-view" size="mini" @click="handleActivityDetail(scope.row)">
                  </el-button>
                </el-tooltip>
                <el-tooltip v-if="scope.row.type == 1 && scope.row.cyclingType == 2" :content="$t('map.previewMode')"
                  placement="top">
                  <el-button type="text" icon="el-icon-map-location" size="mini" @click="handleMapPreview(scope.row)">
                  </el-button>
                </el-tooltip>

                <el-tooltip
                  :content="scope.row.status === 0 ? $t('bike.ridingManage.disable') : $t('bike.ridingManage.enable')"
                  placement="top">
                  <el-button type="text" :icon="scope.row.status === 0 ? 'el-icon-close' : 'el-icon-check'" size="mini"
                    :class="[scope.row.status === 0 ? 'text-yellow' : 'text-green']" @click="handleTeamAuth(scope.row)"
                    hasPermi="['bike:team:auth']">
                  </el-button>
                </el-tooltip>
                <el-tooltip :content="$t('queryParams.delete')" placement="top">
                  <el-button type="text" class="text-red" icon="el-icon-delete" size="mini"
                    @click="handleTeamDelete(scope.row)" hasPermi="['bike:team:remove']">
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <pagination v-show="teamTotal > 0" :total="teamTotal" :page.sync="teamQueryParams.pageNum"
            :limit.sync="teamQueryParams.pageSize" @pagination="getTeamList" />
        </div>
      </el-tab-pane>

      <!-- 骑行声明公告 Tab -->
      <el-tab-pane :label="$t('bike.ridingManage.announcement_title')" name="announcement">
        <div class="tab-content">
          <Announcement />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 组队活动新增/编辑对话框 -->
    <el-dialog :title="teamTitle" :visible.sync="teamOpen" width="1000px" :close-on-click-modal="false" append-to-body
      class="activity-dialog">
      <el-form ref="teamForm" :model="teamForm" :rules="teamRules" label-width="120px" class="activity-form">
        <!-- 基本信息分组 -->
        <el-card class="form-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-info"></i>
            <span>{{ $t('bike.ridingManage.basicInfo') }}</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item :label="$t('bike.ridingManage.activityName')" prop="titleName">
                <el-input v-model="teamForm.titleName" size="small"
                  :placeholder="$t('form.input') + $t('bike.ridingManage.activityName')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.ridingManage.type')" prop="type">
                <el-radio-group v-model="teamForm.type" size="small">
                  <el-radio :label="1">{{ $t('bike.ridingManage.activityType.official') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item :label="$t('bike.ridingManage.description')" prop="detail">
            <el-input v-model="teamForm.detail" type="textarea" :rows="4" size="small"
              :placeholder="$t('form.input') + $t('bike.ridingManage.description')" />
          </el-form-item>
          <el-form-item :label="$t('bike.ridingManage.cover')" prop="cover">
            <Upload v-model="teamForm.cover" :limit="1" :accept="'image/*'" upload-mode="binary"
              list-type="picture-card">
              <i class="el-icon-plus"></i>
            </Upload>
            <div class="form-tip">{{ $t('bike.ridingManage.coverTip') }}</div>
          </el-form-item>
        </el-card>

        <!-- 时间设置分组 -->
        <el-card class="form-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-time"></i>
            <span>{{ $t('bike.ridingManage.timeSettings') }}</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('bike.ridingManage.activityTime')" prop="activityTimeRange">
                <el-date-picker v-model="teamForm.activityTimeRange" type="datetimerange"
                  :range-separator="$t('form.to')" :start-placeholder="$t('bike.ridingManage.startTime')" size="small"
                  :end-placeholder="$t('bike.ridingManage.endTime')" style="width: 100%"
                  value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss"
                  @change="handleActivityTimeRangeChange" :picker-options="pickerOptions" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('bike.ridingManage.expandTime')" prop="expandTime">
                <el-input-number :controls="false" v-model="teamForm.expandTime" :min="0" style="width: 100%"
                  size="small" :placeholder="$t('bike.ridingManage.expandTimePlaceholder')" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 骑行设置分组 -->
        <el-card class="form-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-bicycle"></i>
            <span>{{ $t('bike.ridingManage.cyclingSettings') }}</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('bike.ridingManage.cyclingType')" prop="cyclingType">
                <el-radio-group v-model="teamForm.cyclingType" size="small">
                  <el-radio-button :label="1">{{ $t('bike.ridingManage.cyclingTypes.distance') }}</el-radio-button>
                  <el-radio-button :label="2">{{ $t('bike.ridingManage.cyclingTypes.route') }}</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('bike.ridingManage.distance')" prop="distance" v-if="teamForm.cyclingType == 1">
                <el-input-number :controls="false" v-model="teamForm.distance" :min="0" :precision="2"
                  style="width: 100%" size="small" :placeholder="$t('form.input')" />
                <div class="form-tip">{{ $t('bike.ridingManage.distanceTip') }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 路线管理 -->
        <el-card class="form-card" shadow="never" v-if="teamForm.cyclingType == 2">
          <div slot="header" class="card-header">
            <i class="el-icon-map-location"></i>
            <span>{{ $t('bike.ridingManage.routeManagement') }}</span>
          </div>
          <el-form-item :label="$t('bike.ridingManage.waypoints')" prop="waypoints">
            <div class="waypoint-management">
              <el-button type="primary" icon="el-icon-location" @click="showMap = true" size="small">
                {{ $t('bike.ridingManage.manageWaypoints') }}
              </el-button>
              <div class="waypoint-info" v-if="waypoints && waypoints.length">
                <span class="waypoint-count">{{ waypoints.length }} {{ $t('bike.ridingManage.waypointsCount') }}</span>
              </div>
            </div>
            <el-table v-if="waypoints && waypoints.length" :data="waypoints" size="mini" border
              style="width: 100%; margin-top: 12px;" class="waypoint-table">
              <el-table-column label="#" width="50" align="center" type="index" :index="index => index + 1" />
              <el-table-column prop="name" :label="$t('map.waypointName')" />
              <el-table-column prop="latitude" :label="$t('map.latitude')" width="120">
                <template slot-scope="scope">
                  {{ scope.row.latitude ? scope.row.latitude.toFixed(6) : '' }}
                </template>
              </el-table-column>
              <el-table-column prop="longitude" :label="$t('map.longitude')" width="120">
                <template slot-scope="scope">
                  {{ scope.row.longitude ? scope.row.longitude.toFixed(6) : '' }}
                </template>
              </el-table-column>
            </el-table>
            <div v-else class="no-waypoints">
              <i class="el-icon-map-location"></i>
              <span>{{ $t('map.noWaypoints') }}</span>
            </div>
          </el-form-item>
        </el-card>

        <!-- 奖金设置分组 -->
        <el-card class="form-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-trophy"></i>
            <span>{{ $t('bike.ridingManage.prizeSettings') }}</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('bike.ridingManage.firstPrize')" prop="firstPrize">
                <el-input-number :controls="false" v-model="teamForm.firstPrize" :min="0" style="width: 100%"
                  size="small" :placeholder="$t('bike.ridingManage.prizePlaceholder')">
                  <template slot="prepend">¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.ridingManage.secondPrize')" prop="secondPrize">
                <el-input-number :controls="false" v-model="teamForm.secondPrize" :min="0" style="width: 100%"
                  size="small" :placeholder="$t('bike.ridingManage.prizePlaceholder')">
                  <template slot="prepend">¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('bike.ridingManage.thirdPrize')" prop="thirdPrize">
                <el-input-number :controls="false" v-model="teamForm.thirdPrize" :min="0" style="width: 100%"
                  size="small" :placeholder="$t('bike.ridingManage.prizePlaceholder')">
                  <template slot="prepend">¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="prize-total" v-if="getPrizeTotal() > 0">
            <span>{{ $t('bike.ridingManage.totalPrize') }}: ¥{{ getPrizeTotal() }}</span>
          </div>
        </el-card>

        <!-- 状态设置分组 -->
        <el-card class="form-card" shadow="never">
          <div slot="header" class="card-header">
            <i class="el-icon-setting"></i>
            <span>{{ $t('bike.ridingManage.statusSettings') }}</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="$t('bike.ridingManage.status')" prop="status">
                <el-radio-group v-model="teamForm.status" size="small">
                  <el-radio-button :label="0">{{ $t('bike.ridingManage.statusType.normal') }}</el-radio-button>
                  <el-radio-button :label="1">{{ $t('bike.ridingManage.statusType.disabled') }}</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelTeam" size="small">{{ $t("dialog.cancel") }}</el-button>
        <el-button type="primary" @click="submitTeamForm" size="small" :loading="teamLoading">
          {{ teamForm.id ? $t("dialog.update") : $t("dialog.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <!-- 途径点管理对话框 -->
    <el-dialog :title="$t('map.addWaypoint')" :visible.sync="showMap" width="90%" append-to-body top="5vh"
      @opened="handleMapOpened">
      <MapboxMap ref="mapboxMap" :initial-waypoints="waypoints" :activity-id="activityId" :preview-mode="false" />
      <div slot="footer">
        <el-button @click="showMap = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="saveWaypoints">{{ $t('common.save') }}</el-button>
      </div>
    </el-dialog>

    <!-- 地图预览对话框 -->
    <el-dialog :title="$t('map.previewMode')" :visible.sync="showMapPreview" width="90%" append-to-body top="5vh"
      @opened="handleMapPreviewOpened">
      <MapboxMap ref="mapboxMapPreview" :initial-waypoints="previewWaypoints" :preview-mode="true" />
      <div slot="footer">
        <el-button @click="showMapPreview = false">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 活动详情预览对话框 -->
    <ActivityDetail ref="activityDetail" />

    <!-- 活动排名对话框 -->
    <ActivityRank ref="activityRank" />

  </div>
</template>

<script>
import { commonJs } from "@/mixinFile/common";
import { getDicts } from "@/api/system/dict/data";
import { getActivityList, getActivityInfo, addActivity, updateActivity, deleteActivity, updateActivityAuth } from "@/api/bike/ridingManage";
import Announcement from "./components/Announcement.vue";
import Upload from "@/components/ImageCoverUpload/index.vue";
import { VUE_BASE_UPLOAD } from "@/api/config";
import MapboxMap from './components/MapboxMap.vue'
import ActivityDetail from './components/ActivityDetail.vue'
import ActivityRank from './components/ActivityRank.vue'
export default {
  name: "RidingManage",
  mixins: [commonJs],
  components: {
    Announcement,
    Upload,
    MapboxMap,
    ActivityDetail,
    ActivityRank
  },
  data() {
    return {
      waypoints: [],
      activityId: null,
      // 当前活动tab
      activeTab: "teamActivity",
      showMap: false,
      // 地图预览相关
      showMapPreview: false,
      previewWaypoints: [],
      // 组队活动管理相关数据
      teamLoading: true,
      teamList: [],
      teamTotal: 0,
      teamOpen: false,
      teamTitle: "",
      teamIds: [],
      teamMultiple: true,
      teamQueryParams: {
        pageNum: 1,
        pageSize: 10,
        titleName: null,
        startTime: null,
        endTime: null,
        timeRange: null
      },
      teamForm: {
        id: null,
        titleName: null,
        detail: null,
        startTime: null,
        endTime: null,
        activityTimeRange: null,
        distance: null,
        cyclingType: 1,
        type: 1, // 默认为官方类型
        cover: null,
        adImg: null,
        adUrl: null,
        firstPrize: null,
        secondPrize: null,
        thirdPrize: null,
        purview: 0,
        expandTime: null,
        pointImg: null,
        groupId: null,
        status: 0,
        activityStatus: 0
      },
      teamRules: {
        titleName: [
          { required: true, message: this.$t('bike.ridingManage.validations.activityNameRequired'), trigger: 'blur' },
          { min: 2, max: 50, message: this.$t('bike.ridingManage.validations.activityNameLength'), trigger: 'blur' }
        ],
        detail: [
          { required: true, message: this.$t('bike.ridingManage.validations.detailRequired'), trigger: 'blur' },
          { min: 10, max: 500, message: this.$t('bike.ridingManage.validations.detailLength'), trigger: 'blur' }
        ],
        activityTimeRange: [
          { required: true, message: this.$t('bike.ridingManage.validations.activityTimeRequired'), trigger: 'change' }
        ],
        cover: [
          { required: true, message: this.$t('validation.coverRequired'), trigger: 'change' }
        ],
        cyclingType: [
          { required: true, message: this.$t('bike.ridingManage.validations.cyclingTypeRequired'), trigger: 'change' }
        ],
        distance: [
          {
            required: true,
            message: this.$t('bike.ridingManage.validations.distanceRequired'),
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.teamForm.cyclingType === 1) {
                if (!value || value <= 0) {
                  callback(new Error(this.$t('bike.ridingManage.validations.distanceRequired')));
                } else if (value > 1000) {
                  callback(new Error(this.$t('bike.ridingManage.validations.distanceMax')));
                }
              }
              callback();
            }
          }
        ],
        firstPrize: [
          {
            validator: (rule, value, callback) => {
              if (value && value < 0) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMin')));
              } else if (value && value > 100000) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMax')));
              }
              callback();
            },
            trigger: 'change'
          }
        ],
        secondPrize: [
          {
            validator: (rule, value, callback) => {
              if (value && value < 0) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMin')));
              } else if (value && value > 100000) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMax')));
              }
              callback();
            },
            trigger: 'change'
          }
        ],
        thirdPrize: [
          {
            validator: (rule, value, callback) => {
              if (value && value < 0) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMin')));
              } else if (value && value > 100000) {
                callback(new Error(this.$t('bike.ridingManage.validations.prizeMax')));
              }
              callback();
            },
            trigger: 'change'
          }
        ]
      },



      // 字典数据
      statusOptions: [],
      typeOptions: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },

    };
  },
  computed: {

  },
  created() {
    this.getTeamList();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("announcement_type").then(response => {
      this.typeOptions = response.data;
    });
  },
  methods: {
    // Tab切换处理
    handleTabClick(tab) {
      if (tab.name === "teamActivity") {
        this.getTeamList();
      }
      // 公告Tab现在使用组件，不需要手动调用方法
    },

    // 组队活动管理方法
    getTeamList() {
      this.teamLoading = true;
      // 直接使用字符串格式的时间参数（YYYY-MM-DD HH:mm:ss）
      const queryParams = { ...this.teamQueryParams };
      console.log('发送组队活动请求:', queryParams); // 添加调试日志
      getActivityList(queryParams).then(response => {
        console.log('接收组队活动响应:', response); // 添加调试日志
        const { data } = response;
        this.teamList = data.list;
        this.teamTotal = data.total;
        this.teamLoading = false;
      }).catch(error => {
        console.error('组队活动请求失败:', error); // 添加错误日志
        this.teamLoading = false;
      });
    },

    // 时间范围变化处理
    handleTimeRangeChange(value) {
      console.log("🚀 ~ file: index.vue:546 ~ value:", value)
      if (value && Array.isArray(value) && value.length === 2) {
        // 确保时间格式为 YYYY-MM-DD HH:mm:ss
        this.teamQueryParams.startTime = new Date(value[0]).getTime() / 1000;
        this.teamQueryParams.endTime = new Date(value[1]).getTime() / 1000;
        console.log('时间范围已更新:', {
          startTime: value[0], // 格式: 2025-07-15 00:00:00
          endTime: value[1]    // 格式: 2025-07-15 23:59:59
        });
      } else {
        this.teamQueryParams.startTime = null;
        this.teamQueryParams.endTime = null;
        console.log('时间范围已清空');
      }
    },

    handleTeamQuery() {
      this.teamQueryParams.pageNum = 1;
      this.getTeamList();
    },

    resetTeamQuery() {
      this.resetForm("teamQueryForm");
      this.teamQueryParams.timeRange = null;
      this.teamQueryParams.startTime = null;
      this.teamQueryParams.endTime = null;
      this.handleTeamQuery();
    },

    handleTeamSelectionChange(selection) {
      this.teamIds = selection.map(item => item.id);
      this.teamMultiple = !selection.length;
    },

    handleTeamAdd() {
      this.resetTeamForm();
      this.waypoints = [];
      this.activityId = null;
      this.teamOpen = true;
      this.teamTitle = this.$t('bike.ridingManage.addTeamActivity');
    },

    handleTeamUpdate(row) {
      this.resetTeamForm();
      const id = row.id || this.teamIds;
      getActivityInfo(id).then(response => {
        const data = { ...response.data };
        // 转换时间格式为日期选择器需要的格式 (yyyy-MM-dd HH:mm:ss)
        if (data.startTime) {
          data.startTime = this.parseTime(data.startTime);
        }
        if (data.endTime) {
          data.endTime = this.parseTime(data.endTime);
        }
        // 设置时间范围
        if (data.startTime && data.endTime) {
          // 简单补充秒数
          const startTime = data.startTime.includes(':00:') ? data.startTime : data.startTime + ':00';
          const endTime = data.endTime.includes(':00:') ? data.endTime : data.endTime + ':00';
          data.activityTimeRange = [startTime, endTime];
        }

        // 加载途径点数据
        this.waypoints = [...data.list || []];
        this.activityId = data.id;

        this.teamForm = data;
        this.teamOpen = true;
        this.teamTitle = this.$t('bike.ridingManage.editTeamActivity');
      });
    },

    handleTeamDetail(row) {
      // TODO: 显示组队活动详情
      this.$message.info(this.$t('bike.ridingManage.viewDetail') + ': ' + row.titleName);
    },

    handleTeamDelete(row) {
      const ids = row.id || this.teamIds;
      this.$confirm(this.$t('bike.ridingManage.confirmDelete'), this.$t('bike.ridingManage.warning'), {
        confirmButtonText: this.$t('dialog.confirm'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: 'warning'
      }).then(() => {
        deleteActivity(Array.isArray(ids) ? ids : [ids]).then(() => {
          this.$message.success(this.$t('dialog.deleteSuccess'));
          this.getTeamList();
        });
      });
    },

    handleTeamDeleteBatch() {
      if (!this.teamIds.length) {
        this.$message.warning(this.$t('bike.ridingManage.selectItemsToDelete'));
        return;
      }

      this.$confirm(
        this.$t('bike.ridingManage.confirmDeleteBatch', { count: this.teamIds.length }),
        this.$t('bike.ridingManage.warning'),
        {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          type: 'warning'
        }
      ).then(() => {
        deleteActivity(this.teamIds).then(() => {
          this.$message.success(this.$t('dialog.deleteSuccess'));
          this.getTeamList();
          // 清空选择
          this.$refs.teamTable && this.$refs.teamTable.clearSelection();
        });
      });
    },

    // 启用/禁用组队活动
    handleTeamAuth(row) {
      const authData = {
        id: row.id,
        status: row.status === 0 ? 1 : 0
      };
      updateActivityAuth([authData]).then(() => {
        this.$message.success(row.status === 0 ?
          this.$t('bike.ridingManage.disableSuccess') :
          this.$t('bike.ridingManage.enableSuccess')
        );
        this.getTeamList();
      });
    },

    submitTeamForm() {
      console.log("🚀 ~ file: index.vue:538 ~ this.$filterUrl:", this.filterUrl)
      this.$refs["teamForm"].validate(valid => {
        if (valid) {
          const formData = { ...this.teamForm };
          // 保持字符串格式的时间（YYYY-MM-DD HH:mm:ss）供API使用
          console.log('提交表单数据:', formData); // 添加调试日志
          if (formData.cover) {
            // 过滤掉http或https开头的完整URL，只保留路径部分
            if (formData.cover.startsWith('http://') || formData.cover.startsWith('https://')) {
              formData.cover = this.filterUrl(formData.cover)
            }
          }
          if (formData.endTime) {
            formData.endTime = new Date(formData.endTime).getTime() / 1000
            formData.startTime = new Date(formData.startTime).getTime() / 1000
          }
          delete formData.activityTimeRange
          if (formData.cyclingType == 2 && this.waypoints.length < 2) {
            this.$message.warning(this.$t('map.waypointsRequired'));
            return;
          } else if (formData.cyclingType == 2) {
            formData.list = this.waypoints
          }
          if (formData.id != null) {
            updateActivity(formData).then(() => {
              this.$message.success(this.$t('dialog.updateSuccess'));
              this.teamOpen = false;
              this.getTeamList();
            });
          } else {
            addActivity(formData).then(() => {
              this.$message.success(this.$t('dialog.addSuccess'));
              this.teamOpen = false;
              this.getTeamList();
            });
          }
        }
      });
    },

    cancelTeam() {
      this.teamOpen = false;
      this.resetTeamForm();
    },

    resetTeamForm() {
      this.teamForm = {
        id: null,
        titleName: null,
        detail: null,
        startTime: null,
        endTime: null,
        activityTimeRange: null,
        distance: null,
        cyclingType: 1,
        type: 1, // 默认为官方类型
        cover: null,
        adImg: null,
        adUrl: null,
        firstPrize: null,
        secondPrize: null,
        thirdPrize: null,
        purview: 0,
        expandTime: null,
        pointImg: null,
        groupId: null,
        status: 0,
        activityStatus: 0
      };
      this.resetForm("teamForm");
    },

    // 活动时间范围变化处理
    handleActivityTimeRangeChange(value) {
      if (value && Array.isArray(value) && value.length === 2) {
        this.teamForm.startTime = value[0];
        this.teamForm.endTime = value[1];
        console.log('活动时间已更新:', {
          startTime: value[0],
          endTime: value[1]
        });
      } else {
        this.teamForm.startTime = null;
        this.teamForm.endTime = null;
        console.log('活动时间已清空');
      }
    },

    // 地图预览方法
    handleMapPreview(row) {
      // 获取活动详情，包括途径点信息
      getActivityInfo(row.id).then(response => {
        const data = response.data;
        // 假设后端返回的途径点数据在 data.waypoints 中
        this.previewWaypoints = [...data.list] || [];
        this.showMapPreview = true;
      }).catch(error => {
        console.error('获取活动详情失败:', error);
        this.$message.error(this.$t('bike.ridingManage.getActivityInfoFailed'));
      });
    },

    // 活动详情预览方法
    handleActivityDetail(row) {
      this.$refs.activityDetail.open(row.id);
    },

    // 活动排名方法
    handleActivityRank(row) {
      this.$nextTick(() => {
        if (this.$refs.activityRank && typeof this.$refs.activityRank.openDialog === 'function') {
          this.$refs.activityRank.openDialog(row.id, row.titleName);
        } else {
          console.error('ActivityRank组件未正确加载或openDialog方法不存在');
          this.$message.error(this.$t('bike.ridingManage.rankFeatureUnavailable'));
        }
      });
    },

    // 保存途径点
    saveWaypoints() {
      // 途径点必填
      if (!this.waypoints || this.waypoints.length === 1) {
        this.$message.warning(this.$t('map.waypointsRequired'));
        return;
      }

      // ...原有保存逻辑
      console.log('保存途径点:', JSON.parse(JSON.stringify(this.$refs.mapboxMap.waypoints)));
      this.showMap = false;
      this.waypoints = this.$refs.mapboxMap.waypoints;
      this.$message.success(this.$t('map.waypointsSaved'));
    },

    // 计算奖金总额
    getPrizeTotal() {
      const first = this.teamForm.firstPrize || 0;
      const second = this.teamForm.secondPrize || 0;
      const third = this.teamForm.thirdPrize || 0;
      return first + second + third;
    },

    // 地图预览对话框打开后的处理
    handleMapPreviewOpened() {
      // 延迟一段时间确保DOM完全渲染后再初始化地图
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.mapboxMapPreview) {
            // 如果地图还没初始化，手动初始化
            if (!this.$refs.mapboxMapPreview.isMapLoaded) {
              this.$refs.mapboxMapPreview.loadMapboxGL();
            } else {
              // 如果地图已初始化，重新加载途径点数据
              this.$refs.mapboxMapPreview.loadWaypoints(this.previewWaypoints);
            }
          }
        }, 200);
      });
    },

    // 途径点管理对话框打开后的处理
    handleMapOpened() {
      // 延迟一段时间确保DOM完全渲染后再初始化地图
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.mapboxMap) {
            // 如果地图还没初始化，手动初始化
            if (!this.$refs.mapboxMap.isMapLoaded) {
              this.$refs.mapboxMap.loadMapboxGL();
            } else {
              // 如果地图已初始化，重新加载途径点数据
              this.$refs.mapboxMap.loadWaypoints(this.waypoints);
            }
          }
        }, 200);
      });
    },
  }
};
</script>

<style scoped>
.page-title {
  margin-bottom: 20px;
}

.tab-content {
  padding: 20px 0;
}

.mb8 {
  margin-bottom: 8px;
}

.mb16 {
  margin-bottom: 16px;
}

.text-red {
  color: #F56C6C;
}

/* 排名按钮样式 */
.el-button[type="text"] .el-icon-trophy {
  color: #f39c12;
}

.el-button[type="text"]:hover .el-icon-trophy {
  color: #e67e22;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.text-gray {
  color: #909399;
  font-size: 12px;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
}

/* 美观的活动表单样式 */
.activity-form {
  .form-card {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 15px;
      font-weight: 600;
      color: #303133;

      i {
        color: #409eff;
        font-size: 18px;
        padding: 6px;
        background: #ecf5ff;
        border-radius: 50%;
      }
    }

    .el-card__body {
      padding: 24px;
    }

    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 6px;
      padding: 8px 12px;
      background: #f5f7fa;
      border-radius: 4px;
      border-left: 3px solid #409eff;
    }
  }

  .waypoint-management {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;

    .el-button {
      border-radius: 6px;
    }

    .waypoint-info .waypoint-count {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }
  }

  .waypoint-table {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .no-waypoints {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #909399;
    padding: 32px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
    transition: all 0.3s ease;

    &:hover {
      border-color: #c0c4cc;
      background: linear-gradient(135deg, #f0f2f5 0%, #e6e8eb 100%);
    }

    i {
      font-size: 28px;
      opacity: 0.7;
    }

    span {
      font-size: 14px;
    }
  }

  .prize-total {
    text-align: right;
    margin-top: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 6px;

    span {
      background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

/* 美化时间范围选择器 */
::v-deep .el-date-editor.el-range-editor {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #c0c4cc;
  }

  &.is-active {
    border-color: #409eff;
  }

  .el-range-separator {
    color: #606266;
    font-weight: 500;
  }

  .el-range-input {
    color: #606266;

    &::placeholder {
      color: #c0c4cc;
    }
  }
}

/* 快捷选项样式 */
::v-deep .el-picker-panel__shortcut {
  color: #606266;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: #f5f7fa;
    color: #409eff;
  }
}

/* 美观的对话框样式 */
.activity-dialog {
  ::v-deep .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  ::v-deep .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }

    .el-dialog__close {
      color: white;
      font-size: 20px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  ::v-deep .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 30px;
    background: #fafbfc;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  ::v-deep .el-dialog__footer {
    padding: 20px 30px;
    background: white;
    border-top: 1px solid #e4e7ed;
    text-align: center;

    .el-button {
      padding: 10px 24px;
      border-radius: 6px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .activity-dialog {
    ::v-deep .el-dialog {
      width: 95% !important;
      margin: 20px auto;
    }

    ::v-deep .el-dialog__body {
      padding: 20px;
    }
  }

  .activity-form {
    .el-col {
      width: 100% !important;
    }

    .form-card .el-card__body {
      padding: 16px;
    }

    .waypoint-management {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .prize-total {
      text-align: center;
    }
  }
}
</style>