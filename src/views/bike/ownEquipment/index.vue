<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('bike.bike.modelName')" prop="modelKey">
        <el-autocomplete v-model.trim="queryParams.modelKey" clearable :fetch-suggestions="querySearchAsync"
          @select="handleQuery" :placeholder="$t('form.input') + $t('bike.bike.modelName')" />
      </el-form-item>
      <el-form-item :label="$t('bike.bike.bluetooth')" prop="bluetooth">
        <el-input v-model.trim="queryParams.bluetooth" :placeholder="$t('form.input') + $t('bike.bike.bluetooth')"
          clearable @input="filterBluetoothValue" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('bike.bike.bindState')" prop="bindStatus">
        <el-select v-model="queryParams.bindStatus" clearable @change="handleQuery"
          :placeholder="$t('form.input') + $t('bike.bike.bindState')">
          <el-option v-for="dict in bindStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("bike.bike.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("bike.bike.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="bikeList" :height="tableHeight()">
      <el-table-column type="index" width="65" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="装备类型" prop="customerName" align="center" />
      <el-table-column label="装备型号" prop="brandName" align="center" />
      <el-table-column align="center" :label="$t('bike.bike.modelName')" prop="modelName" />
      <el-table-column align="center" :label="$t('bike.bike.nickName')" prop="nickName">
        <span slot-scope="scope" v-NoData="scope.row.nickName"></span>
      </el-table-column>
      <el-table-column align="center" :label="$t('bike.bike.bindTime')" prop="registerTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.bindTime)"></span>
      </el-table-column>
      <el-table-column align="center" :label="$t('bike.bike.bluetooth')" prop="bluetooth" />
      <el-table-column :label="$t('bike.bike.bindState')" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.bindTime">{{ $t("bike.bike.bind") }}</el-tag>
          <el-tag type="danger" v-else>{{ $t("bike.bike.notBind") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.operation')" class-name="small-padding fixed-width" header-align="center"
        width="120">
        <template slot-scope="scope">
          <div class="flex flex-wrap justify-around padding-lr-xs">
            <el-button type="text" @click="handleDetail(scope.row)">
              {{ $t("bike.bike.particulars") }}
            </el-button>
            <el-button v-if="scope.row.bindTime" type="text" class="text-red" @click="handleUntie(scope.row)">
              {{ $t("bike.bike.untie") }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <el-dialog :close-on-click-modal="false" :title="$t('bike.bike.particulars')" :visible.sync="dialogTableVisible">
      <el-card>
        <el-form class="form-content-detail" :model="queryParams" ref="queryForm" :inline="true" label-position="left">
          <el-form-item :label="$t('bike.bike.customerKey') + ':'" prop="modelKey">
            <span v-NoData="detailData.customerName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.brandName') + ':'" prop="modelKey">
            <span v-NoData="detailData.brandName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.computerName') + ':'" prop="modelKey">
            <span v-NoData="detailData.computerName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.modelName') + ':'" prop="modelKey">
            <span v-NoData="detailData.modelName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.nickName') + ':'" prop="modelKey">
            <span v-NoData="detailData.nickName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.registerTime') + ':'" prop="modelKey">
            <span v-NoData="parseTime(detailData.registerTime)"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.bluetooth') + ':'" prop="modelKey">
            <span v-NoData="detailData.bluetooth"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.batteryCapacity') + ':'" prop="modelKey">
            <span v-NoData="detailData.batteryCapacity"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.bindTime') + ':'">
            <span v-NoData="parseTime(detailData.bindTime)"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.carName') + ':'">
            <span v-NoData="detailData.carName"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.motorPower') + ':'">
            <span v-NoData="detailData.power"></span>
          </el-form-item>
          <el-form-item :label="$t('bike.bike.voltage') + ':'">
            <span v-NoData="detailData.voltage"></span>
          </el-form-item>
        </el-form>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { listBike, detailBike, computerUnbind } from "@/api/bike/bike";
import { modelFuzzy } from "@/api/bike/model";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      bikeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      bindStatusOptions: [
        { dictValue: "0", dictLabel: this.$t("bike.bike.bind") },
        { dictValue: "1", dictLabel: this.$t("bike.bike.notBind") }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        bindStatus: undefined,
        modelKey: undefined,
        nickName: undefined,
        bluetooth: undefined
      },
      // 表单校验
      detailData: {},
      dialogTableVisible: false
    };
  },
  created() {
    const { modelName } = this.$route.query;

    if (modelName) {
      this.queryParams.modelKey = modelName;
    }
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      listBike(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.bikeList = list;
        this.total = total;
        this.loading = false;
      });
    },
    querySearchAsync(queryString, cb) {
      modelFuzzy({ key: queryString || "" }).then(res => {
        let results = res.data.map(item => {
          return { value: item.key };
        });
        cb(results);
      });
    },
    handleDetail(row) {
      this.dialogTableVisible = true;
      detailBike(row.id).then(response => {
        this.detailData = Object.assign(row, response.data);
      });
    },
    filterBluetoothValue(val) {
      if (!this.$IS_Empty(val)) {
        val = val.replace(/:/gi, "");
        let reg = new RegExp("\\w{1," + 2 + "}", "g");
        let ma = val.match(reg);
        ma = ma.join(":");
        this.queryParams.bluetooth = ma.toUpperCase();
      }
    },
    handleUntie(row) {
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      }).then(() => {
        computerUnbind(row.id).then(() => {
          this.msgSuccess(this.$t("acc.user.succeed"));
          this.getList();
        });
      });
    }
  }
};
</script>
