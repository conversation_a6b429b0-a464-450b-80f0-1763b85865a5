<template>
  <div class="app-container">

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 骑游攻略管理 -->
      <el-tab-pane :label="t('tabs.guidebook')" name="guidebook">
        <!-- 搜索表单 -->
        <div class="search-toolbar">

          <el-form ref="searchForm" :model="searchForm" :inline="true" class="search-form">
            <el-form-item :label="t('search.title')" prop="title">
              <el-input v-model="searchForm.title" :placeholder="t('search.titlePlaceholder')" clearable
                style="width: 200px" />
            </el-form-item>
            <el-form-item :label="t('search.nickName')" prop="nickName">
              <el-input v-model="searchForm.nickName" :placeholder="t('search.nickNamePlaceholder')" clearable
                style="width: 200px" />
            </el-form-item>
            <el-form-item :label="t('search.type')" prop="type">
              <el-select v-model="searchForm.type" :placeholder="t('search.typePlaceholder')" clearable
                style="width: 120px">
                <el-option :label="t('type.all')" value="" />
                <el-option :label="t('type.personal')" :value="0" />
                <el-option :label="t('type.official')" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item :label="t('search.theme')" prop="theme">
              <el-select v-model="searchForm.theme" :placeholder="t('search.themePlaceholder')" clearable
                style="width: 120px">
                <el-option :label="t('theme.all')" value="" />
                <el-option :label="t('theme.sea')" value="1" />
                <el-option :label="t('theme.mountain')" value="2" />
                <el-option :label="t('theme.road')" value="3" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 操作按钮 -->
          <div class="toolbar">
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
              {{ t('actions.add') }}
            </el-button>
            <el-button type="success" icon="el-icon-check" :disabled="!hasSelection" @click="handleBatchEnable">
              {{ t('actions.batchEnable') }}
            </el-button>
            <el-button type="warning" icon="el-icon-close" :disabled="!hasSelection" @click="handleBatchDisable">
              {{ t('actions.batchDisable') }}
            </el-button>
            <el-button type="danger" icon="el-icon-delete" :disabled="!hasSelection" @click="handleBatchDelete">
              {{ t('actions.batchDelete') }}
            </el-button>
          </div>
        </div>
        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="tableData" style="width: 100%" :header-cell-style="{ textAlign: 'center' }"
          :height="tableHeight(-120)" :cell-style="{ textAlign: 'center' }" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="no" :label="t('table.no')" width="120" />
          <el-table-column prop="title" :label="t('table.title')" show-overflow-tooltip />
          <el-table-column prop="userImg" :label="t('discover.posted.headImg')" width="100">
            <template slot-scope="scope">
              <PreviewImg :imgUrl="scope.row.userImg" size="small" />
            </template>
          </el-table-column>
          <el-table-column prop="nickName" :label="t('table.nickName')">
            <template slot-scope="scope">
              <div class="user-info">

                <span style="margin-left: 8px">{{ scope.row.nickName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" :label="t('table.type')" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 1 ? 'success' : 'primary'">
                {{ scope.row.type === 1 ? t('type.official') : t('type.personal') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="themeName" :label="t('table.theme')" width="120" />
          <el-table-column prop="mileage" :label="t('table.mileage')" width="100">
            <template slot-scope="scope">
              {{ scope.row.mileage }}km
            </template>
          </el-table-column>
          <el-table-column prop="ridingTime" :label="t('table.ridingTime')" width="100">
            <template slot-scope="scope">
              {{ formatTime(scope.row.ridingTime) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('table.stats')" width="150">
            <template slot-scope="scope">
              <div class="stats-info">
                <span>{{ t('table.joinCount') }}: {{ scope.row.joinCount }}</span><br>
                <span>{{ t('table.likeCount') }}: {{ scope.row.likeCount }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="t('table.status')" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                {{ scope.row.status === 0 ? t('status.normal') : t('status.deleted') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('table.actions')" width="140" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="handleDetail(scope.row)">
                {{ t('actions.detail') }}
              </el-button>
              <el-button type="text" size="mini" @click="handleViewMap(scope.row)">
                {{ t('actions.viewMap') }}
              </el-button>
              <el-button type="text" size="mini" @click="handleEdit(scope.row)">
                {{ t('actions.edit') }}
              </el-button>
              <el-button type="text" size="mini" :class="scope.row.status === 0 ? 'danger-text' : 'success-text'"
                @click="handleToggleStatus(scope.row)">
                {{ scope.row.status === 0 ? t('actions.disable') : t('actions.enable') }}
              </el-button>
              <el-button type="text" size="mini" class="danger-text" @click="handleDelete(scope.row)">
                {{ t('actions.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="searchForm.p" :limit.sync="searchForm.l"
          @pagination="getList" />
      </el-tab-pane>

      <!-- 主题管理 -->
      <el-tab-pane :label="t('tabs.theme')" name="theme">
        <div class="toolbar">
          <el-button type="primary" icon="el-icon-plus" @click="handleAddTheme">
            {{ t('actions.addTheme') }}
          </el-button>
        </div>
        <el-table v-loading="themeLoading" :data="themeList" :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }" style="width: 100%">
          <el-table-column prop="name" :label="t('table.themeName')" />
          <el-table-column prop="img" :label="t('table.themeImg')" width="100">
            <template slot-scope="scope">
              <preview-img v-if="scope.row.img" :imgUrl="scope.row.img" width="50px" height="50px" />
              <span v-else class="no-image">{{ t('messages.noImage') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="sort" :label="t('table.sort')" width="80" sortable />
          <el-table-column prop="status" :label="t('table.status')" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                {{ scope.row.status === 0 ? t('status.enable') : t('status.disable') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('table.actions')" width="140">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="handleEditTheme(scope.row)">
                {{ t('actions.edit') }}
              </el-button>
              <el-button type="text" size="mini" :class="scope.row.status === 0 ? 'danger-text' : 'success-text'"
                @click="handleToggleThemeStatus(scope.row)">
                {{ scope.row.status === 0 ? t('actions.disable') : t('actions.enable') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/编辑骑游攻略对话框 -->
    <el-dialog :title="isEdit ? t('dialog.edit') : t('dialog.add')" :visible.sync="dialogVisible" width="900px"
      :close-on-click-modal="false" @close="handleDialogClose">
      <el-form ref="dataForm" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.title')" prop="title">
              <el-input v-model="formData.title" :placeholder="t('form.titlePlaceholder')" maxlength="30"
                show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.type')" prop="type">
              <el-radio-group v-model="formData.type">
                <!-- 只允许官方选项 -->
                <el-radio :label="1">{{ t('type.official') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.themeId')" prop="themeId">
              <el-select v-model="formData.themeId" :placeholder="t('form.themeIdPlaceholder')" style="width: 100%">
                <el-option v-for="theme in themeOptions" :key="theme.id" :label="theme.name" :value="theme.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.routeType')" prop="routeType">
              <el-select v-model="formData.routeType" :placeholder="t('form.routeTypePlaceholder')" style="width: 100%">
                <el-option :label="t('routeType.custom')" :value="1" />
                <el-option :label="t('routeType.smart')" :value="2" />
                <el-option :label="t('routeType.myRiding')" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.purview')" prop="purview">
              <el-radio-group v-model="formData.purview">
                <el-radio :label="0">{{ t('purview.public') }}</el-radio>
                <el-radio :label="1">{{ t('purview.private') }}</el-radio>
                <el-radio :label="2">{{ t('purview.friends') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('form.mileage')" prop="mileage">
              <el-input-number v-model="formData.mileage" :min="0" :max="9999" :precision="2" style="width: 100%"
                placeholder="0.00" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('form.ridingTime')" prop="ridingTime">
              <el-input-number v-model="formData.ridingTime" :min="0" :max="9999" style="width: 100%" placeholder="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 地图选择途径点功能 -->
        <el-form-item :label="t('form.waypoints')" prop="routeEntityList">
          <div class="waypoints-selector">
            <el-button type="primary" icon="el-icon-location" @click="openMapSelector" :disabled="!formData.themeId">
              {{ t('form.selectWaypoints') }}
            </el-button>
            <div class="waypoints-info" v-if="formData.routeEntityList && formData.routeEntityList.length > 0">
              <div class="waypoint-count">
                {{ t('form.selectedWaypoints', { count: formData.routeEntityList.length }) }}
              </div>
            </div>
            <div class="form-tip" v-if="!formData.themeId">
              {{ t('form.selectThemeFirst') }}
            </div>
          </div>

          <!-- 途径点详细信息表格 - 参考ridingManage的实现 -->
          <el-table v-if="formData.routeEntityList && formData.routeEntityList.length" :data="formData.routeEntityList"
            size="mini" border style="width: 100%; margin-top: 12px;" class="waypoint-table">
            <el-table-column label="#" width="50" align="center" type="index" :index="index => index + 1" />
            <el-table-column prop="name" :label="t('map.waypointName')" min-width="120">
              <template slot-scope="scope">
                {{ scope.row.name || `途径点${scope.$index + 1}` }}
              </template>
            </el-table-column>
            <el-table-column prop="latitude" :label="t('map.latitude')" width="120" align="center">
              <template slot-scope="scope">
                {{ scope.row.latitude ? scope.row.latitude.toFixed(6) : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="longitude" :label="t('map.longitude')" width="120" align="center">
              <template slot-scope="scope">
                {{ scope.row.longitude ? scope.row.longitude.toFixed(6) : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="address" :label="t('map.address')" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.address || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="mileage" :label="t('form.mileage')" width="80" align="center">
              <template slot-scope="scope">
                {{ scope.row.mileage ? scope.row.mileage.toFixed(1) + 'km' : '-' }}
              </template>
            </el-table-column>
            <el-table-column :label="t('common.operation')" width="80" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="mini" icon="el-icon-delete" class="text-red"
                  @click="removeWaypoint(scope.$index)">
                  {{ t('common.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 无途径点时的提示 -->
          <div v-else class="no-waypoints">
            <i class="el-icon-map-location"></i>
            <span>{{ t('map.noWaypoints') }}</span>
          </div>
        </el-form-item>

        <el-form-item :label="t('form.content')" prop="content">
          <el-input v-model="formData.content" type="textarea" :rows="4" :placeholder="t('form.contentPlaceholder')"
            maxlength="1000" show-word-limit />
        </el-form-item>

        <!-- 轨迹图上传 -->
        <el-form-item :label="t('form.img')" prop="img">
          <ImageCoverUpload v-model="formData.img" :placeholder="t('form.imgPlaceholder')" />
        </el-form-item>

        <!-- 路书图片 - 使用文本输入，多个用逗号分隔 -->
        <el-form-item :label="t('form.imgs')" prop="imgs">
          <el-input v-model="formData.imgs" :placeholder="t('form.imgsPlaceholder')" />
          <div class="form-tip">{{ t('form.imgsHint') }}</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 地图选择器对话框 -->
    <el-dialog :title="t('dialog.selectWaypoints')" :visible.sync="mapSelectorVisible" width="80%" top="5vh"
      :close-on-click-modal="false" custom-class="map-selector-dialog" @opened="onMapSelectorOpened"
      @close="onMapSelectorClosed">
      <!-- MapboxMap组件本身包含搜索框和途径点列表 -->
      <MapboxMap ref="mapboxMap" :initial-waypoints="formData.routeEntityList || []" :activity-id="formData.id || ''"
        :preview-mode="false" />

      <div slot="footer" class="dialog-footer">
        <el-button @click="mapSelectorVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="confirmWaypoints">
          {{ t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 主题新增/编辑对话框 -->
    <el-dialog :title="isEditTheme ? t('dialog.editTheme') : t('dialog.addTheme')" :visible.sync="themeDialogVisible"
      width="500px" :close-on-click-modal="false" @close="handleThemeDialogClose">
      <el-form ref="themeForm" :model="themeFormData" :rules="themeFormRules" label-width="100px">
        <el-form-item :label="t('form.themeName')" prop="name">
          <el-input v-model="themeFormData.name" :placeholder="t('form.themeNamePlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.themeImg')" prop="img">
          <ImageCoverUpload v-model="themeFormData.img" :placeholder="t('form.themeImgPlaceholder')" />
        </el-form-item>
        <el-form-item :label="t('form.sort')" prop="sort">
          <el-input-number v-model="themeFormData.sort" :min="0" :max="9999" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="themeDialogVisible = false">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" :loading="themeSubmitLoading" @click="handleThemeSubmit">
          {{ t('common.save') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog :title="t('dialog.detail')" :visible.sync="detailDialogVisible" width="900px"
      :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item :label="t('table.title')">
          {{ detailData.title }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.type')">
          <el-tag :type="detailData.type === 1 ? 'success' : 'primary'">
            {{ detailData.type === 1 ? t('type.official') : t('type.personal') }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.nickName')">
          {{ detailData.nickName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.theme')">
          {{ detailData.themeName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.mileage')">
          {{ detailData.mileage }}km
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.ridingTime')">
          {{ formatTime(detailData.ridingTime) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.joinCount')">
          {{ detailData.joinCount }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.likeCount')">
          {{ detailData.likeCount }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('form.content')" :span="2">
          <div class="content-display">
            {{ detailData.content }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">
          {{ t('common.close') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 地图预览对话框 -->
    <el-dialog :title="t('dialog.mapPreview')" :visible.sync="mapDialogVisible" width="80%" top="5vh"
      :close-on-click-modal="false" custom-class="map-preview-dialog" :modal-append-to-body="false"
      @opened="onMapDialogOpened" @close="onMapDialogClosed">
      <div class="map-preview-container">
        <!-- 地图区域 -->
        <div class="map-section">
          <div class="map-header">
            <h4>{{ mapData.title || t('map.routePreview') }}</h4>
            <div class="map-controls">
              <el-button size="mini" icon="el-icon-zoom-in" circle @click="zoomIn" :title="t('map.zoomIn')"></el-button>
              <el-button size="mini" icon="el-icon-zoom-out" circle @click="zoomOut"
                :title="t('map.zoomOut')"></el-button>
              <el-button size="mini" icon="el-icon-location" circle @click="fitToRoute"
                :title="t('map.fitToRoute')"></el-button>
            </div>
          </div>
          <div class="map-container">
            <div class="map-placeholder" v-if="!mapWaypoints.length">
              <div class="placeholder-content">
                <i class="el-icon-location-outline"></i>
                <h3>{{ t('map.mapPreview') }}</h3>
                <p>{{ t('map.noRouteData') }}</p>
              </div>
            </div>
            <div v-else class="map-wrapper">
              <SimpleMapPreview ref="mapPreview" :waypoints="mapWaypoints" :center="getMapCenter()"
                :accessToken="mapboxAccessToken" :translate="t" :key="currentLanguage" />
            </div>
          </div>
        </div>

        <!-- 信息面板 -->
        <div class="info-panel">
          <div class="route-summary">
            <el-card class="summary-card" shadow="never">
              <div slot="header" class="card-header">
                <i class="el-icon-data-analysis"></i>
                <span>{{ t('map.routeOverview') }}</span>
              </div>
              <div class="summary-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ mapData.mileage || 0 }}</span>
                  <span class="stat-label">{{ t('map.kilometers') }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ formatTime(mapData.ridingTime) }}</span>
                  <span class="stat-label">{{ t('map.duration') }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ mapData.joinCount || 0 }}</span>
                  <span class="stat-label">{{ t('map.participants') }}</span>
                </div>
              </div>
            </el-card>
          </div>

          <div class="route-details">
            <!-- 起点信息 -->
            <el-card class="info-card" shadow="never">
              <div class="point-info">
                <div class="point-icon start">
                  <i class="el-icon-position"></i>
                </div>
                <div class="point-details">
                  <div class="point-label">{{ t('map.startPoint') }}</div>
                  <div class="point-name">{{ mapData.startName || t('map.unknownLocation') }}</div>
                  <div class="point-coords" v-if="mapData.startLatitude && mapData.startLongitude">
                    {{ mapData.startLatitude.toFixed(4) }}, {{ mapData.startLongitude.toFixed(4) }}
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 终点信息 -->
            <el-card class="info-card" shadow="never">
              <div class="point-info">
                <div class="point-icon end">
                  <i class="el-icon-location"></i>
                </div>
                <div class="point-details">
                  <div class="point-label">{{ t('map.endPoint') }}</div>
                  <div class="point-name">{{ mapData.endName || t('map.unknownLocation') }}</div>
                  <div class="point-coords" v-if="mapData.endLatitude && mapData.endLongitude">
                    {{ mapData.endLatitude.toFixed(4) }}, {{ mapData.endLongitude.toFixed(4) }}
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 主题信息 -->
            <el-card class="info-card" shadow="never" v-if="mapData.themeName">
              <div class="theme-info">
                <div class="theme-icon">
                  <i class="el-icon-star-off"></i>
                </div>
                <div class="theme-details">
                  <div class="theme-label">{{ t('table.theme') }}</div>
                  <div class="theme-name">{{ mapData.themeName }}</div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>

    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import ImageCoverUpload from '@/components/ImageCoverUpload'
import MapboxMap from '../ridingManage/components/MapboxMap.vue'
import SimpleMapPreview from './components/SimpleMapPreview.vue'
import { createI18nManager } from './lang'
import {
  getGuidebookList,
  getGuidebookInfo,
  addGuidebook,
  updateGuidebook,
  deleteGuidebook,
  authGuidebook,
  getThemeList,
  addTheme,
  updateTheme,
  authTheme
} from '@/api/acc/guidebook'
import { tableHeight } from '@/utils/ruoyi'

export default {
  name: "StrategyManage",
  components: {
    Pagination,
    ImageCoverUpload,
    MapboxMap,
    SimpleMapPreview
  },
  data() {
    return {
      loading: false,
      themeLoading: false,
      submitLoading: false,
      themeSubmitLoading: false,
      activeTab: 'guidebook',
      tableData: [],
      themeList: [],
      themeOptions: [],
      total: 0,
      selectedRows: [],
      searchForm: {
        title: '',
        nickName: '',
        type: '',
        theme: '',
        p: 1,
        l: 20
      },
      dialogVisible: false,
      themeDialogVisible: false,
      detailDialogVisible: false,
      mapDialogVisible: false,
      isEdit: false,
      isEditTheme: false,
      formData: {
        id: '',
        title: '',
        content: '',
        img: '', // 轨迹图
        imgs: '', // 路书图片
        mileage: 0,
        ridingTime: 0,
        themeId: '',
        type: 1, // 默认为官方类型 0个人 1官方
        routeType: 1, // 路线类型 1自定义 2智能 3我的骑行
        status: 0, // 0正常 1删除
        purview: 0, // 0public 1for you 2friends
        ridingId: '', // 有轨迹记录id
        userId: '', // 用户id
        routeEntityList: [] // 途径点数据
      },
      themeFormData: {
        id: '',
        name: '',
        img: '',
        sort: 0,
        status: 0
      },
      detailData: {},
      mapData: {},
      mapWaypoints: [],
      formRules: {},
      themeFormRules: {},
      mapboxAccessToken: process.env.VUE_APP_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYWRkbW90b3IiLCJhIjoiY2x6dGltaDFzMmgybzJtb2NtNmsxYTIxaCJ9.b3yBd9nkM3EDNzebk_gVDA',
      // 添加一个响应式的语言标识符，用于触发重新渲染
      currentLanguage: 'zh',

      // 新增功能相关数据
      mapSelectorVisible: false,

    }
  },
  computed: {
    hasSelection() {
      return this.selectedRows.length > 0
    }
  },
  watch: {
    // 监听全局语言变化
    '$i18n.locale'(newLang, oldLang) {
      console.log('Language changed from', oldLang, 'to', newLang)
      this.handleLanguageChange(newLang)
    }
  },
  async created() {
    try {
      // 设置初始语言
      this.currentLanguage = this.$i18n.locale

      // 初始化国际化管理器并等待加载完成
      this.i18nManager = await createI18nManager(this.$i18n)

      // 初始化表单规则
      this.initFormRules()

      // 加载数据
      this.getList()
      this.getThemeOptions()
    } catch (error) {
      console.error('Failed to initialize i18n manager:', error)
      // 如果国际化初始化失败，仍然继续加载数据
      this.getList()
      this.getThemeOptions()
    }
  },
  methods: {
    // 简洁的国际化文本获取方法
    t(key) {
      // 引用currentLanguage确保响应式更新
      const lang = this.currentLanguage

      if (!this.i18nManager) {
        console.warn('I18n manager not initialized yet')
        return key
      }
      return this.i18nManager.getText(key)
    },

    // 处理语言变化
    async handleLanguageChange(newLang) {
      try {
        if (this.i18nManager && this.i18nManager.changeLanguage) {
          // 更新国际化管理器的语言
          await this.i18nManager.changeLanguage(newLang)

          // 更新响应式语言标识符
          this.currentLanguage = newLang

          // 重新初始化表单验证规则
          this.initFormRules()

          // 如果有表单正在显示，重新应用验证规则
          this.$nextTick(() => {
            if (this.$refs.dataForm) {
              this.$refs.dataForm.clearValidate()
            }
            if (this.$refs.themeForm) {
              this.$refs.themeForm.clearValidate()
            }
          })

          console.log(`Language changed to ${newLang} successfully`)
        }
      } catch (error) {
        console.error('Failed to change language:', error)
      }
    },

    // 初始化表单验证规则
    initFormRules() {
      this.formRules = {
        title: [
          { required: true, message: this.t('validation.titleRequired'), trigger: 'blur' },
          { min: 2, max: 30, message: this.t('validation.titleLength'), trigger: 'blur' }
        ],
        content: [
          { required: true, message: this.t('validation.contentRequired'), trigger: 'blur' },
          { min: 10, max: 1000, message: this.t('validation.contentLength'), trigger: 'blur' }
        ],
        themeId: [
          { required: true, message: this.t('validation.themeIdRequired'), trigger: 'change' }
        ],
        mileage: [
          { required: true, message: this.t('validation.mileageRequired'), trigger: 'blur' },
          { type: 'number', min: 0.1, max: 9999, message: this.t('validation.mileageRange'), trigger: 'blur' }
        ],
        ridingTime: [
          { required: true, message: this.t('validation.ridingTimeRequired'), trigger: 'blur' },
          { type: 'number', min: 1, max: 9999, message: this.t('validation.ridingTimeRange'), trigger: 'blur' }
        ],
        routeEntityList: [
          {
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error(this.t('validation.waypointsRequired')))
              } else if (value.length < 2) {
                callback(new Error(this.t('validation.waypointsMinimum')))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }

      this.themeFormRules = {
        name: [
          { required: true, message: this.t('validation.themeNameRequired'), trigger: 'blur' }
        ],
        img: [
          { required: true, message: this.t('validation.themeImgRequired'), trigger: 'change' }
        ]
      }
    },

    // 格式化时间（分钟转换为小时分钟）
    formatTime(minutes) {
      if (!minutes) return '0m'
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      if (hours > 0) {
        if (mins > 0) {
          return `${hours}h${mins}m`
        } else {
          return `${hours}h`
        }
      }
      return `${mins}m`
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getGuidebookList(this.searchForm)
        if (response.code === 200) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取骑游攻略列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.loading = false
      }
    },

    // 获取主题列表
    async getThemeListData() {
      this.themeLoading = true
      try {
        const response = await getThemeList({ p: 1, l: 100 })
        if (response.code === 200) {
          this.themeList = response.data.list || []
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取主题列表失败:', error)
        this.$message.error(this.t('messages.loadError'))
      } finally {
        this.themeLoading = false
      }
    },

    // 获取主题选项
    async getThemeOptions() {
      try {
        const response = await getThemeList({ p: 1, l: 100 })
        if (response.code === 200) {
          this.themeOptions = response.data.list || []
        }
      } catch (error) {
        console.error('获取主题选项失败:', error)
      }
    },

    // 标签页切换
    handleTabClick(tab) {
      if (tab.name === 'theme') {
        this.getThemeListData()
      }
    },

    // 搜索
    handleSearch() {
      this.searchForm.p = 1
      this.getList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        title: '',
        nickName: '',
        type: '',
        theme: '',
        p: 1,
        l: 20
      }
      this.$nextTick(() => {
        this.$refs.searchForm && this.$refs.searchForm.clearValidate()
      })
      this.getList()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 添加骑游攻略
    handleAdd() {
      this.isEdit = false
      this.formData = {
        id: '',
        title: '',
        content: '',
        img: '',
        imgs: '',
        mileage: 0,
        ridingTime: 0,
        themeId: '',
        type: 1, // 默认为官方类型
        routeType: 1,
        status: 0,
        purview: 0,
        ridingId: '',
        userId: '',
        routeEntityList: []
      }
      this.dialogVisible = true
    },

    // 编辑骑游攻略
    async handleEdit(row) {
      this.isEdit = true
      try {
        const response = await getGuidebookInfo(row.id)
        if (response.code === 200) {
          this.formData = {
            ...response.data,
            // 确保routeEntityList存在
            routeEntityList: response.data.routeEntityList || []
          }
          this.dialogVisible = true
        } else {
          this.$message.error(response.msg || this.t('messages.loadError'))
        }
      } catch (error) {
        console.error('获取骑游攻略详情失败:', error)
        this.$message.error(this.t('messages.loadError'))
      }
    },

    // 查看详情
    handleDetail(row) {
      this.detailData = { ...row }
      this.detailDialogVisible = true
    },

    // 查看地图
    handleViewMap(row) {
      console.log('打开地图预览，行数据:', row)

      // 设置地图数据
      this.mapData = { ...row }

      // 清理旧数据并准备新数据
      this.mapWaypoints = []
      this.$nextTick(() => {
        this.prepareMapWaypoints(row)
        this.mapDialogVisible = true
      })
    },

    // 对话框打开后的回调
    onMapDialogOpened() {
      console.log('地图对话框已打开，途径点数据:', this.mapWaypoints)

      // 延迟一点确保地图组件完全加载
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.mapPreview && this.mapWaypoints.length > 0) {
            console.log('强制更新地图数据')
            this.$refs.mapPreview.forceUpdate()
          }
        }, 100)
      })
    },

    // 对话框关闭后的回调
    onMapDialogClosed() {
      console.log('地图对话框已关闭，清理数据')
      // 清理地图数据，为下次打开做准备
      this.mapData = {}
      this.mapWaypoints = []
    },

    // 地图缩放控制
    zoomIn() {
      if (this.$refs.mapPreview) {
        this.$refs.mapPreview.zoomIn()
      }
    },

    zoomOut() {
      if (this.$refs.mapPreview) {
        this.$refs.mapPreview.zoomOut()
      }
    },

    fitToRoute() {
      if (this.$refs.mapPreview) {
        this.$refs.mapPreview.fitToWaypoints()
      }
    },


    // 准备地图途径点数据
    prepareMapWaypoints(rowData) {
      console.log('开始准备新的途径点数据，行数据:', rowData)

      // 创建新的途径点数组
      const newWaypoints = []

      // 如果有起始点信息，添加起始点
      if (rowData.startLatitude && rowData.startLongitude) {
        newWaypoints.push({
          activityId: rowData.id || '',
          createTime: Date.now(),
          id: 'start_' + Date.now(),
          name: rowData.startName || '起点',
          latitude: parseFloat(rowData.startLatitude),
          longitude: parseFloat(rowData.startLongitude),
          sort: 0
        })
      }

      // 如果有结束点信息，添加结束点
      if (rowData.endLatitude && rowData.endLongitude) {
        newWaypoints.push({
          activityId: rowData.id || '',
          createTime: Date.now(),
          id: 'end_' + Date.now(),
          name: rowData.endName || '终点',
          latitude: parseFloat(rowData.endLatitude),
          longitude: parseFloat(rowData.endLongitude),
          sort: newWaypoints.length > 0 ? 1 : 0
        })
      }

      // 使用 Vue.set 来确保响应式更新
      this.$set(this, 'mapWaypoints', newWaypoints)

      console.log('准备的地图途径点数据:', this.mapWaypoints)

      // 强制更新子组件
      this.$nextTick(() => {
        console.log('强制更新完成，mapWaypoints:', this.mapWaypoints)
      })
    },

    // 获取地图中心点
    getMapCenter() {
      if (this.mapWaypoints.length > 0) {
        // 如果有途径点，计算中心点
        const totalLat = this.mapWaypoints.reduce((sum, point) => sum + point.latitude, 0)
        const totalLng = this.mapWaypoints.reduce((sum, point) => sum + point.longitude, 0)
        return {
          lat: totalLat / this.mapWaypoints.length,
          lng: totalLng / this.mapWaypoints.length
        }
      }
      // 默认中心点（欧洲中心 - 德国法兰克福）
      return { lat: 50.1109, lng: 8.6821 }
    },

    // 切换状态
    async handleToggleStatus(row) {
      const newStatus = row.status === 0 ? 1 : 0
      const action = newStatus === 0 ? 'enable' : 'disable'
      const confirmMessage = newStatus === 0 ? this.t('actions.confirmEnable') : this.t('actions.confirmDisable')

      try {
        await this.$confirm(confirmMessage, this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = [{ id: row.id, status: newStatus }]
        const response = await authGuidebook(authList)

        if (response.code === 200) {
          this.$message.success(this.t(`messages.${action}Success`))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('状态切换失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm(this.t('actions.confirmDelete'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const response = await deleteGuidebook([row.id])
        if (response.code === 200) {
          this.$message.success(this.t('messages.deleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量启用
    async handleBatchEnable() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmEnable'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = this.selectedRows.map(row => ({ id: row.id, status: 0 }))
        const response = await authGuidebook(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchEnableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量启用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量禁用
    async handleBatchDisable() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmDisable'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const authList = this.selectedRows.map(row => ({ id: row.id, status: 1 }))
        const response = await authGuidebook(authList)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDisableSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量禁用失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (!this.hasSelection) {
        this.$message.warning(this.t('actions.selectRows'))
        return
      }

      try {
        await this.$confirm(this.t('actions.confirmDelete'), this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const ids = this.selectedRows.map(row => row.id)
        const response = await deleteGuidebook(ids)

        if (response.code === 200) {
          this.$message.success(this.t('messages.batchDeleteSuccess'))
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.dataForm.validate()
        this.submitLoading = true

        // 准备提交数据
        const submitData = {
          ...this.formData,
          // 确保途径点数据格式正确
          routeEntityList: this.formData.routeEntityList.map(point => ({
            ...point,
            guidebookId: this.formData.id || '' // 确保guidebookId正确
          }))
        }

        const apiCall = this.isEdit ? updateGuidebook : addGuidebook
        const response = await apiCall(submitData)

        if (response.code === 200) {
          this.$message.success(this.t(this.isEdit ? 'messages.updateSuccess' : 'messages.addSuccess'))
          this.dialogVisible = false
          this.getList()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== false) {
          console.error('提交失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      } finally {
        this.submitLoading = false
      }
    },

    // 对话框关闭
    handleDialogClose() {
      this.$refs.dataForm && this.$refs.dataForm.resetFields()
      this.formData = {
        id: '',
        title: '',
        content: '',
        img: '',
        imgs: '',
        mileage: 0,
        ridingTime: 0,
        themeId: '',
        type: 1,
        routeType: 1,
        status: 0,
        purview: 0,
        ridingId: '',
        userId: '',
        routeEntityList: []
      }
    },

    // 主题相关方法
    handleAddTheme() {
      this.isEditTheme = false
      this.themeFormData = {
        id: '',
        name: '',
        img: '',
        sort: 0,
        status: 0
      }
      this.themeDialogVisible = true
    },

    handleEditTheme(row) {
      this.isEditTheme = true
      this.themeFormData = { ...row }
      this.themeDialogVisible = true
    },

    async handleToggleThemeStatus(row) {
      const newStatus = row.status === 0 ? 1 : 0
      const action = newStatus === 0 ? 'enable' : 'disable'
      const confirmMessage = newStatus === 0 ? this.t('actions.confirmEnable') : this.t('actions.confirmDisable')

      try {
        await this.$confirm(confirmMessage, this.t('common.confirm'), {
          confirmButtonText: this.t('common.confirm'),
          cancelButtonText: this.t('common.cancel'),
          type: 'warning'
        })

        const response = await authTheme({ id: row.id, status: newStatus })

        if (response.code === 200) {
          this.$message.success(this.t(`messages.${action}Success`))
          this.getThemeListData()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('主题状态切换失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      }
    },

    async handleThemeSubmit() {
      try {
        await this.$refs.themeForm.validate()
        this.themeSubmitLoading = true

        const apiCall = this.isEditTheme ? updateTheme : addTheme
        const response = await apiCall(this.themeFormData)

        if (response.code === 200) {
          this.$message.success(this.t(this.isEditTheme ? 'messages.updateSuccess' : 'messages.addSuccess'))
          this.themeDialogVisible = false
          this.getThemeListData()
          this.getThemeOptions()
        } else {
          this.$message.error(response.msg || this.t('messages.operationError'))
        }
      } catch (error) {
        if (error !== false) {
          console.error('主题提交失败:', error)
          this.$message.error(this.t('messages.operationError'))
        }
      } finally {
        this.themeSubmitLoading = false
      }
    },

    handleThemeDialogClose() {
      this.$refs.themeForm && this.$refs.themeForm.resetFields()
      this.themeFormData = {
        id: '',
        name: '',
        img: '',
        sort: 0,
        status: 0
      }
    },

    // ========== 新增功能方法 ==========

    // 打开地图选择器
    openMapSelector() {
      if (!this.formData.themeId) {
        this.$message.warning(this.t('form.selectThemeFirst'))
        return
      }

      this.mapSelectorVisible = true
    },

    // 地图选择器打开后的回调
    onMapSelectorOpened() {
      this.$nextTick(() => {
        if (this.$refs.mapboxMap) {
          // MapboxMap组件会自动初始化
          console.log('地图组件已加载')
        }
      })
    },

    // 地图选择器关闭后的回调
    onMapSelectorClosed() {
      // 地图组件会自己处理清理
    },



    // 清空所有途径点
    clearAllWaypoints() {
      if (this.$refs.mapboxMap && this.$refs.mapboxMap.clearAllWaypoints) {
        this.$refs.mapboxMap.clearAllWaypoints()
      }
    },

    // 确认途径点选择 - 参考ridingManage的saveWaypoints方法
    confirmWaypoints() {
      if (this.$refs.mapboxMap) {
        // 从地图组件获取途径点数据
        const waypoints = this.$refs.mapboxMap.waypoints || []

        if (waypoints.length < 2) {
          this.$message.warning(this.t('validation.waypointsMinimum'))
          return
        }

        // 转换为API需要的格式
        this.formData.routeEntityList = waypoints.map((waypoint, index) => ({
          id: waypoint.id || Date.now().toString() + index,
          name: waypoint.name || `途径点${index + 1}`,
          latitude: waypoint.latitude,
          longitude: waypoint.longitude,
          address: waypoint.address || '',
          content: waypoint.content || '',
          highlights: waypoint.highlights || '',
          img: waypoint.img || '',
          sort: index,
          mileage: waypoint.mileage || 0,
          duration: waypoint.duration || 0,
          speed: waypoint.speed || 0,
          uphill: waypoint.uphill || 0,
          downhill: waypoint.downhill || 0,
          totalHill: waypoint.totalHill || 0,
          guidebookId: this.formData.id || ''
        }))

        this.mapSelectorVisible = false
        this.$message.success(this.t('messages.waypointsSelected', { count: this.formData.routeEntityList.length }))

        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('routeEntityList')
        })
      }
    },

    // 移除途径点
    removeWaypoint(index) {
      this.formData.routeEntityList.splice(index, 1)
      this.$refs.dataForm.validateField('routeEntityList')
    },




  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-form {
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  align-items: center;
}

.stats-info {
  font-size: 12px;
  color: #666;
}

.content-display {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

.danger-text {
  color: #f56c6c;
}

.success-text {
  color: #67c23a;
}

.dialog-footer {
  text-align: right;
}

// 操作按钮居中样式
::v-deep .el-table {
  .el-table__cell {
    .cell {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 4px;

      .el-button--text {
        margin: 0;
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

// 地图预览对话框样式
::v-deep .map-preview-dialog {
  .el-dialog__wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
  }

  .el-dialog {
    position: relative;
    margin: 0 !important;
    top: auto !important;
    left: auto !important;
    transform: none !important;
    width: 80% !important;
    height: 85vh !important;
    max-width: 1200px !important;
    max-height: none !important;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: none;
  }

  .el-dialog__header {
    flex-shrink: 0;
    height: 56px;
    padding: 16px 24px;
    background: #fff;
    color: #333;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #999;
        font-size: 20px;

        &:hover {
          color: #333;
        }
      }
    }
  }

  .el-dialog__body {
    flex: 1;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    background: #f8f9fa;
  }

  .el-dialog__footer {
    flex-shrink: 0;
    height: 60px;
    padding: 16px 24px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.map-preview-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  min-height: 0;
  gap: 0;
}

.map-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;

  .map-header {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .map-controls {
      display: flex;
      gap: 8px;

      .el-button {
        &.is-circle {
          width: 32px;
          height: 32px;
          padding: 0;
          border: 1px solid #dcdfe6;
          color: #606266;
          background: #fff;
          transition: all 0.2s ease;

          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background: #ecf5ff;
          }

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }
  }

  .map-container {
    flex: 1;
    position: relative;
    background: #f8f9fa;
    overflow: hidden;

    .map-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;

      .placeholder-content {
        text-align: center;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          display: block;
        }

        h3 {
          font-size: 18px;
          margin: 0 0 8px 0;
          font-weight: 500;
        }

        p {
          font-size: 14px;
          margin: 0;
        }
      }
    }

    .map-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }

  // 确保地图组件占满容器
  ::v-deep .mapbox-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    background: #fff !important;
    border: none !important;

    .map-container {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      background: #fff !important;
      border: none !important;
    }

    // Mapbox地图画布样式修复
    .mapboxgl-map {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      background: #fff !important;
      z-index: 1 !important;
    }

    .mapboxgl-canvas-container {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      z-index: 2 !important;
    }

    .mapboxgl-canvas {
      width: 100% !important;
      height: 100% !important;
      position: relative !important;
      z-index: 3 !important;
      background: transparent !important;
    }

    // 隐藏预览模式下不需要的控件
    .map-search,
    .waypoints-panel .panel-header {
      display: none !important;
    }

    // 隐藏途径点编辑功能
    .waypoint-actions,
    .panel-header .el-button {
      display: none !important;
    }

    // 隐藏地图控件
    .mapboxgl-ctrl-top-left,
    .mapboxgl-ctrl-top-right,
    .mapboxgl-ctrl-bottom-left,
    .mapboxgl-ctrl-bottom-right {
      display: none !important;
    }

    // 简化途径点面板
    .waypoints-panel {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 8px;
      border: 1px solid #e6e6e6;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(4px);

      .waypoint-actions {
        display: none !important;
      }
    }
  }
}

.info-panel {
  width: 340px;
  min-width: 320px;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .route-summary {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;

    .summary-card {
      border: none;
      box-shadow: none;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #333;
        font-size: 14px;

        i {
          color: #409eff;
        }
      }

      .summary-stats {
        display: flex;
        gap: 12px;
        margin-top: 16px;

        .stat-item {
          text-align: center;
          flex: 1;
          min-width: 80px; // 设置最小宽度确保内容不被过度压缩
          max-width: 120px; // 限制最大宽度保持美观
          padding: 0 6px;

          .stat-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            white-space: nowrap; // 防止数值换行
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .stat-label {
            display: block;
            font-size: 11px;
            color: #666;
            margin-top: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .route-details {
    flex: 1;
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-card {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      box-shadow: none;
      transition: all 0.2s;

      &:hover {
        border-color: #e6e6e6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .el-card__body {
        padding: 16px;
      }

      .point-info {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .point-icon {
          width: 36px;
          height: 36px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          &.start {
            background: #e7f4ff;
            color: #409eff;
          }

          &.end {
            background: #f0f9ff;
            color: #67c23a;
          }

          i {
            font-size: 16px;
          }
        }

        .point-details {
          flex: 1;
          min-width: 0;

          .point-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .point-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .point-coords {
            font-size: 11px;
            color: #999;
            font-family: monospace;
          }
        }
      }

      .theme-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .theme-icon {
          width: 36px;
          height: 36px;
          background: #fef7e0;
          color: #e6a23c;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          i {
            font-size: 16px;
          }
        }

        .theme-details {
          flex: 1;

          .theme-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .theme-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 1024px) {
  ::v-deep .map-preview-dialog {
    .el-dialog {
      width: 90% !important;
    }

    .map-preview-container {
      flex-direction: column;
    }

    .info-panel {
      width: 100%;
      max-height: 300px;
      border-right: none;
      border-top: 1px solid #f0f0f0;
    }

    .route-summary {
      padding: 16px;
    }

    .route-details {
      padding: 12px 16px;

      .info-card {
        .el-card__body {
          padding: 12px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  ::v-deep .map-preview-dialog {
    .el-dialog {
      width: 95% !important;
      height: 90vh !important;
    }

    .el-dialog__header {
      height: 48px;
      padding: 12px 16px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__footer {
      height: 52px;
      padding: 12px 16px;
    }

    .map-section {
      .map-header {
        padding: 12px 16px;

        h4 {
          font-size: 14px;
        }
      }
    }

    .info-panel {
      .route-summary {
        .summary-stats {
          gap: 8px;
          flex-direction: column; // 小屏幕垂直排列

          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f9f9f9;
            border-radius: 4px;
            text-align: left;

            .stat-value {
              font-size: 16px;
              order: 2;
            }

            .stat-label {
              font-size: 13px;
              order: 1;
              margin-top: 0;
              white-space: normal;
            }
          }
        }
      }

      .route-details {
        gap: 8px;

        .info-card {
          .point-info {
            gap: 10px;

            .point-icon {
              width: 32px;
              height: 32px;
            }
          }
        }
      }
    }
  }
}

// 简单地图测试样式
.simple-map-test {
  margin: 20px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 2px solid #1890ff;

  h3 {
    margin: 0 0 16px 0;
    color: #1890ff;
    font-size: 16px;
    font-weight: 600;
  }
}

// 极小屏幕优化
@media (max-width: 480px) {
  ::v-deep .map-preview-dialog {
    .info-panel {
      width: 100%;

      .route-summary {
        padding: 16px;

        .summary-stats {
          gap: 8px;

          .stat-item {
            min-width: 70px;
            max-width: 90px;
            padding: 0 4px;

            .stat-value {
              font-size: 14px;
            }

            .stat-label {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}

// ========== 新增功能样式 ==========

// 表单提示样式
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

// 途径点选择器样式
.waypoints-selector {
  .waypoints-info {
    margin-top: 12px;

    .waypoint-count {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .waypoint-list {
      max-height: 120px;
      overflow-y: auto;
    }
  }
}

// 图片上传样式
.images-upload {
  ::v-deep .el-upload-list--picture-card {
    .el-upload-list__item {
      width: 100px;
      height: 100px;
      margin: 0 8px 8px 0;
    }
  }

  ::v-deep .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .el-upload__tip {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }
}

// 地图选择器对话框样式
::v-deep .map-selector-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 85vh;
    max-height: none;
  }

  .el-dialog__body {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }
}

// 途径点表格样式 - 参考ridingManage
.waypoint-table {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 无途径点时的提示样式
.no-waypoints {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #909399;
  padding: 32px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
  transition: all 0.3s ease;
  margin-top: 12px;

  &:hover {
    border-color: #c0c4cc;
    background: linear-gradient(135deg, #f0f2f5 0%, #e6e8eb 100%);
  }

  i {
    font-size: 28px;
    opacity: 0.7;
  }

  span {
    font-size: 14px;
  }
}

.map-selector-container {
  height: 100%;
  display: flex;
  flex-direction: row;

  .map-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-right: 1px solid #f0f0f0;

    .map-header {
      padding: 16px 20px;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .map-controls {
        display: flex;
        gap: 8px;
      }
    }

    .map-container {
      flex: 1;
      position: relative;
      background: #f8f9fa;
    }
  }

  .waypoints-panel {
    width: 300px;
    background: #fff;
    display: flex;
    flex-direction: column;

    .panel-header {
      padding: 16px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .waypoint-count {
        font-size: 12px;
        color: #666;
        background: #e6f7ff;
        padding: 2px 8px;
        border-radius: 10px;
      }
    }

    .waypoints-list {
      flex: 1;
      padding: 16px 20px;
      overflow-y: auto;

      .waypoint-item {
        margin-bottom: 12px;

        .waypoint-info {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 12px;
          background: #f9f9f9;
          border-radius: 8px;
          border: 1px solid #f0f0f0;

          .waypoint-index {
            width: 24px;
            height: 24px;
            background: #409eff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
          }

          .waypoint-details {
            flex: 1;
            min-width: 0;

            .waypoint-name {
              margin-bottom: 4px;
            }

            .waypoint-coords {
              font-size: 11px;
              color: #999;
              font-family: monospace;
            }
          }

          .waypoint-actions {
            flex-shrink: 0;

            .el-button--text {
              color: #f56c6c;

              &:hover {
                color: #f78989;
              }
            }
          }
        }
      }

      .empty-waypoints {
        text-align: center;
        padding: 40px 20px;
        color: #999;

        i {
          font-size: 48px;
          display: block;
          margin-bottom: 16px;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 1024px) {
  .map-selector-container {
    flex-direction: column;

    .map-section {
      border-right: none;
      border-bottom: 1px solid #f0f0f0;
    }

    .waypoints-panel {
      width: 100%;
      max-height: 300px;
    }
  }
}

@media (max-height: 600px) {
  ::v-deep .map-preview-dialog {
    .el-dialog {
      height: 95vh !important;
    }

    .info-panel {
      .route-summary {
        padding: 12px 16px;
      }

      .route-details {
        padding: 8px 16px;
      }
    }
  }
}
</style>