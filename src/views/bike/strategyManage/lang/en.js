export default {
  title: 'Cycling Strategy Management',
  description: 'Manage cycling strategy content, including add, edit, delete and status control, as well as theme management',
  
  // Tabs
  tabs: {
    guidebook: 'Cycling Strategy',
    theme: 'Theme Management'
  },
  
  // Search Form
  search: {
    title: 'Strategy Title',
    titlePlaceholder: 'Please enter strategy title',
    nickName: 'User Nickname',
    nickNamePlaceholder: 'Please enter user nickname',
    type: 'Strategy Type',
    typePlaceholder: 'Please select strategy type',
    theme: 'Theme',
    themePlaceholder: 'Please select theme',
    timeRange: 'Create Time',
    to: 'to',
    startTime: 'Start Time',
    endTime: 'End Time'
  },
  
  // Type
  type: {
    all: 'All',
    personal: 'Personal',
    official: 'Official'
  },
  
  // Theme
  theme: {
    all: 'All',
    sea: 'Sea',
    mountain: 'Mountain',
    road: 'Road'
  },
  
  // Route Type
  routeType: {
    custom: 'Custom',
    smart: 'Smart',
    myRiding: 'My Riding'
  },

  // Purview Type
  purview: {
    public: 'Public',
    private: 'Private',
    friends: 'Friends Only'
  },
  
  // Table Fields
  table: {
    no: 'Guidebook No.',
    title: 'Strategy Title',
    nickName: 'User',
    type: 'Type',
    theme: 'Theme',
    mileage: 'Mileage',
    ridingTime: 'Riding Time',
    stats: 'Statistics',
    joinCount: 'Participants',
    likeCount: 'Likes',
    status: 'Status',
    actions: 'Actions',
    themeName: 'Theme Name',
    themeImg: 'Theme Image',
    sort: 'Sort'
  },
  
  // Status
  status: {
    normal: 'Normal',
    deleted: 'Deleted',
    enable: 'Enable',
    disable: 'Disable'
  },
  
  // Actions
  actions: {
    add: 'Add Strategy',
    addTheme: 'Add Theme',
    edit: 'Edit',
    delete: 'Delete',
    batchDelete: 'Batch Delete',
    enable: 'Enable',
    disable: 'Disable',
    batchEnable: 'Batch Enable',
    batchDisable: 'Batch Disable',
    detail: 'Detail',
    viewMap: 'View Map',
    confirmDelete: 'Are you sure to delete selected strategies?',
    confirmEnable: 'Are you sure to enable selected strategies?',
    confirmDisable: 'Are you sure to disable selected strategies?',
    selectRows: 'Please select data to operate'
  },
  
  // Form
  form: {
    title: 'Strategy Title',
    titlePlaceholder: 'Please enter strategy title',
    type: 'Strategy Type',
    typeHint: 'Only official strategies are allowed',
    themeId: 'Theme',
    themeIdPlaceholder: 'Please select theme',
    routeType: 'Route Type',
    routeTypePlaceholder: 'Please select route type',
    purview: 'Visibility',
    mileage: 'Mileage(km)',
    mileageUnit: 'Unit: kilometers',
    ridingTime: 'Riding Time(minutes)',
    ridingTimeUnit: 'Unit: minutes',
    content: 'Strategy Content',
    contentPlaceholder: 'Please enter strategy content, max 1000 characters',
    img: 'Track Image',
    imgPlaceholder: 'Please upload track image',
    imgs: 'Guidebook Images',
    imgsPlaceholder: 'Please enter image links, separated by commas',
    imgsHint: 'Multiple image links should be separated by commas',
    images: 'Strategy Images',
    imageUploadTip: 'Only jpg/png format supported, max 5MB per image, up to 3 images',
    imageTypeError: 'Only jpg/png format images are supported',
    imageSizeError: 'Image size cannot exceed 5MB',
    imageExceedLimit: 'Maximum 3 images allowed',
    waypoints: 'Waypoints',
    selectWaypoints: 'Select Waypoints',
    selectedWaypoints: '{count} waypoints selected',
    selectThemeFirst: 'Please select theme first',
    waypointsTitle: 'Activity Waypoints',
    manageWaypoints: 'Manage Waypoints',
    waypoint: 'Waypoint',
    noWaypoints: 'No waypoints yet, click "Manage Waypoints" to add',
    themeName: 'Theme Name',
    themeNamePlaceholder: 'Please enter theme name',
    themeImg: 'Theme Image',
    themeImgPlaceholder: 'Please upload theme image',
    sort: 'Sort'
  },
  
  // Validation
  validation: {
    titleRequired: 'Strategy title is required',
    titleLength: 'Title length should be 2-30 characters',
    contentRequired: 'Strategy content is required',
    contentLength: 'Content length should be 10-1000 characters',
    themeIdRequired: 'Theme is required',
    mileageRequired: 'Mileage is required',
    mileageRange: 'Mileage should be 0.1-9999 km',
    ridingTimeRequired: 'Riding time is required',
    ridingTimeRange: 'Riding time should be 1-9999 minutes',
    waypointsRequired: 'Waypoints are required',
    waypointsMinimum: 'At least 2 waypoints are required',
    themeNameRequired: 'Theme name is required',
    themeImgRequired: 'Theme image is required'
  },
  
  // Messages
  messages: {
    addSuccess: 'Add successfully',
    updateSuccess: 'Update successfully',
    deleteSuccess: 'Delete successfully',
    enableSuccess: 'Enable successfully',
    disableSuccess: 'Disable successfully',
    batchDeleteSuccess: 'Batch delete successfully',
    batchEnableSuccess: 'Batch enable successfully',
    batchDisableSuccess: 'Batch disable successfully',
    loadError: 'Data load failed',
    operationError: 'Operation failed',
    noImage: 'No image available',
    waypointsSaved: 'Waypoints saved successfully',
    waypointsSelected: '{count} waypoints selected',
    uploadSuccess: 'Upload successfully',
    uploadError: 'Upload failed'
  },
  
  // Dialog Titles
  dialog: {
    add: 'Add Cycling Strategy',
    edit: 'Edit Cycling Strategy',
    addTheme: 'Add Theme',
    editTheme: 'Edit Theme',
    detail: 'Strategy Detail',
    mapPreview: 'Map Preview',
    mapManagement: 'Waypoints Management',
    selectWaypoints: 'Select Waypoints'
  },
  
  // Map Related
  map: {
    placeholder: 'Google Maps Preview Area',
    integration: 'Google Maps API can be integrated here to show routes',
    startPoint: 'Start Point',
    endPoint: 'End Point',
    routeInfo: 'Route Information',
    start: 'S',
    end: 'E',
    routePreview: 'Route Preview',
    mapPreview: 'Map Preview',
    noRouteData: 'No route data available',
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    routeOverview: 'Route Overview',
    kilometers: 'km',
    duration: 'Duration',
    participants: 'Participants',
    unknownLocation: 'Unknown Location',
    fitToRoute: 'Fit to Route',
    selectRoutePoints: 'Select Route Points',
    waypointsList: 'Waypoints List',
    waypointName: 'Waypoint Name',
    clickMapToAdd: 'Click map to add waypoints',
    clearAll: 'Clear All',
    waypointLimit: 'Maximum 10 waypoints allowed',
    latitude: 'Latitude',
    longitude: 'Longitude',
    address: 'Address',
    noWaypoints: 'No waypoints yet, please click button to select'
  },
  
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    selectAll: 'Select All',
    pleaseSelect: 'Please Select'
  }
} 