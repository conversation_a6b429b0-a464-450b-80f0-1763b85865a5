export default {
  title: '骑游攻略管理',
  description: '管理骑游攻略内容，包括新增、编辑、删除和状态控制，以及主题管理',
  
  // 标签页
  tabs: {
    guidebook: '骑游攻略',
    theme: '主题管理'
  },
  
  // 搜索表单
  search: {
    title: '攻略标题',
    titlePlaceholder: '请输入攻略标题',
    nickName: '用户昵称',
    nickNamePlaceholder: '请输入用户昵称',
    type: '攻略类型',
    typePlaceholder: '请选择攻略类型',
    theme: '主题',
    themePlaceholder: '请选择主题',
    timeRange: '创建时间',
    to: '至',
    startTime: '开始时间',
    endTime: '结束时间'
  },
  
  // 类型
  type: {
    all: '全部',
    personal: '个人',
    official: '官方'
  },
  
  // 主题
  theme: {
    all: '全部',
    sea: '海边',
    mountain: '山地',
    road: '公路'
  },
  
  // 路线类型
  routeType: {
    custom: '自定义',
    smart: '智能',
    myRiding: '我的骑行'
  },

  // 权限类型
  purview: {
    public: '公开',
    private: '仅自己',
    friends: '好友可见'
  },
  
  // 表格字段
  table: {
    no: '路书编号',
    title: '攻略标题',
    nickName: '用户',
    type: '类型',
    theme: '主题',
    mileage: '里程',
    ridingTime: '骑行时长',
    stats: '统计',
    joinCount: '参与人数',
    likeCount: '点赞数',
    status: '状态',
    actions: '操作',
    themeName: '主题名称',
    themeImg: '主题图片',
    sort: '排序'
  },
  
  // 状态
  status: {
    normal: '正常',
    deleted: '已删除',
    enable: '启用',
    disable: '禁用'
  },
  
  // 操作
  actions: {
    add: '添加攻略',
    addTheme: '添加主题',
    edit: '编辑',
    delete: '删除',
    batchDelete: '批量删除',
    enable: '启用',
    disable: '禁用',
    batchEnable: '批量启用',
    batchDisable: '批量禁用',
    detail: '详情',
    viewMap: '查看地图',
    confirmDelete: '确定要删除选中的攻略吗？',
    confirmEnable: '确定要启用选中的攻略吗？',
    confirmDisable: '确定要禁用选中的攻略吗？',
    selectRows: '请选择要操作的数据'
  },
  
  // 表单
  form: {
    title: '攻略标题',
    titlePlaceholder: '请输入攻略标题',
    type: '攻略类型',
    typeHint: '只允许创建官方攻略',
    themeId: '主题',
    themeIdPlaceholder: '请选择主题',
    routeType: '路线类型',
    routeTypePlaceholder: '请选择路线类型',
    purview: '可见权限',
    mileage: '里程(km)',
    mileageUnit: '单位：公里',
    ridingTime: '骑行时长(分钟)',
    ridingTimeUnit: '单位：分钟',
    content: '攻略内容',
    contentPlaceholder: '请输入攻略内容，最多1000字',
    img: '轨迹图',
    imgPlaceholder: '请上传轨迹图',
    imgs: '路书图片',
    imgsPlaceholder: '请输入图片链接，多个用逗号分隔',
    imgsHint: '多个图片链接请用逗号分隔',
    images: '攻略图片',
    imageUploadTip: '只支持jpg/png格式，单张图片不超过5MB，最多上传3张',
    imageTypeError: '只支持jpg/png格式的图片',
    imageSizeError: '图片大小不能超过5MB',
    imageExceedLimit: '最多只能上传3张图片',
    waypoints: '途径点',
    selectWaypoints: '选择途径点',
    selectedWaypoints: '已选择{count}个途径点',
    selectThemeFirst: '请先选择主题',
    waypointsTitle: '活动途径点',
    manageWaypoints: '管理途径点',
    waypoint: '途径点',
    noWaypoints: '暂无途径点，点击"管理途径点"按钮添加',
    themeName: '主题名称',
    themeNamePlaceholder: '请输入主题名称',
    themeImg: '主题图片',
    themeImgPlaceholder: '请上传主题图片',
    sort: '排序'
  },
  
  // 验证
  validation: {
    titleRequired: '攻略标题不能为空',
    titleLength: '攻略标题长度为2-30个字符',
    contentRequired: '攻略内容不能为空',
    contentLength: '攻略内容长度为10-1000个字符',
    themeIdRequired: '主题不能为空',
    mileageRequired: '里程不能为空',
    mileageRange: '里程范围为0.1-9999公里',
    ridingTimeRequired: '骑行时长不能为空',
    ridingTimeRange: '骑行时长范围为1-9999分钟',
    waypointsRequired: '至少需要选择途径点',
    waypointsMinimum: '至少需要选择2个途径点',
    themeNameRequired: '主题名称不能为空',
    themeImgRequired: '主题图片不能为空'
  },
  
  // 消息
  messages: {
    addSuccess: '添加成功',
    updateSuccess: '更新成功',
    deleteSuccess: '删除成功',
    enableSuccess: '启用成功',
    disableSuccess: '禁用成功',
    batchDeleteSuccess: '批量删除成功',
    batchEnableSuccess: '批量启用成功',
    batchDisableSuccess: '批量禁用成功',
    loadError: '数据加载失败',
    operationError: '操作失败',
    noImage: '暂无图片',
    waypointsSaved: '途径点保存成功',
    waypointsSelected: '已选择{count}个途径点',
    uploadSuccess: '上传成功',
    uploadError: '上传失败'
  },
  
  // 对话框标题
  dialog: {
    add: '添加骑游攻略',
    edit: '编辑骑游攻略',
    addTheme: '添加主题',
    editTheme: '编辑主题',
    detail: '攻略详情',
    mapPreview: '地图预览',
    mapManagement: '途径点管理',
    selectWaypoints: '选择途径点'
  },
  
  // 地图相关
  map: {
    placeholder: '谷歌地图预览区域',
    integration: '此处可集成谷歌地图API显示路线',
    startPoint: '起点',
    endPoint: '终点',
    routeInfo: '路线信息',
    start: '起',
    end: '终',
    routePreview: '路线预览',
    mapPreview: '地图预览',
    noRouteData: '暂无路线数据',
    zoomIn: '放大',
    zoomOut: '缩小',
    routeOverview: '路线概览',
    kilometers: '公里',
    duration: '时长',
    participants: '参与',
    unknownLocation: '未知地点',
    fitToRoute: '适应路线',
    selectRoutePoints: '选择路线途径点',
    waypointsList: '途径点列表',
    waypointName: '途径点名称',
    clickMapToAdd: '点击地图添加途径点',
    clearAll: '清空所有',
    waypointLimit: '最多只能添加10个途径点',
    latitude: '纬度',
    longitude: '经度',
    address: '地址',
    noWaypoints: '暂无途径点，请点击按钮选择'
  },
  
  // 通用
  common: {
    search: '搜索',
    reset: '重置',
    add: '添加',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确定',
    close: '关闭',
    loading: '加载中...',
    noData: '暂无数据',
    operation: '操作',
    status: '状态',
    selectAll: '全选',
    pleaseSelect: '请选择'
  }
} 