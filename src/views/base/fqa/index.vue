<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('base.fqa.customerName')" prop="customerId">
        <el-select v-model="queryParams.customerId" :placeholder="$t('base.fqa.customerNameInput')" clearable
          size="small" style="width: 190px">
          <el-option v-for="dict in customerNameOptions" :key="dict.key" :label="dict.value" :value="dict.key" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('base.fqa.nameOrCodeOrBrief')" prop="key">
        <el-input v-model="queryParams.key" :placeholder="$t('base.fqa.issueOrDescribeInput')" clearable size="small"
          style="width: 190px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t("base.fqa.search")
        }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("base.fqa.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          hasPerminone="['bike:customer:add']">{{ $t("base.fqa.newAdd") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" size="mini" @click="handleAuth(0)"
          hasPerminone="['sys:update:auth']">{{ $t("base.fqa.startUsing") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleAuth(1)"
          hasPerminone="['sys:update:auth']">{{ $t("base.fqa.forbidden") }}</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="fqaList" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="65" align="center"></el-table-column>
      <el-table-column :label="$t('base.fqa.serialNumber')" type="index" width="65" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.p - 1) * queryParams.l + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.fqa.customerName')" prop="customerName " align="center" width="125" />
      <el-table-column :label="$t('base.fqa.questionName')" prop="title" :show-overflow-tooltip="true" align="left"
        header-align="center" />
      <el-table-column :label="$t('base.fqa.sort')" prop="sort" align="center" width="80" />
      <el-table-column :label="$t('base.fqa.activatedState')" align="center" width="125">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.fqa.operation')" align="center" class-name="small-padding fixed-width"
        width="125">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)">{{
            $t("base.fqa.update")
          }}</el-button>
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">{{
            $t("base.fqa.particulars")
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="730px" append-to-body>
      <el-form ref="form" label-position="top" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('base.fqa.questionName')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('base.fqa.questionNameInput')" />
        </el-form-item>
        <el-form-item :label="$t('base.fqa.sort')" prop="sort">
          <el-input v-model="form.sort" type="number" :placeholder="$t('base.fqa.sortInput')" />
        </el-form-item>

        <el-form-item :label="$t('base.fqa.reply')" prop="reply">
          <tinymce v-if="open" height="267" v-model="form.reply" :placeholder="$t('base.fqa.replyInput')"></tinymce>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("base.fqa._confirm")
        }}</el-button>
        <el-button @click="cancel">{{ $t("base.fqa._cancel") }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :close-on-click-modal="false" :visible.sync="dialogTableVisible" :title="$t('bike.model.particulars')">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <div class="text-orange label flex justify-between align-center">
            <span> {{ detailData.title }}</span>

            <el-tag :type="detailData.status == 0 ? 'primary' : 'danger'">
              {{
                detailData.status == 0
                  ? $t("base.fqa.activated")
                  : $t("base.fqa.activatedNot")
              }}
            </el-tag>
          </div>
        </div>
        <div v-html="detailData.reply"></div>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { listFqa, addfqa, fqaupdate, fqaAuth } from "@/api/base/fqa";
import tinymce from "@/components/Editor";
import { listDictCustomers } from '@/api/base/dict';
export default {
  components: {
    tinymce,
  },
  data() {
    return {
      dialogTableVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 总条数
      total: 0,
      // 用户表格数据
      fqaList: [],
      //所属客户下拉搜索列表
      customerNameOptions: [],
      // 弹出层标题
      // 是否显示弹出层
      open: false,
      title: "",
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined,
        customerId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          {
            required: true,
            message: this.$t("base.fqa.questionNameNotNull"),
            trigger: "blur",
          },
        ],
        reply: [
          {
            required: true,
            message: this.$t("base.fqa.replyNotNull"),
            trigger: "blur",
          },
        ],
      },
      detailData: {},
      textContent: "",
      drawer: false,
    };
  },
  created() {
    listDictCustomers().then((response) => {
      this.customerNameOptions = response.data;
    });
  },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listFqa(this.queryParams).then((response) => {
        this.fqaList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    handleDetail(item) {
      this.dialogTableVisible = true;
      this.detailData = item;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleLook(row) {
      this.drawer = true;
      this.textContent = row;
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        key: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("base.fqa.addFQA");
    },
    handleUpdate(row) {
      // this.reset();
      this.form = row;
      this.open = true;
      this.title = this.$t("base.fqa.updateFQA");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 0
          ? this.$t("base.fqa.startUsing")
          : this.$t("base.fqa.blockUp");
      this.$confirm(this.$t("base.fqa.sure"), this.$t("base.fqa.warn"), {
        confirmButtonText: this.$t("base.fqa.confirm"),
        cancelButtonText: this.$t("base.fqa.cancel"),
        type: this.$t("base.fqa.warning"),
      })
        .then(() => {
          this.loading = true;
          let data = [];
          let authData = { id: row.id, status: row.status };
          data.push(authData);

          fqaAuth(data).then((response) => {
            this.msgSuccess(text + this.$t("base.fqa.succeed"));
            this.loading = false;
          });
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    handleAuth(status) {
      let text =
        status === 0
          ? this.$t("base.fqa.startUsing")
          : this.$t("base.fqa.blockUp");
      this.$confirm(this.$t("base.fqa.sure"), this.$t("base.fqa.warn"), {
        confirmButtonText: this.$t("base.fqa.confirm"),
        cancelButtonText: this.$t("base.fqa.cancel"),
        type: this.$t("base.fqa.warning"),
      })
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let authData = { id: this.ids[i], status: status };
            data.push(authData);
          }
          this.loading = false;
          fqaAuth(data).then((response) => {
            this.msgSuccess(text + this.$t("base.fqa.succeed"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(function () {
          status = status === 0 ? 1 : 0;
        });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            fqaupdate(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("base.fqa.updateSucceed"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addfqa(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("base.fqa.addSucceed"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
  },
};
</script>
