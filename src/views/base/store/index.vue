<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('base.store.shopName')" prop="name">
        <el-input v-model.trim="queryParams.name" :placeholder="$t('base.store.shopNameInput')" clearable />
      </el-form-item>
      <el-form-item :label="$t('base.store.createTime')">
        <el-date-picker v-model="dateRange" value-format="timestamp" type="datetimerange" range-separator="-"
          :start-placeholder="$t('acc.user.beginDate')" :end-placeholder="$t('acc.user.endDate')" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("base.store.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.store.reset") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("base.store.newAdd") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="storeList" :height="tableHeight()">
      <el-table-column type="expand">
        <template v-slot="{ row }">
          <el-descriptions class="margin-tb" direction="vertical" :column="9" border>
            <el-descriptions-item :label="$t('base.store.longitude')" label-class-name="text-green">
              <span v-NoData="row.longitude"></span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('base.store.latitude')" label-class-name="text-green">
              <span v-NoData="row.latitude"></span>
            </el-descriptions-item>
            <el-descriptions-item label="monday" label-class-name="text-green">
              <span v-NoData="row.monday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="tuesday" label-class-name="text-green">
              <span v-NoData="row.tuesday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="wednesday" label-class-name="text-green">
              <span v-NoData="row.wednesday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="thursday" label-class-name="text-green">
              <span v-NoData="row.thursday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="friday" label-class-name="text-green">
              <span v-NoData="row.friday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="saturday" label-class-name="text-green">
              <span v-NoData="row.saturday"></span>
            </el-descriptions-item>
            <el-descriptions-item label="sunday" label-class-name="text-green">
              <span v-NoData="row.sunday"></span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('base.store.desc')" :span="9" label-class-name="text-green">
              <span v-NoData="row.desc"></span>
            </el-descriptions-item>
            <el-descriptions-item v-if="row.imgs" :label="$t('base.store.elseImgs')" :span="9"
              label-class-name="text-green">
              <el-row type="flex">
                <el-card class="margin-right-xs" style="height: 80px;" :body-style="{ padding: '0px' }" shadow="hover"
                  v-for="(item, index) in row.imgs ? row.imgs.split(',') : []" :key="index">
                  <preview-img :imgUrl="item" />
                </el-card>
              </el-row>
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.store.shopName')" prop="name" align="center" />
      <el-table-column :label="$t('base.store.imgs')" prop="imgs" align="center">
        <template slot-scope="scope">
          <preview-img :imgUrl="scope.row.cover" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.store.address')" prop="address" align="center">
        <span slot-scope="scope" v-NoData="scope.row.address"></span>
      </el-table-column>
      <el-table-column :label="$t('base.store.contact')" prop="contact" align="center">
        <span slot-scope="scope" v-NoData="scope.row.contact"></span>
      </el-table-column>
      <el-table-column :label="$t('base.store.contactPhone')" prop="contactPhone" align="center">
        <span slot-scope="scope" v-NoData="scope.row.contactPhone"></span>
      </el-table-column>
      <el-table-column :label="$t('base.store.website')" align="center" prop="website">
        <template slot-scope="scope">
          <el-link v-show="scope.row.website" type="primary" :href="scope.row.website" target="_blank">
            {{ scope.row.website }}
          </el-link>
          <span v-show="!scope.row.website">- - -</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.store.state')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.store.createTime')" align="center" sortable prop="createTime">
        <span slot-scope="scope" v-NoData="parseTime(scope.row.createTime)"></span>
      </el-table-column>
      <el-table-column :label="$t('base.store.operation')" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row)" hasPerminone="['base:medal:edit']">
            {{ $t("base.store.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 添加 -->
    <el-dialog :visible.sync="open" :title="title" width="910px" append-to-body center top="8vh"
      :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('base.store.storeBasicInfo')" name="first">
            <fieldset class="fieldset-box">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="$t('base.store.shopName')" prop="name">
                    <el-input v-model.trim="form.name" clearable :placeholder="$t('form.input') + $t('base.store.shopName')
                      " />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.labels')" prop="labels">
                    <el-input v-model.trim="form.labels" clearable
                      :placeholder="$t('form.input') + $t('base.store.labels')" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.weight')" prop="weight">
                    <el-input v-model.number="form.weight" type="number" clearable
                      :placeholder="$t('form.input') + $t('base.store.weight')" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('base.store.address')" prop="address">
                    <el-input v-model.trim="form.address" clearable
                      :placeholder="$t('form.input') + $t('base.store.address')" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="longitudeAndLatitude">
                    <div slot="label" class="flex align-center">
                      {{ $t("base.store.longitudeAndLatitude") }}
                      <Tooltip :content="$t('base.store.longitudeAndLatitudeScheme') +
                        ': 22.517076,114.072749'
                        " />
                    </div>

                    <el-input v-model.trim="form.longitudeAndLatitude" clearable :placeholder="$t('form.input') + $t('base.store.longitudeAndLatitude')
                      ">
                      <el-button slot="append" type="primary" @click="handleOpenMapPicker">
                        {{ $t("base.store.positionPick") }}
                      </el-button>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.manager')" prop="manager">
                    <el-input v-model.trim="form.manager" clearable
                      :placeholder="$t('form.input') + $t('base.store.manager')" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.contact')" prop="contact">
                    <el-input v-model.trim="form.contact" clearable
                      :placeholder="$t('form.input') + $t('base.store.contact')" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.contactPhone')" prop="contactPhone">
                    <el-input v-model.trim.lazy="form.contactPhone" clearable :placeholder="$t('form.input') + $t('base.store.contactPhone')
                      " />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('base.store.contactEmail')" prop="contactEmail">
                    <el-input v-model.trim.lazy="form.contactEmail" clearable :placeholder="$t('form.input') + $t('base.store.contactEmail')
                      " />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item :label="$t('base.store.website')" prop="website">
                <el-input v-model.trim.lazy="form.website" clearable
                  :placeholder="$t('form.input') + $t('base.store.website')" />
              </el-form-item>
              <el-form-item :label="$t('base.store.desc')" prop="desc" class="width-all">
                <el-input v-model="form.desc" type="textarea" :placeholder="$t('form.input') + $t('base.store.desc')" />
              </el-form-item>
              <el-row>
                <el-col :span="12">
                  <el-form-item :label="$t('base.store.imgs')" prop="cover">
                    <ImageCoverUpload v-model="form.cover" :width="80" :height="80" :limit="1" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('base.store.elseImg')" prop="imgs">
                    <ImageCoverUpload v-model="form.imgs" :width="80" :height="80" :limit="8" />
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-tab-pane>
          <el-tab-pane :label="$t('base.store.storeOpenHours')" name="second">
            <fieldset class="fieldset-box">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="Monday" prop="monday">
                    <TimePicker v-model="form.monday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Tuesday" prop="tuesday">
                    <TimePicker v-model="form.tuesday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Wednesday" prop="wednesday">
                    <TimePicker v-model="form.wednesday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Thursday" prop="thursday">
                    <TimePicker v-model="form.thursday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Friday" prop="friday">
                    <TimePicker v-model="form.friday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Saturday" prop="saturday">
                    <TimePicker v-model="form.saturday" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Sunday" prop="sunday">
                    <TimePicker v-model="form.sunday" />
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="isBtnLoading" @click="submitForm">
          {{ $t("base.store.confirm") }}
        </el-button>
        <el-button @click="cancel">{{ $t("base.store.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 地图坐标拾取器 -->
    <el-dialog :visible.sync="mapPickerVisible" title="选择门店位置" width="80%" append-to-body center top="5vh"
      :close-on-click-modal="false">
      <div style="height: 600px;">
        <MapCoordinatePicker :initial-coordinate="mapCenter" :zoom="13"
          @coordinate-change="handleMapCoordinateSelected" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="mapPickerVisible = false">
          {{ $t("base.store.cancel") }}
        </el-button>
        <el-button type="primary" @click="mapPickerVisible = false">
          {{ $t("base.store.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStore, addStore, eidtStore, authStore } from "@/api/bike/store";
import { commonJs } from "@/mixinFile/common";
import {
  checkValidPhone,
  checkValidEmail,
  checkValidUrl
} from "@/utils/validate";

export default {
  mixins: [commonJs],
  components: {
    TimePicker: () => import("@/components/TimePicker"),
    ImageCoverUpload: () => import("@/components/ImageCoverUpload"),
    MapCoordinatePicker: () => import("@/components/MapCoordinatePicker")
  },
  data() {
    const isValidLatLong = latLongString => {
      const latLongRegex = /^(\-?[1-8]?\d(\.\d{1,20})?)(,|，)\s*(\-?1?\d{1,2}\d?(\.\d{1,20})?)$/;
      return latLongRegex.test(latLongString);
    };

    const checkLongitudeAndLatitude = (rule, value, callback) => {
      if (value && !isValidLatLong(value)) {
        return callback(
          new Error(this.$t("base.store.longitudeAndLatitudeErr"))
        );
      } else {
        callback();
      }
    };

    return {
      activeName: "first",
      aFn: authStore,
      // 用户表格数据
      storeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 地图选择器
      mapPickerVisible: false,
      mapCenter: { lng: 116.3974, lat: 39.9093 }, // 默认北京
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        name: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: this.$t("base.store.shopNameNotNull"),
            trigger: "blur"
          }
        ],
        desc: [
          {
            required: false,
            message: this.$t("base.store.descNotNull"),
            trigger: "blur"
          }
        ],
        address: [
          {
            required: false,
            message: this.$t("base.store.addressNotNull"),
            trigger: "blur"
          }
        ],
        contact: [
          {
            required: false,
            message: this.$t("base.store.contactNotNull"),
            trigger: "blur"
          }
        ],
        contactPhone: [
          {
            required: false,
            validator: checkValidPhone,
            trigger: ["blur", "change"]
          }
        ],
        endDate: [
          {
            required: false,
            message: this.$t("base.store.endDateNotNull"),
            trigger: "blur"
          }
        ],
        labels: [
          {
            required: false,
            message: this.$t("base.store.labelsNotNull"),
            trigger: "blur"
          }
        ],
        latitude: [
          {
            required: false,
            message: this.$t("base.store.longitudeNotNull"),
            trigger: "blur"
          }
        ],
        longitude: [
          {
            required: false,
            message: this.$t("base.store.latitudeNotNull"),
            trigger: "blur"
          }
        ],
        longitudeAndLatitude: [
          {
            required: false,
            validator: checkLongitudeAndLatitude,
            trigger: "blur"
          }
        ],
        manager: [
          {
            required: false,
            message: this.$t("base.store.managerNotNull"),
            trigger: "blur"
          }
        ],
        contactEmail: [
          {
            required: false,
            validator: checkValidEmail,
            trigger: ["blur", "change"]
          }
        ],
        website: [
          {
            required: false,
            validator: checkValidUrl,
            trigger: ["blur", "change"]
          }
        ],
        rescuePhone: [
          {
            required: false,
            message: this.$t("base.store.firstAidCallNotNull"),
            trigger: "blur"
          }
        ],
        score: [
          {
            required: false,
            message: this.$t("base.store.scoreNotNull"),
            trigger: "blur"
          }
        ],
        weight: [
          {
            required: false,
            message: this.$t("base.store.weightNotNull"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      listStore(
        this.addDateRange(this.queryParams, this.dateRange, {
          timestampLen: 13
        })
      )
        .then(res => {
          const { list, total } = res.data;
          this.storeList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleOpenMapPicker() {
      // 如果已有坐标，设置为地图中心点
      const { longitudeAndLatitude } = this.form;
      if (longitudeAndLatitude) {
        const coords = longitudeAndLatitude.split(',');
        if (coords.length === 2) {
          const lat = parseFloat(coords[0].trim());
          const lng = parseFloat(coords[1].trim());
          if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
            this.mapCenter = { lng, lat };
          } else {
            // 坐标无效时使用默认值
            this.mapCenter = { lng: 116.3974, lat: 39.9093 };
          }
        }
      } else {
        // 没有坐标时使用默认值
        this.mapCenter = { lng: 116.3974, lat: 39.9093 };
      }
      this.mapPickerVisible = true;
    },

    // 地图坐标选择回调
    handleMapCoordinateSelected(coordinate) {
      if (coordinate) {
        const coordinates = `${coordinate.latitude},${coordinate.longitude}`;
        this.form.longitudeAndLatitude = coordinates;
        this.$message.success('坐标选择成功');
      }
    },
    // 表单重置
    reset() {
      this.form = {
        monday: null,
        tuesday: null,
        wednesday: null,
        thursday: null,
        friday: null,
        saturday: null,
        sunday: null
      };
      this.activeName = "first";
      this.resetForm("form");
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("base.store.addShop");
    },
    handleUpdate(row) {
      this.reset();
      const longitudeAndLatitude =
        row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : "";
      this.form = Object.assign({}, row, { longitudeAndLatitude });
      this.open = true;
      this.title = this.$t("base.store.updateShop");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.isBtnLoading = true;
          if (this.form.longitudeAndLatitude) {
            const [longitude, latitude] = this.form.longitudeAndLatitude.split(
              ","
            );

            this.form.longitude = longitude;
            this.form.latitude = latitude;
          }

          if (this.form.id !== undefined) {
            eidtStore(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.store.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          } else {
            addStore(this.form)
              .then(response => {
                if (response.code === 200) {
                  this.msgSuccess(this.$t("base.store.updateSucceed"));
                  this.open = false;
                  this.getList();
                }
              })
              .finally(() => {
                this.isBtnLoading = false;
              });
          }
        } else {
          this.activeName = "first";
        }
      });
    }
  }
};
</script>
