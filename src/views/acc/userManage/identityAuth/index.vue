<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="$t('acc.identityAuth.userName')" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('acc.identityAuth.status')" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择审核状态" clearable style="width: 240px">
          <el-option v-for="(dict, index) in statusOptions" :key="index" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="提交时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{ $t('base.carexception.search')
        }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('base.carexception.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" @click="handleAdd"
          v-if="isDev || checkPermi(['system:identityAuth:add'])">{{ $t('queryParams.add') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="authList" @selection-change="handleSelectionChange" :height="tableHeight()">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('acc.identityAuth.userName')" align="center" prop="userName" />
      <el-table-column :label="$t('acc.identityAuth.realName')" align="center" prop="realName" />
      <el-table-column :label="$t('acc.identityAuth.idNumber')" align="center" prop="idNumber"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('acc.identityAuth.status')" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === 'pending' ? 'warning' : (scope.row.status === 'approved' ? 'success' : 'danger')">
            {{ statusFormat(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.identityAuth.submitTime')" align="center" prop="submitTime" width="180" />
      <el-table-column :label="$t('acc.identityAuth.auditTime')" align="center" prop="auditTime" width="180" />
      <el-table-column :label="$t('acc.identityAuth.operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-if="isDev || checkPermi(['system:identityAuth:query'])">{{ $t('base.detail') }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-if="(scope.row.status === 'pending' && (isDev || checkPermi(['system:identityAuth:edit'])))">{{
              $t('queryParams.update') }}</el-button>
          <el-button v-if="scope.row.status === 'pending'" size="mini" type="text" icon="el-icon-check"
            @click="handleApprove(scope.row)">{{ $t('acc.identityAuth.approve') }}</el-button>
          <el-button v-if="scope.row.status === 'pending'" size="mini" type="text" icon="el-icon-close"
            @click="handleReject(scope.row)">{{ $t('acc.identityAuth.reject') }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-if="isDev || checkPermi(['system:identityAuth:remove'])">{{ $t('queryParams.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改身份认证对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" @closed="reset">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('acc.identityAuth.userName')" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('acc.identityAuth.realName')" prop="realName">
              <el-input v-model="form.realName" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('acc.identityAuth.idNumber')" prop="idNumber">
          <el-input v-model="form.idNumber" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item :label="$t('acc.identityAuth.frontImage')" prop="frontImage">
          <el-upload class="avatar-uploader" :action="uploadImgUrl" :headers="headers" :show-file-list="false"
            :on-success="handleFrontImageSuccess" :before-upload="beforeImageUpload">
            <img v-if="form.frontImage" :src="form.frontImage" class="id-card-image">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="el-upload__tip">{{ $t('acc.identityAuth.imageUploadTip') }}</div>
        </el-form-item>
        <el-form-item :label="$t('acc.identityAuth.backImage')" prop="backImage">
          <el-upload class="avatar-uploader" :action="uploadImgUrl" :headers="headers" :show-file-list="false"
            :on-success="handleBackImageSuccess" :before-upload="beforeImageUpload">
            <img v-if="form.backImage" :src="form.backImage" class="id-card-image">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="el-upload__tip">{{ $t('acc.identityAuth.imageUploadTip') }}</div>
        </el-form-item>
        <el-form-item :label="$t('acc.identityAuth.selfieImage')" prop="selfieImage">
          <el-upload class="avatar-uploader" :action="uploadImgUrl" :headers="headers" :show-file-list="false"
            :on-success="handleSelfieImageSuccess" :before-upload="beforeImageUpload">
            <img v-if="form.selfieImage" :src="form.selfieImage" class="id-card-image">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="el-upload__tip">{{ $t('acc.identityAuth.imageUploadTip') }}</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('dialog.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('dialog.cancel') }}</el-button>
      </div>
    </el-dialog>

    <!-- 查看身份认证详情对话框 -->
    <el-dialog :title="viewTitle" :visible.sync="viewOpen" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item :label="$t('acc.identityAuth.userName')">{{ viewForm.userName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.realName')">{{ viewForm.realName }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.idNumber')">{{ viewForm.idNumber }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.status')">
          <el-tag
            :type="viewForm.status === 'pending' ? 'warning' : (viewForm.status === 'approved' ? 'success' : 'danger')">
            {{ statusTextFormat(viewForm) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.submitTime')">{{ viewForm.submitTime
        }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.auditTime')">{{ viewForm.auditTime || '-'
        }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.auditReason')">{{ viewForm.auditReason || '-'
        }}</el-descriptions-item>
        <el-descriptions-item :label="$t('acc.identityAuth.auditBy')">{{ viewForm.auditBy || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="id-card-images">
        <div class="id-card-container">
          <h4>{{ $t('acc.identityAuth.frontImage') }}</h4>
          <img :src="viewForm.frontImage" class="id-card-preview" />
        </div>
        <div class="id-card-container">
          <h4>{{ $t('acc.identityAuth.backImage') }}</h4>
          <img :src="viewForm.backImage" class="id-card-preview" />
        </div>
        <div class="id-card-container">
          <h4>{{ $t('acc.identityAuth.selfieImage') }}</h4>
          <img :src="viewForm.selfieImage" class="id-card-preview" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">{{ $t('dialog.close') }}</el-button>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog :title="auditTitle" :visible.sync="auditOpen" width="500px">
      <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="100px">
        <el-form-item :label="$t('acc.identityAuth.auditReason')" prop="auditReason">
          <el-input type="textarea" :rows="4" v-model="auditForm.auditReason"
            :placeholder="$t('acc.identityAuth.reasonInput')"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">{{ $t('dialog.confirm') }}</el-button>
        <el-button @click="auditOpen = false">{{ $t('dialog.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
