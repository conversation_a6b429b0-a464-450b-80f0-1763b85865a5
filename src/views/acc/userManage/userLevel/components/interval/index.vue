<template>
  <div class="app-container">
    <div class="toolbar">
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form" @submit.native.prevent>
        <el-form-item :label="$t('acc.userLevel.interval.intervalName')" prop="levelName">
          <el-input v-model="queryParams.levelName" :placeholder="$t('acc.userLevel.interval.intervalNameInput')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            {{ $t("base.carexception.search") }}
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">
            {{ $t("base.carexception.reset") }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="right-button">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("acc.userLevel.interval.addInterval") }}
        </el-button>
      </div>
    </div>

    <el-table v-loading="loading" :data="intervalList" :height="tableHeight(-65)" border>
      <el-table-column align="center" :label="$t('acc.userLevel.index')" width="55">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.userLevel.interval.intervalName')" prop="levelName"
        :show-overflow-tooltip="true" />
      <el-table-column align="center" :label="$t('acc.userLevel.interval.minLevel')" prop="minLevel" />
      <el-table-column align="center" :label="$t('acc.userLevel.interval.maxLevel')" prop="maxLevel" />
      <!-- <el-table-column align="center" :label="$t('acc.userLevel.interval.minPoint')" prop="minPoint" />
      <el-table-column align="center" :label="$t('acc.userLevel.interval.maxPoint')" prop="maxPoint" /> -->
      <el-table-column align="center" :label="$t('acc.userLevel.interval.grayIcon')" prop="grayIcon">
        <template slot-scope="scope">
          <preview-img v-if="scope.row.grayIcon" :imgUrl="scope.row.grayIcon" class="icon-preview" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.userLevel.interval.lightenIcon')" prop="lightenIcon">
        <template slot-scope="scope">
          <preview-img v-if="scope.row.lightenIcon" :imgUrl="scope.row.lightenIcon" class="icon-preview" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.userLevel.createTime')" prop="createTime" width="180">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.identityAuth.operation')" width="240">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="() => handleUpdate(scope.row)">{{
            $t("queryParams.update") }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-setting" @click="() => handleSetTasks(scope.row)">{{
            $t("acc.userLevel.interval.setTasks") }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" class="text-red"
            @click="() => handleDelete(scope.row)">{{
              $t("queryParams.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改用户等级区间对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" @closed="reset">
      <el-form ref="form" :model="form" :rules="formRules" label-width="140px">
        <el-form-item :label="$t('acc.userLevel.interval.intervalName')" prop="levelName">
          <el-input v-model="form.levelName" :placeholder="$t('acc.userLevel.interval.intervalNameInput')" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('acc.userLevel.interval.minLevel')" prop="minLevel">
              <el-input-number v-model="form.minLevel" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('acc.userLevel.interval.maxLevel')" prop="maxLevel">
              <el-input-number v-model="form.maxLevel" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('acc.userLevel.interval.minPoint')" prop="minPoint">
              <el-input-number v-model="form.minPoint" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('acc.userLevel.interval.maxPoint')" prop="maxPoint">
              <el-input-number v-model="form.maxPoint" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('acc.userLevel.interval.grayIcon')" prop="grayIcon">
              <ImageCoverUpload v-model="form.grayIcon" :width="120" :height="120" :limit="1" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('acc.userLevel.interval.lightenIcon')" prop="lightenIcon">
              <ImageCoverUpload v-model="form.lightenIcon" :width="120" :height="120" :limit="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t("dialog.confirm") }}</el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 设置任务对话框 -->
    <el-dialog :title="$t('acc.userLevel.interval.taskSelectionTitle')" :visible.sync="taskOpen" width="1400px"
      @closed="resetTasks">
      <div class="task-selection-container">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :inline="true">
            <el-form-item :label="$t('acc.userLevel.interval.taskName')">
              <el-input v-model="taskSearchForm.name" :placeholder="$t('acc.userLevel.interval.taskNameInput')"
                clearable style="width: 180px" />
            </el-form-item>
            <el-form-item :label="$t('acc.userLevel.interval.taskEnName')">
              <el-input v-model="taskSearchForm.enName" :placeholder="$t('acc.userLevel.interval.taskEnNameInput')"
                clearable style="width: 180px" />
            </el-form-item>
            <el-form-item :label="$t('acc.userLevel.interval.taskCode')">
              <el-input v-model="taskSearchForm.taskCode" :placeholder="$t('acc.userLevel.interval.taskCodeInput')"
                clearable style="width: 180px" />
            </el-form-item>
            <el-form-item :label="$t('acc.userLevel.interval.taskType')">
              <el-select v-model="taskSearchForm.type" :placeholder="$t('acc.userLevel.interval.taskTypeSelect')"
                clearable style="width: 120px">
                <el-option :label="$t('acc.userLevel.interval.taskTypeBasic')" :value="0" />
                <el-option :label="$t('acc.userLevel.interval.taskTypeAdvanced')" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getTaskList">{{ $t("base.feedback.search") }}</el-button>
              <el-button @click="resetTaskSearch">{{ $t("base.feedback.reset") }}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 任务列表 -->
        <div class="task-list-container">
          <el-table ref="taskTable" v-loading="taskLoading" :data="taskList"
            @selection-change="handleTaskSelectionChange" height="400">
            <el-table-column type="selection" width="50" />
            <el-table-column prop="name" :label="$t('acc.userLevel.interval.taskName')" width="150"
              show-overflow-tooltip />
            <el-table-column prop="enName" :label="$t('acc.userLevel.interval.taskEnName')" width="150"
              show-overflow-tooltip />
            <el-table-column prop="taskCode" :label="$t('acc.userLevel.interval.taskCode')" />
            <el-table-column prop="description" :label="$t('acc.userLevel.interval.taskDescription')"
              show-overflow-tooltip />
            <el-table-column prop="coins" :label="$t('acc.userLevel.interval.taskCoins')" width="80" align="center" />
            <el-table-column prop="point" :label="$t('acc.userLevel.interval.taskPoint')" width="80" align="center" />
            <el-table-column prop="condition" :label="$t('acc.userLevel.interval.taskCondition')" width="80"
              align="center" />
            <el-table-column prop="cycle" :label="$t('acc.userLevel.interval.taskCycle')" width="80" align="center" />
            <el-table-column prop="type" :label="$t('acc.userLevel.interval.taskType')" width="100" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.type === 0 ? 'primary' : 'warning'">
                  {{ scope.row.type === 0 ? $t('acc.userLevel.interval.taskTypeBasic') :
                    $t('acc.userLevel.interval.taskTypeAdvanced') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="model" :label="$t('acc.userLevel.interval.taskModel')" width="100" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.model === 0">骑行</span>
                <span v-else-if="scope.row.model === 1">社区</span>
                <span v-else-if="scope.row.model === 2">摄影</span>
                <span v-else-if="scope.row.model === 3">攻略</span>
                <span v-else-if="scope.row.model === 4">组队</span>
                <span v-else-if="scope.row.model === 5">商场</span>
                <span v-else-if="scope.row.model === 6">运动计划</span>
                <span v-else-if="scope.row.model === 7">个人页面</span>
                <span v-else-if="scope.row.model === 8">车况</span>
                <span v-else-if="scope.row.model === 9">车友圈</span>
                <span v-else-if="scope.row.model === 10">官方</span>
                <span v-else>{{ scope.row.model }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('acc.userLevel.interval.taskStatus')" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                  {{ scope.row.status === 0 ? $t('acc.userLevel.enabled') : $t('acc.userLevel.disabled') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 已选任务 -->
        <div class="selected-tasks" v-if="selectedTasks.length > 0">
          <h4>{{ $t('acc.userLevel.interval.selectedTasks') }} ({{ selectedTasks.length }})</h4>
          <div class="selected-task-tags">
            <el-tag v-for="task in selectedTasks" :key="task.id" closable @close="removeSelectedTask(task)"
              style="margin: 2px;">
              {{ task.name }}
            </el-tag>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="taskOpen = false">{{ $t("dialog.cancel") }}</el-button>
        <el-button type="primary" @click="submitTasks" :loading="taskSubmitLoading">{{ $t("dialog.confirm")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserLevelIntervals, addUserLevelInterval, updateUserLevelInterval, delUserLevelInterval, setLevelTasks, updateLevelTasks } from "@/api/acc/userLevel";
import { getTaskList } from "@/api/task";
import ImageCoverUpload from "@/components/ImageCoverUpload";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "UserLevelInterval",
  components: {
    ImageCoverUpload
  },
  data() {
    return {
      // 遮罩层 
      loading: true,
      // 总条数
      total: 0,
      // 用户等级区间表格数据
      intervalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示任务设置对话框
      taskOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        levelName: undefined
      },
      // 表单参数
      form: {},
      // 任务表单参数
      taskForm: {
        id: undefined,
        taskList: []
      },
      // 任务相关数据
      taskLoading: false,
      taskSubmitLoading: false,
      taskList: [],
      selectedTasks: [],
      hasExistingTasks: false, // 标记是否已有设置的任务
      taskSearchForm: {
        name: '',
        enName: '',
        taskCode: '',
        type: undefined,
        pageNum: 1,
        pageSize: 50
      },
      // 表单校验 - 将在computed中动态生成以支持国际化
      rules: {}
    };
  },
  computed: {
    // 动态生成表单校验规则以支持国际化
    formRules() {
      return {
        levelName: [
          { required: true, message: this.$t('acc.userLevel.interval.intervalNameRequired'), trigger: "blur" }
        ],
        minLevel: [
          { required: true, message: this.$t('acc.userLevel.interval.minLevelRequired'), trigger: "blur" }
        ],
        maxLevel: [
          { required: true, message: this.$t('acc.userLevel.interval.maxLevelRequired'), trigger: "blur" }
        ],
        minPoint: [
          { required: true, message: this.$t('acc.userLevel.interval.minPointRequired'), trigger: "blur" }
        ],
        maxPoint: [
          { required: true, message: this.$t('acc.userLevel.interval.maxPointRequired'), trigger: "blur" }
        ]
      };
    }
  },
  created() {
    this.open = false;
    this.taskOpen = false;
    this.title = "";
    this.getList();
  },

  mounted() {
    document.addEventListener('keydown', this.handleKeyDown);
  },

  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown);
  },


  methods: {
    // 时间格式化
    parseTime,
    /** 查询用户等级区间列表 */
    getList() {
      this.loading = true;

      listUserLevelIntervals(this.queryParams)
        .then(response => {
          // 根据API文档，响应结构应该是 {code, data: {list, pageNum, pageSize, pages, total}, msg}
          if (response.data && response.data.list) {
            this.intervalList = response.data.list;
            this.total = response.data.total;
          } else {
            // 兼容旧的响应格式
            this.intervalList = response.rows || response.data || [];
            this.total = response.total || 0;
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('获取用户等级区间列表失败:', error);
          this.loading = false;
        });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
    },
    /** 取消任务设置 */
    cancelTasks() {
      this.taskOpen = false;
    },
    /** 表单重置 */
    reset() {
      // 安全地重置表单，避免在组件未完全加载时出错
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
        }
        this.form = {
          id: undefined,
          levelName: undefined,
          minLevel: undefined,
          maxLevel: undefined,
          minPoint: undefined,
          maxPoint: undefined,
          grayIcon: undefined,
          lightenIcon: undefined,
          createTime: undefined
        };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        levelName: undefined
      };
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('acc.userLevel.interval.addInterval');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = { ...row };
      this.open = true;
      this.title = this.$t('acc.userLevel.interval.editInterval');
    },
    /** 设置任务按钮操作 */
    async handleSetTasks(row) {
      this.resetTasks();
      this.taskForm.id = row.id;
      this.selectedTasks = [];
      this.taskOpen = true;

      // 先获取任务列表，再从row.list中读取已设置的任务
      await this.getTaskList();
      this.setExistingTasksFromRow(row);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 根据API文档准备数据，确保字段符合"用户等级区间"模型
          const submitData = {
            id: this.form.id,
            levelName: this.form.levelName,
            minLevel: this.form.minLevel,
            maxLevel: this.form.maxLevel,
            minPoint: this.form.minPoint,
            maxPoint: this.form.maxPoint,
            grayIcon: this.form.grayIcon,
            lightenIcon: this.form.lightenIcon,
            createTime: this.form.createTime || Date.now(),
            madelList: [] // 勋章列表，如果需要的话
          };

          if (this.form.id != undefined) {
            updateUserLevelInterval(submitData).then(response => {
              this.$message.success(this.$t('acc.userLevel.updateSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('更新用户等级区间失败:', error);
            });
          } else {
            addUserLevelInterval(submitData).then(response => {
              this.$message.success(this.$t('acc.userLevel.addSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('添加用户等级区间失败:', error);
            });
          }
        }
      });
    },
    /** 提交任务设置 */
    async submitTasks() {
      if (this.selectedTasks.length === 0) {
        this.$message.warning(this.$t('acc.userLevel.interval.noTasksSelected'));
        return;
      }

      this.taskSubmitLoading = true;
      try {
        const taskData = {
          id: this.taskForm.id,
          list: this.selectedTasks.map(task => task.id)
        };

        // 根据是否已有任务设置来决定调用新增还是编辑API
        let response;
        if (this.hasExistingTasks) {
          // 有已设置的任务，调用编辑API
          response = await updateLevelTasks(taskData);
        } else {
          // 没有已设置的任务，调用新增API
          response = await setLevelTasks(taskData);
        }

        if (response.code === 200) {
          this.$message.success(this.$t('acc.userLevel.interval.taskSetSuccess'));
          this.taskOpen = false;
          // 刷新表格数据以显示最新的任务设置
          this.getList();
        }
      } catch (error) {
        console.error('设置任务失败:', error);
        this.$message.error('设置任务失败');
      } finally {
        this.taskSubmitLoading = false;
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(this.$t('acc.userLevel.deleteConfirm'), {
        confirmButtonText: this.$t('dialog.confirm'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: 'warning'
      }).then(() => {
        return delUserLevelInterval(row.id);
      }).then(() => {
        this.getList();
        this.$message.success(this.$t('acc.userLevel.deleteSuccess'));
      }).catch(() => { });
    },
    /** 任务表单重置 */
    resetTasks() {
      this.taskForm = {
        id: undefined,
        taskList: []
      };
      this.selectedTasks = [];
      this.taskList = [];
      this.hasExistingTasks = false;
      this.taskSearchForm = {
        name: '',
        enName: '',
        taskCode: '',
        type: undefined,
        pageNum: 1,
        pageSize: 50
      };
    },

    /** 获取任务列表 */
    async getTaskList() {
      this.taskLoading = true;
      try {
        const params = {
          p: this.taskSearchForm.pageNum,
          l: this.taskSearchForm.pageSize
        };

        if (this.taskSearchForm.name) {
          params.name = this.taskSearchForm.name;
        }

        if (this.taskSearchForm.enName) {
          params.enName = this.taskSearchForm.enName;
        }

        if (this.taskSearchForm.taskCode) {
          params.taskCode = this.taskSearchForm.taskCode;
        }

        if (this.taskSearchForm.type !== undefined) {
          params.type = this.taskSearchForm.type;
        }

        const response = await getTaskList(params);
        if (response.code === 200 && response.data) {
          this.taskList = response.data.list || [];
        } else {
          this.taskList = [];
          this.$message.error(response.msg || '获取任务列表失败');
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        this.$message.error('获取任务列表失败');
        this.taskList = [];
      } finally {
        this.taskLoading = false;
      }
    },

    /** 从row.list中设置已存在的任务 */
    setExistingTasksFromRow(row) {
      try {
        // 从row.list中提取taskId数组
        const existingTaskIds = (row.list || []).map(item => item.taskId);
        this.hasExistingTasks = existingTaskIds.length > 0;

        // 根据已设置的任务ID找到对应的任务对象
        const existingTasks = this.taskList.filter(task =>
          existingTaskIds.includes(task.id)
        );
        this.selectedTasks = existingTasks;

        // 等待DOM更新后设置表格的选中状态
        this.$nextTick(() => {
          if (this.$refs.taskTable) {
            this.$refs.taskTable.clearSelection();
            existingTasks.forEach(task => {
              this.$refs.taskTable.toggleRowSelection(task, true);
            });
          }
        });
      } catch (error) {
        console.error('设置已存在任务失败:', error);
        this.hasExistingTasks = false;
        this.selectedTasks = [];
      }
    },

    /** 重置任务搜索 */
    resetTaskSearch() {
      this.taskSearchForm = {
        name: '',
        enName: '',
        taskCode: '',
        type: undefined,
        pageNum: 1,
        pageSize: 50
      };
      this.getTaskList();
    },

    /** 任务选择变化 */
    handleTaskSelectionChange(selection) {
      this.selectedTasks = selection;
    },

    /** 移除已选任务 */
    removeSelectedTask(task) {
      const index = this.selectedTasks.findIndex(t => t.id === task.id);
      if (index > -1) {
        this.selectedTasks.splice(index, 1);
        // 同步更新表格中的勾选状态
        this.$nextTick(() => {
          if (this.$refs.taskTable) {
            this.$refs.taskTable.toggleRowSelection(task, false);
          }
        });
      }
    },

    /** 处理键盘事件 */
    handleKeyDown(e) {
      if (e.keyCode === 27) {
        if (this.open) {
          this.cancel();
        }
        if (this.taskOpen) {
          this.cancelTasks();
        }
      }
    },

    /** 检查权限 */
    checkPermi(permissions) {
      try {
        if (this.$store && this.$store.getters && this.$store.getters.permissions) {
          const permissionList = this.$store.getters.permissions;
          return permissions.some(permission => permissionList.includes(permission));
        }
      } catch (error) {
        console.error('检查权限失败:', error);
      }
      return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.icon-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.toolbar {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 20px;

  .search-form {
    flex: 1;
    
    :deep(.el-form-item) {
      margin-bottom: 18px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  .right-button {
    position: relative;
  }
}

/* 任务选择对话框样式 */
.task-selection-container {
  .search-area {
    border-radius: 4px;
  }

  .task-list-container {
    margin-bottom: 20px;
  }

  .selected-tasks {
    padding: 15px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border: 1px solid #d1ecf1;

    h4 {
      margin: 0 0 10px 0;
      color: #31708f;
      font-size: 14px;
    }

    .selected-task-tags {
      .el-tag {
        margin: 2px 4px 2px 0;
      }
    }
  }
}
</style>