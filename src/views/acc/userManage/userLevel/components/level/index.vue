<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">{{ $t("queryParams.add") }}</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="levelList" :height="tableHeight()" border>
      <el-table-column :label="$t('acc.userLevel.index')" width="55" align="center">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.userLevel.level')" prop="level" align="center" />
      <el-table-column :label="$t('acc.userLevel.point')" prop="point" align="center" />
      <el-table-column :label="$t('acc.userLevel.status')" prop="status" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? $t('acc.userLevel.enabled') : $t('acc.userLevel.disabled') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.userLevel.createTime')" prop="createTime" width="180">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.identityAuth.operation')" align="center" width="240">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{
            $t("queryParams.update") }}</el-button>
          <el-button class="text-green" v-if="scope.row.status === 1" size="mini" type="text" icon="el-icon-check"
            @click="handleStatusChange(scope.row, 0)">{{
              $t("acc.userLevel.enabled") }}</el-button>
          <el-button class="text-red" v-if="scope.row.status === 0" size="mini" type="text" icon="el-icon-close"
            @click="handleStatusChange(scope.row, 1)">{{
              $t("acc.userLevel.disabled") }}</el-button>

          <el-button class="text-red" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{
            $t("queryParams.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改用户等级对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="400px" @closed="handleDialogClosed">
      <el-form ref="form" :model="form" :rules="formRules" label-width="120px">
        <el-form-item :label="$t('acc.userLevel.level')" prop="level">
          <el-input-number v-model="form.level" :min="1" :placeholder="$t('acc.userLevel.levelInput')"
            style="width: 100%" />
        </el-form-item>
        <el-form-item :label="$t('acc.userLevel.point')" prop="point">
          <el-input-number v-model="form.point" :min="0" :placeholder="$t('acc.userLevel.pointInput')"
            style="width: 100%" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t("dialog.confirm") }}</el-button>
        <el-button @click="cancel">{{ $t("dialog.cancel") }}</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listUserLevels, getUserLevel, delUserLevel, addUserLevel, updateUserLevel, changeUserLevelStatus } from "@/api/acc/userLevel";

export default {
  name: "LevelManagement",
  data() {
    return {
      // 遮罩层 
      loading: true,
      // 总条数
      total: 0,
      // 用户等级表格数据
      levelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        level: undefined
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    document.addEventListener('keydown', this.handleKeyDown);
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown);
  },
  computed: {
    formRules() {
      return {
        level: [
          { required: true, message: this.$t('acc.userLevel.levelRequired'), trigger: "blur" }
        ],
        point: [
          { required: true, message: this.$t('acc.userLevel.pointRequired'), trigger: "blur" }
        ]
      };
    }
  },
  methods: {
    tableHeight() {
      return window.innerHeight - 400;
    },
    /** 查询用户等级列表 */
    getList() {
      this.loading = true;
      listUserLevels(this.queryParams)
        .then(response => {
          if (response.data && response.data.list) {
            this.levelList = response.data.list;
            this.total = response.data.total;
          } else {
            this.levelList = response.rows || response.data || [];
            this.total = response.total || 0;
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('获取用户等级列表失败:', error);
          this.loading = false;
        });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
    },
    /** 对话框关闭事件 */
    handleDialogClosed() {
      this.reset();
    },
    /** 表单重置 */
    reset() {

      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
        }
        this.form = {
          id: undefined,
          level: undefined,
          point: undefined,
          status: 0
        };
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('acc.userLevel.addTitle');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form = {
        id: row.id,
        level: row.level,
        point: row.point,
        status: row.status,
        createTime: row.createTime
      };
      this.open = true;
      this.title = this.$t('acc.userLevel.updateTitle');
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const submitData = {
            level: this.form.level,
            point: this.form.point,
            status: this.form.status || 0
          };

          if (this.form.id != undefined) {
            submitData.id = this.form.id;
            updateUserLevel(submitData).then(response => {
              this.$message.success(this.$t('acc.userLevel.updateSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('更新用户等级失败:', error);
            });
          } else {
            addUserLevel(submitData).then(response => {
              this.$message.success(this.$t('acc.userLevel.addSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('添加用户等级失败:', error);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(this.$t('acc.userLevel.deleteConfirm').replace('{level}', row.level), this.$t('dialog.confirm'), {
        confirmButtonText: this.$t('dialog.confirm'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: 'warning'
      }).then(() => {
        return delUserLevel(row.id);
      }).then(() => {
        this.getList();
        this.$message.success(this.$t('acc.userLevel.deleteSuccess'));
      }).catch(() => { });
    },
    /** 状态切换 */
    handleStatusChange(row, newStatus) {
      const text = newStatus === 0 ? this.$t('acc.userLevel.enabled') : this.$t('acc.userLevel.disabled');
      this.$confirm(this.$t('dialog.confirmStatus').replace('{action}', text), this.$t('dialog.confirm'), {
        confirmButtonText: this.$t('dialog.confirm'),
        cancelButtonText: this.$t('dialog.cancel'),
        type: 'warning'
      }).then(() => {
        const oldStatus = row.status;
        row.status = newStatus;
        return changeUserLevelStatus(row.id, newStatus);
      }).then(() => {
        const successText = newStatus === 0 ? this.$t('acc.userLevel.enableSuccess') : this.$t('acc.userLevel.disableSuccess');
        this.$message.success(successText);
        this.getList(); // 刷新列表
      }).catch(() => {
        // 恢复原状态
        row.status = row.status === 0 ? 1 : 0;
      });
    },
    /** 处理键盘事件 */
    handleKeyDown(e) {
      if (e.keyCode === 27 && this.open) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>
