<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
      <el-form-item :label="$t('acc.log.email')" prop="key">
        <el-input v-model="queryParams.key" :placeholder="$t('acc.log.emailInput')" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("base.carexception.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("base.carexception.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="exceptionList" :height="tableHeight()"
      @selection-change="handleSelectionChange">
      <el-table-column :label="$t('base.carexception.customerName')" type="index" width="65" prop="customerName"
        align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.riding.have.email')" prop="email" :show-overflow-tooltip="true">
        <span slot-scope="{ row }" v-NoData="row.email" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.log.registerStatus')" prop="register"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.register">
            {{ $t("acc.log.registed") }}
          </el-tag>
          <el-tag type="danger" v-else>{{ $t("acc.log.noRegisted") }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('base.carexception.createTime')" align="center" prop="loseTime" sortable>
        <span slot-scope="{row}" v-NoData="parseTime(row.loseTime)" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.riding.have.operation')" prop="code"
        :show-overflow-tooltip="true">
        <template slot-scope="{ row }">
          <el-button :type="row.status ? 'primary' : 'danger'" size="mini" @click="!row.status ? onUserLose(row) : ''">
            {{
              row.status ? $t("acc.log.processed") : $t("acc.log.notProcessed")
            }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { userLose, userLoseSearch } from "@/api/acc/user";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "RegisterError",
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      exceptionList: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      userLoseSearch(this.queryParams).then(res => {
        const { list, total } = res.data;
        this.exceptionList = list;
        this.total = total;
        this.loading = false;
      });
    },
    onUserLose({ email, status }) {
      userLose({ email, status }).then(() => {
        this.getList();
      });
    }
  }
};
</script>
