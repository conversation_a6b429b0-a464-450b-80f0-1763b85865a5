<template>
  <div class="app-container">
    <!-- 使用全局CSS类实现toolbar布局 -->
    <div class="search-toolbar">
      <!-- 搜索表单区域 -->
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
          <el-form-item :label="$t('acc.log.email')" prop="email">
            <el-input v-model="queryParams.email" :placeholder="$t('acc.log.emailInput')" clearable
              style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('acc.user.phone')" prop="phone">
            <el-input v-model="queryParams.phone" :placeholder="$t('acc.user.phoneInput')" clearable
              style="width: 200px" />
          </el-form-item>
          <el-form-item :label="$t('acc.log.nickName')" prop="nikeName">
            <el-input v-model="queryParams.nikeName" :placeholder="$t('acc.log.nickNameInput')" clearable
              style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
              {{ $t("base.carexception.search") }}
            </el-button>
            <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
              {{ $t("base.carexception.reset") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          {{ $t("base.carexception.newAdd") }}
        </el-button>
        <el-button type="success" icon="el-icon-download" @click="handleExport">
          {{ $t("base.carexception.export") }}
        </el-button>
      </div>
    </div>

    <el-table v-loading="loading" :data="exceptionList" :height="tableHeight(10)"
      @selection-change="handleSelectionChange" border>
      <el-table-column :label="$t('base.carexception.customerName')" type="index" width="65" prop="customerName"
        align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.log.email')" :show-overflow-tooltip="true">
        <span slot-scope="scope" v-NoData="scope.row.email" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.user.phone')" prop="phone">
        <span slot-scope="scope" v-NoData="scope.row.phone" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.log.nickName')" prop="nickName">
        <span slot-scope="scope" v-NoData="scope.row.nickName" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.log.logoutStatus')" prop="logoutStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status !== 1 ? 'success' : 'info'">
            {{ scope.row.status !== 1 ? $t('acc.log.loggedOut') : $t('acc.log.notLoggedOut') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('bike.bike.registerTime')" align="center" prop="logoutTime" sortable>
        <span slot-scope="scope" v-NoData="parseTime(scope.row.logoutTime)" />
      </el-table-column>
      <el-table-column align="center" :label="$t('acc.riding.have.operation')" prop="code" width="120">
        <template slot-scope="{ row }">
          <el-button v-if="row.status === 1" type="text" @click="onUserLogout(row)">
            {{ $t("acc.log.confirmLogout") }}
          </el-button>
          <span v-else class="text-muted">
            --
          </span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { userSearch, userLogout } from "@/api/acc/user";
import { commonJs } from "@/mixinFile/common";

export default {
  name: "BikeBike",
  mixins: [commonJs],
  data() {
    return {
      // 用户表格数据
      exceptionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        email: "",
        phone: "",
        nikeName: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询品牌列表 */
    getList() {
      this.loading = true;
      userSearch(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.exceptionList = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 用户注销
    onUserLogout({ id, nickName }) {
      this.$confirm(
        this.$t("base.carexception.sure") +
        '"' +
        this.$t("acc.log.resetLogout") +
        '""' +
        nickName +
        '"' +
        this.$t("base.carexception.what_"),
        this.$t("base.carexception.warn"),
        {
          confirmButtonText: this.$t("base.carexception.confirm"),
          cancelButtonText: this.$t("base.carexception.cancel"),
          type: this.$t("base.carexception.warning")
        }
      )
        .then(() => {
          userLogout({ userId: id }).then(() => {
            this.getList();
          });
        })
        .catch(err => { });
    }
  }
};
</script>
