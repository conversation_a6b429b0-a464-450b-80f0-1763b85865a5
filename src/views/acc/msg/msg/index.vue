<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.msg.msg.messageType')" prop="type">
        <el-select v-model="queryParams.type" :placeholder="$t('form.select') + $t('acc.msg.msg.messageType')"
          clearable>
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
          {{ $t("acc.msg.msg.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" v-debounce-click="resetQuery">
          {{ $t("acc.msg.msg.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd" hasPerminone="['bike:customer:add']">
          {{ $t("acc.msg.msg.newAdd") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" v-debounce-click="() => handleAuth(0, aFn, getList)">
          {{ $t("acc.msg.msg.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" v-debounce-click="() => handleAuth(1, aFn, getList)">
          {{ $t("acc.msg.msg.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table ref="multipleTableRef" v-loading="loading" row-key="id" :data="userList" :height="tableHeight()"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" reserve-selection align="center" />
      <el-table-column :label="$t('acc.msg.msg.serialNumber')" type="index" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.msg.msg.messageType')" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.msgType == 0">
            {{ $t("acc.msg.msg.system") }}
          </el-tag>
          <el-tag v-if="scope.row.msgType == 1" type="success">
            {{ $t("acc.msg.msg.push") }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.msg.msg.how_much_read')" prop="readNum" align="center"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('acc.msg.msg.title')" prop="title" align="center" />
      <el-table-column :label="$t('acc.msg.msg.messageBody')" prop="msg" align="center" />
      <el-table-column :label="$t('acc.msg.msg.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row, aFn, getList)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.msg.msg.operation')" align="center" class-name="small-padding fixed-width"
        width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="handleLook(scope.row)">
            {{ $t("base.fqa.lookOver") }}
          </el-button>
          <el-button type="text" @click="handleUpdate(scope.row)">
            {{ $t("acc.msg.msg.update") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <!-- 文本回显 -->
    <el-show-text :isShow.sync="drawer" :title="textContent.title">
      <div class="box-scrollbar" v-if="textContent.desc">
        <span v-html="textContent.desc" class="scroll-cont"></span>
      </div>
    </el-show-text>

    <el-dialog v-bind="dialogOption" :view.sync="dialogOption.view" :visible.sync="dialogOption.show"
      @close="closeDynamicDialog" :close-on-click-modal="false" append-to-body class="el-dialog-dynamic" center>
      <component :is="dialogOption.view" :rowData="currentRow" :bool="bool" @refresh="getList"
        @close="closeDynamicDialog">
      </component>
    </el-dialog>
  </div>
</template>

<script>
import { listMsg, authUser } from "@/api/acc/msg";
import PushConfigDialog from "./components/push-config-dialog";
import { commonJs } from "@/mixinFile/common";

export default {
  mixins: [commonJs],
  components: {
    PushConfigDialog
  },
  data() {
    return {
      aFn: authUser,
      textContent: {},
      drawer: false,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      bool: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("acc.msg.msg.system")
        },
        {
          dictValue: 1,
          dictLabel: this.$t("acc.msg.msg.push")
        }
      ],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: undefined
      },
      /**
       * 当前点击的row
       */
      currentRow: {},
      dialogOption: {
        width: "",
        title: "",
        show: false,
        view: ""
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listMsg(this.queryParams)
        .then(response => {
          this.userList = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /**
     * 新增
     */
    handleAdd() {
      this.currentRow = {};
      this.showDynamicDialog(
        "PushConfigDialog",
        this.$t("acc.msg.msg.addMessage"),
        "1100px",
        false
      );
    },
    handleUpdate(row) {
      this.currentRow = row;
      this.showDynamicDialog(
        "PushConfigDialog",
        this.$t("acc.msg.msg.updateMessage"),
        "1100px",
        true
      );
    },
    handleLook(row) {
      this.drawer = true;
      this.textContent = row;
    },
    showDynamicDialog(view, title, width = "1200px", bool) {
      this.dialogOption.show = true;
      this.dialogOption.view = view;
      this.dialogOption.title = title;
      this.dialogOption.width = width;
      this.bool = bool;
    },
    closeDynamicDialog() {
      this.dialogOption.show = false;
      this.dialogOption.view = null;
    }
  }
};
</script>
