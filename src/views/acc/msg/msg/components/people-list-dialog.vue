<template>
  <div>
    <div class="el-dialog-body">
      <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent>
        <el-form-item :label="$t('acc.user.nameOremail') + '/' + $t('acc.user.phone')" prop="key">
          <el-input v-model.trim="queryParams.key" :placeholder="$t('form.input') +
            $t('acc.user.nameOremail') +
            '/' +
            $t('acc.user.phone')
            " clearable style="width: 190px" @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" v-debounce-click="handleQuery">
            {{ $t("acc.user.search") }}
          </el-button>
        </el-form-item>
      </el-form>

      <el-table ref="sampleTableRef" v-loading="loading" row-key="id" :data="userList" max-height="400"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" reserve-selection align="center" />
        <el-table-column :label="$t('discover.posted.headName')" align="center">
          <template slot-scope="scope">
            <el-avatar shape="square" fit="contain" :src="scope.row.backImg" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('acc.user.nickName')" prop="nickName" align="center" />
        <el-table-column :label="$t('acc.user.country')" prop="country" align="center" />
        <el-table-column :label="$t('acc.user.email') + '/' + $t('acc.user.phone')" prop="email" align="center">
          <template slot-scope="scope">
            {{ scope.row.email || scope.row.phone }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l" :pager-count="5"
      hide-on-single-page @pagination="getList" />

    <div class="text-center margin-bottom-sm margin-top-sm">
      <el-button type="primary" :disabled="!selectionData.length" @click="save">
        {{ $t("acc.msg.msg._confirm") }}
      </el-button>
      <el-button @click="$emit('close')">
        {{ $t("acc.msg.msg._cancel") }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { listUser } from "@/api/acc/user";
export default {
  props: {
    pushUserList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      selectionData: [],
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        key: undefined
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams)
        .then(response => {
          this.userList = response.data.list;
          this.total = response.data.total;

          this.replayUserIds();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 选择用户回显  */
    replayUserIds() {
      if (this.pushUserList.length > 0) {
        this.$nextTick(() => {
          this.pushUserList.forEach(item => {
            this.userList.forEach(cItem => {
              if (item.id === cItem.id) {
                this.$refs.sampleTableRef.toggleRowSelection(cItem, true);
              }
            })
          })
        });
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectionData = selection;
    },
    /**
     * 确定
     */
    save() {
      this.$emit("selectUser", this.selectionData);
      this.$emit("close");
    }
  }
};
</script>
