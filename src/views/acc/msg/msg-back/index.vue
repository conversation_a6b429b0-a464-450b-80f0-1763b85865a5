<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.msg.msg.messageType')" prop="type">
        <el-select v-model="queryParams.type" clearable size="small" style="width: 190px">
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t("acc.msg.msg.search")
          }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          {{ $t("acc.msg.msg.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          hasPerminone="['bike:customer:add']">{{ $t("acc.msg.msg.newAdd") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" size="mini" @click="handleAuth(0)"
          hasPerminone="['acc:user:auth']">{{ $t("acc.msg.msg.startUsing") }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleAuth(1)"
          hasPerminone="['acc:user:auth']">{{ $t("acc.msg.msg.forbidden") }}</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column type="index" width="65" align="center" :label="$t('acc.msg.msg.serialNumber')">
        <template slot-scope="scope">
          <span>{{
            (queryParams.p - 1) * queryParams.l + scope.$index + 1
            }}</span>
        </template></el-table-column>

      <el-table-column :label="$t('acc.msg.msg.how_much_read')" prop="readNum" :show-overflow-tooltip="true"
        align="center" />
      <el-table-column :label="$t('acc.msg.msg.describe')" prop="desc" :show-overflow-tooltip="true" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleLook(scope.row)">{{
            $t("base.fqa.lookOver")
            }}</el-button>
        </template>
      </el-table-column>

      <el-table-column :label="$t('acc.msg.msg.messageBody')" prop="msg" align="center" />
      <el-table-column :label="$t('acc.msg.msg.messageType')" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1">{{
            $t("acc.msg.msg.system")
            }}</el-tag>
          <el-tag v-if="scope.row.type == 2" type="success">{{
            $t("acc.msg.msg.oneMan")
            }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.msg.msg.activatedState')" align="center">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.msg.msg.operation')" align="center" class-name="small-padding fixed-width"
        width="100">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)">{{
            $t("acc.msg.msg.update")
            }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="950px" append-to-body>
      <el-form ref="form" label-position="top" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="7">
            <el-form-item :label="$t('acc.msg.msg.title')" prop="title">
              <el-input v-model="form.title" :placeholder="$t('acc.msg.msg.pleaseInput') + $t('acc.msg.msg.title')
                " />
            </el-form-item>

            <el-form-item :label="$t('acc.msg.msg.messageType')" prop="type">
              <el-select v-model="form.type" clearable style="width: 100%">
                <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('acc.msg.msg.whetherToPush')" prop="push">
              <el-radio-group v-model="form.push">
                <el-radio :label="1">{{ $t("acc.msg.msg.yes") }}</el-radio>
                <el-radio :label="0">{{ $t("acc.msg.msg.no") }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('acc.msg.msg.messageBody')" prop="msg">
              <el-input v-model="form.msg" type="textarea" :autosize="{ minRows: 12 }" :placeholder="$t('acc.msg.msg.pleaseInput') + $t('acc.msg.msg.messageBody')
                " />
            </el-form-item>
          </el-col>
          <el-col :span="17">
            <el-form-item :label="$t('acc.msg.msg.describe')" prop="desc">
              <tinymce v-if="open" height="367" v-model="form.desc" :placeholder="$t('acc.msg.msg.pleaseInput') + $t('acc.msg.msg.describe')
                "></tinymce>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("acc.msg.msg._confirm")
          }}</el-button>
        <el-button @click="cancel">{{ $t("acc.msg.msg._cancel") }}</el-button>
      </div>
    </el-dialog>

    <!-- 文本回显 -->
    <el-dialog :title="textContent.title" :visible.sync="drawer" size="40%" :close-on-click-modal="false">
      <el-card>
        <div v-html="textContent.desc" class="scroll-cont"></div>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import { listMsg, addMsg, editMsg } from "@/api/acc/msg";
import tinymce from "@/components/Editor";
export default {
  components: {
    tinymce
  },
  data() {
    return {
      textContent: "",
      drawer: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 1,
          dictLabel: this.$t("acc.msg.msg.messageSystem")
        },
        {
          dictValue: 2,
          dictLabel: this.$t("acc.msg.msg.oneMan")
        }
      ],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        type: undefined
      },
      // 表单参数
      form: {
        desc: null,
        msg: "",
        title: "",
        push: 1
      },
      rules: {
        type: [
          {
            required: true,
            message: this.$t("acc.msg.msg.messageTypeNotBeNull"),
            trigger: "blur"
          }
        ],
        desc: [
          {
            required: true,
            message: this.$t("acc.msg.msg.describeNotBeNull"),
            trigger: "blur"
          }
        ],
        msg: [
          {
            required: true,
            message: this.$t("acc.msg.msg.messageBodyNotBeNull"),
            trigger: "blur"
          }
        ],
        title: [
          {
            required: true,
            message: this.$t("acc.msg.msg.titleNotBeNull"),
            trigger: "blur"
          }
        ],
        push: [
          {
            required: true,
            message: this.$t("acc.msg.msg.pleaseChoosewhetherToPush"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listMsg(this.queryParams).then(response => {
        this.userList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 0
          ? this.$t("acc.msg.msg.startUsing")
          : this.$t("acc.msg.msg.blockUp");
      this.$confirm(
        this.$t("acc.msg.msg.sure") + text + this.$t("acc.msg.msg.theUser"),
        this.$t("acc.msg.msg.warn"),
        {
          confirmButtonText: this.$t("acc.msg.msg.confirm"),
          cancelButtonText: this.$t("acc.msg.msg.cancel"),
          type: this.$t("acc.msg.msg.warning")
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          let userData = { id: row.id, status: row.status };
          data.push(userData);

          authUser(data).then(response => {
            this.msgSuccess(text + this.$t("acc.msg.msg.succeed"));
            this.loading = false;
          });
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      var date = new Date();

      var year = date.getFullYear();

      var month = date.getMonth() + 1;
      var day = date.getDate();
      var hour = date.getHours();
      var minute = date.getMinutes();
      var second = date.getSeconds();
      this.form = {
        desc: "",
        status: 0,
        msg: "",
        title: "",
        type: ""
      };

      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("acc.msg.msg.addMessage");
    },
    handleUpdate(row) {
      // this.reset();
      this.form = row;
      this.open = true;
      this.title = this.$t("acc.msg.msg.updateMessage");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
    },
    handleAuth(status) {
      let text =
        status === 0
          ? this.$t("acc.msg.msg.startUsing")
          : this.$t("acc.msg.msg.blockUp");
      this.$confirm(
        this.$t("acc.msg.msg.sure") + '"' + +this.$t("acc.msg.msg.what_"),
        $t("acc.msg.msg.warn"),
        {
          confirmButtonText: this.$t("acc.msg.msg.confirm"),
          cancelButtonText: this.$t("acc.msg.msg.cancel"),
          type: this.$t("acc.msg.msg.warning")
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let userData = { id: this.ids[i], status: status };
            data.push(userData);
          }
          this.loading = false;
          authUser(data).then(response => {
            this.msgSuccess(text + this.$t("acc.msg.msg.succeed"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(function () {
          status = status === "0" ? "1" : "0";
        });
    },
    handleLook(row) {
      this.drawer = true;
      this.textContent = row;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            editMsg(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("acc.msg.msg.updateSucceed"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addMsg(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("acc.msg.msg.addSucceed"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    }
  }
};
</script>
