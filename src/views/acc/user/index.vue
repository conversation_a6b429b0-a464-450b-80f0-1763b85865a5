<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.user.activatedState')" prop="status">
        <el-select v-model="queryParams.status" clearable @keyup.enter="handleQuery">
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acc.user.nameOremail') + '/' + $t('acc.user.phone')" prop="key">
        <el-input v-model="queryParams.key" :placeholder="$t('form.input')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('acc.user.sex')" prop="gender">
        <el-select v-model="queryParams.gender" clearable>
          <el-option v-for="dict in genderOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acc.user.lastActivityTime')">
        <el-date-picker v-model="dateRange" style="width: 190px" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          range-separator="-" :start-placeholder="$t('acc.user.beginDate')"
          :end-placeholder="$t('acc.user.endDate')"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">
          {{ $t("acc.user.search") }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">
          {{ $t("acc.user.reset") }}
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-check" @click="handleAuth(0)" hasPerminone="['acc:user:auth']">
          {{ $t("acc.user.startUsing") }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" @click="handleAuth(1)" hasPerminone="['acc:user:auth']">
          {{ $t("acc.user.forbidden") }}
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="65" align="center" />
      <el-table-column type="index" width="65" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          {{ (queryParams.p - 1) * queryParams.l + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('discover.posted.headName')" align="center" width="80">
        <template slot-scope="scope">
          <el-avatar shape="square" :src="scope.row.backImg"></el-avatar>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.nickName')" prop="nickName" align="center" />
      <el-table-column :label="$t('acc.user.country')" prop="country" align="center">
        <span slot-scope="scope" v-NoData="scope.row.country"></span>
      </el-table-column>
      <el-table-column :label="$t('acc.user.email') + '/' + $t('acc.user.phone')" prop="email" align="center">
        <template slot-scope="scope">
          {{ scope.row.email ? scope.row.email : scope.row.phone }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.sex')" align="center" width="100px">
        <template slot-scope="scope">
          {{
            scope.row.gender == 1
              ? $t("acc.user.man")
              : scope.row.gender == 2
                ? $t("acc.user.woman")
                : $t("acc.user.unknown")
          }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.city')" align="center" prop="city" />
      <el-table-column :label="$t('acc.user.activatedState')" align="center" width="120">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>

      <el-table-column :label="$t('acc.user.lastActivityTime')" align="center" prop="lastActiveTime" sortable>
        <template slot-scope="scope">
          {{ parseTime(scope.row.lastActiveTime) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.user.operation')" align="center" class-name="small-padding fixed-width"
        width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.row)" hasPerminone="['acc:user:query']">
            {{ $t("acc.user.particulars") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />

    <CompDetail ref="compDetail" />
  </div>
</template>

<script>
import { listUser, authUser } from "@/api/acc/user";
import CompDetail from "./detail/index";
import { commonJs } from "@/mixinFile/common";
export default {
  mixins: [commonJs],
  components: {
    CompDetail
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        status: undefined,
        key: undefined,
        gender: undefined // 1男 2女 3保密
      },
      // 表单参数
      form: {},
      userDetail: {
        user: {},
        ridingData: {},
        bikeList: []
      }
    };
  },
  created() {
    if (this.$route.query.nickName) {
      this.queryParams.key = this.$route.query.nickName;
    }
    this.getList();
    this.getDicts("sys_normal_disable").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then(response => {
      this.genderOptions = response.data;
    });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        response => {
          this.userList = response.data.list;
          this.total = response.data.total;
          this.loading = false;
        }
      );
    },

    // 用户状态修改
    handleStatusChange(row) {
      let text =
        row.status === 0
          ? this.$t("acc.user.startUsing")
          : this.$t("acc.user.blockUp");
      this.$confirm(this.$t("acc.user.sure"), this.$t("acc.user.warn"), {
        confirmButtonText: this.$t("acc.user.confirm"),
        cancelButtonText: this.$t("acc.user.cancel"),
        type: this.$t("acc.user.warning")
      })
        .then(() => {
          this.loading = true;
          let data = [];
          let userData = {
            id: row.id,
            status: row.status
          };
          data.push(userData);

          authUser(data).then(response => {
            this.msgSuccess(text + this.$t("acc.user.succeed"));
            this.loading = false;
          });
        })
        .catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        p: 1,
        l: 20,
        status: undefined,
        key: undefined,
        gender: undefined // 1男 2女 3保密
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    handleAuth(status) {
      if (this.tabSelection(this.ids)) {
        if (status === 0)
          return this.warningMessage(`${this.$t("form.enableData")}`, 1);
        else return this.warningMessage(`${this.$t("form.disabledData")}`, 3);
      }
      let text =
        status === 0
          ? this.$t("acc.user.startUsing")
          : this.$t("acc.user.blockUp");
      this.$confirm(
        this.$t("acc.user.sure") + '"' + text + this.$t("acc.user.what_"),
        this.$t("acc.user.warn"),
        {
          confirmButtonText: this.$t("acc.user.confirm"),
          cancelButtonText: this.$t("acc.user.cancel"),
          type: this.$t("acc.user.warning")
        }
      )
        .then(() => {
          this.loading = true;
          let data = [];
          for (let i = 0; i < this.ids.length; i++) {
            let userData = {
              id: this.ids[i],
              status: status
            };
            data.push(userData);
          }
          this.loading = false;
          authUser(data).then(response => {
            this.msgSuccess(text + this.$t("acc.user.succeed"));
            this.loading = false;
            this.getList();
          });
        })
        .catch(function () {
          status = status === "0" ? "1" : "0";
        });
    },
    handleDetail(row) {
      this.$refs["compDetail"].dialogVisble = true;
      this.$refs["compDetail"].getList(row.id);
    }
  }
};
</script>
