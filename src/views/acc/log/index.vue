<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.log.nickName')" prop="nickName">
        <el-input v-model="queryParams.nickName" :placeholder="$t('acc.log.nickNameInput')" clearable size="small"
          style="width: 190px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('acc.log.email')" prop="email">
        <el-input v-model="queryParams.email" :placeholder="$t('acc.log.emailInput')" clearable size="small"
          style="width: 190px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t("queryParams.search")
          }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("queryParams.reset")
          }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8"></el-row>

    <el-table v-loading="loading" :data="apiLogList" @selection-change="handleSelectionChange" border>
      <el-table-column type="index" width="65" :label="$t('acc.msg.msg.serialNumber')" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.p - 1) * queryParams.l + scope.$index + 1
            }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acc.log.nickName')" align="center" prop="nickName" />
      <el-table-column :label="$t('acc.log.email')" align="center" prop="email" />
      <el-table-column :label="$t('acc.log.url')" prop="url" align="center" />
      <el-table-column :label="$t('acc.log.method')" align="center" prop="method" width="100" />
      <el-table-column :label="$t('acc.log.param')" align="center" prop="param" />
      <el-table-column :label="$t('acc.log.result')" align="center" show-overflow-tooltip prop="result" />
      <el-table-column :label="$t('acc.log.ip')" prop="ip" align="center" width="140" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.p" :limit.sync="queryParams.l"
      @pagination="getList" />
  </div>
</template>

<script>
import { listApiLog } from "@/api/acc/log";

export default {
  name: "AccApiLog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      apiLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 日期范围
      dateRange: [],
      genderOptions: [],
      // 查询参数
      queryParams: {
        p: 1,
        l: 20,
        email: undefined,
        nickName: undefined,
      },
      // 表单参数
      form: {},
    };
  },
  created() { },
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listApiLog(this.queryParams).then((response) => {
        this.apiLogList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        id: undefined,
        p: 1,
        l: 20,
        email: undefined,
        nickName: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.p = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
  },
};
</script>
