@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./btn.scss";
@import "./toolbar.scss";

.font14 {
  font-size: 14px;
}

.font18 {
  font-size: 18px;
}

.font20 {
  font-size: 20px;
}

.font28 {
  font-size: 28px;
}

.text-dot {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.width-all {
  width: 100%;

  input,
  textarea,
  .el-form-item__content {
    // width: 100%;
  }
}

/*
布局
==================== */

/*  -- flex弹性布局 -- */

/*  -- flex弹性布局 -- */
.iflex {
  display: inline-flex;
}

.flex {
  display: flex;
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.flex-sub {
  flex: 1;
}

.flex-twice {
  flex: 2;
}

.flex-treble {
  flex: 3;
}

.flex-direction {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: flex-center;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/*
布局
==================== */

.text-red,
.line-red,
.lines-red {
  color: #e54d42 !important;
}

.text-orange,
.line-orange,
.lines-orange {
  color: #f37b1d !important;
}

.text-yellow,
.line-yellow,
.lines-yellow {
  color: #fbbd08 !important;
}

.text-olive,
.line-olive,
.lines-olive {
  color: #8dc63f !important;
}

.text-green,
.line-green,
.lines-green {
  color: #39b54a !important;
}

.text-cyan,
.line-cyan,
.lines-cyan {
  color: #1cbbb4 !important;
}

.text-blue,
.line-blue,
.lines-blue {
  color: #0081ff !important;
}

.text-purple,
.line-purple,
.lines-purple {
  color: #6739b6 !important;
}

.text-mauve,
.line-mauve,
.lines-mauve {
  color: #9c26b0 !important;
}

.text-pink,
.line-pink,
.lines-pink {
  color: #e03997 !important;
}

.text-brown,
.line-brown,
.lines-brown {
  color: #a5673f !important;
}

.text-grey,
.line-grey,
.lines-grey {
  color: #8799a3 !important;
}

.text-gray,
.line-gray,
.lines-gray {
  color: #aaaaaa !important;
}

.text-black,
.line-black,
.lines-black {
  color: #333333 !important;
}

.text-white,
.line-white,
.lines-white {
  color: #ffffff !important;
}

/*  -- 内外边距 -- */

.margin-0 {
  margin: 0;
}

.margin-xs {
  margin: 10px;
}

.margin-sm {
  margin: 20px;
}

.margin {
  margin: 30px;
}

.margin-lg {
  margin: 40px;
}

.margin-xl {
  margin: 50px;
}

.margin-top-xs {
  margin-top: 10px;
}

.margin-top-sm {
  margin-top: 20px;
}

.margin-top {
  margin-top: 30px;
}

.margin-top-lg {
  margin-top: 40px;
}

.margin-top-xl {
  margin-top: 50px;
}

.margin-right-xxs {
  margin-right: 5px;
}

.margin-right-xs {
  margin-right: 10px;
}

.margin-right-sm {
  margin-right: 20px;
}

.margin-right {
  margin-right: 30px;
}

.margin-right-lg {
  margin-right: 40px;
}

.margin-right-xl {
  margin-right: 50px;
}

.margin-bottom-xs {
  margin-bottom: 10px;
}

.margin-bottom-sm {
  margin-bottom: 20px;
}

.margin-bottom {
  margin-bottom: 30px;
}

.margin-bottom-lg {
  margin-bottom: 40px;
}

.margin-bottom-xl {
  margin-bottom: 50px;
}

.margin-left-xs {
  margin-left: 10px;
}

.margin-left-sm {
  margin-left: 20px;
}

.margin-left {
  margin-left: 30px;
}

.margin-left-lg {
  margin-left: 40px;
}

.margin-left-xl {
  margin-left: 50px;
}

.margin-lr-xs {
  margin-left: 10px;
  margin-right: 10px;
}

.margin-lr-sm {
  margin-left: 20px;
  margin-right: 20px;
}

.margin-lr {
  margin-left: 30px;
  margin-right: 30px;
}

.margin-lr-lg {
  margin-left: 40px;
  margin-right: 40px;
}

.margin-lr-xl {
  margin-left: 50px;
  margin-right: 50px;
}

.margin-tb-xs {
  margin-top: 10px;
  margin-bottom: 10px;
}

.margin-tb-sm {
  margin-top: 20px;
  margin-bottom: 20px;
}

.margin-tb {
  margin-top: 30px;
  margin-bottom: 30px;
}

.margin-tb-lg {
  margin-top: 40px;
  margin-bottom: 40px;
}

.margin-tb-xl {
  margin-top: 50px;
  margin-bottom: 50px;
}

.padding-0 {
  padding: 0;
}

.padding-xs {
  padding: 10px;
}

.padding-sm {
  padding: 20px;
}

.padding {
  padding: 30px;
}

.padding-lg {
  padding: 40px;
}

.padding-xl {
  padding: 50px;
}

.padding-top-xs {
  padding-top: 10px;
}

.padding-top-sm {
  padding-top: 20px;
}

.padding-top {
  padding-top: 30px;
}

.padding-top-lg {
  padding-top: 40px;
}

.padding-top-xl {
  padding-top: 50px;
}

.padding-right-xs {
  padding-right: 10px;
}

.padding-right-sm {
  padding-right: 20px;
}

.padding-right {
  padding-right: 30px;
}

.padding-right-lg {
  padding-right: 40px;
}

.padding-right-xl {
  padding-right: 50px;
}

.padding-bottom-xs {
  padding-bottom: 10px;
}

.padding-bottom-sm {
  padding-bottom: 20px;
}

.padding-bottom {
  padding-bottom: 30px;
}

.padding-bottom-lg {
  padding-bottom: 40px;
}

.padding-bottom-xl {
  padding-bottom: 50px;
}

.padding-left-xs {
  padding-left: 10px;
}

.padding-left-sm {
  padding-left: 20px;
}

.padding-left {
  padding-left: 30px;
}

.padding-left-lg {
  padding-left: 40px;
}

.padding-left-xl {
  padding-left: 50px;
}

.padding-lr-xs {
  padding-left: 10px;
  padding-right: 10px;
}

.padding-lr-sm {
  padding-left: 20px;
  padding-right: 20px;
}

.padding-lr {
  padding-left: 30px;
  padding-right: 30px;
}

.padding-lr-lg {
  padding-left: 40px;
  padding-right: 40px;
}

.padding-lr-xl {
  padding-left: 50px;
  padding-right: 50px;
}

.padding-tb-xs {
  padding-top: 10px;
  padding-bottom: 10px;
}

.padding-tb-sm {
  padding-top: 20px;
  padding-bottom: 20px;
}

.padding-tb {
  padding-top: 30px;
  padding-bottom: 30px;
}

.padding-tb-lg {
  padding-top: 40px;
  padding-bottom: 40px;
}

.padding-tb-xl {
  padding-top: 50px;
  padding-bottom: 50px;
}

.bg-red {
  background-color: #e54d42 !important;
  color: #ffffff;
}

.bg-orange {
  background-color: #f37b1d;
  color: #ffffff;
}

.bg-yellow {
  background-color: #fbbd08 !important;
  color: #333333;
}

.bg-olive {
  background-color: #8dc63f;
  color: #ffffff;
}

.bg-green {
  background-color: #39b54a !important;
  color: #ffffff;
}

.bg-cyan {
  background-color: #1cbbb4;
  color: #ffffff;
}

.bg-blue {
  background-color: #0081ff;
  color: #ffffff;
}

.bg-purple {
  background-color: #6739b6;
  color: #ffffff;
}

.bg-mauve {
  background-color: #9c26b0;
  color: #ffffff;
}

.bg-pink {
  background-color: #e03997;
  color: #ffffff;
}

.bg-brown {
  background-color: #a5673f;
  color: #ffffff;
}

.bg-grey {
  background-color: #8799a3;
  color: #ffffff;
}

.bg-gray {
  background-color: #f0f0f0;
  color: #333333;
}

.bg-black {
  background-color: #333333;
  color: #ffffff;
}

.bg-white {
  background-color: #ffffff !important;
  color: #666666 !important;
}

/* -- 实线 -- */

.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
  position: relative;
}

.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}

.solid::after {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-top::after {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-right::after {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-bottom::after {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.solid-left::after {
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.solids::after {
  border: 8px solid #dcdfe6;
}

.solids-top::after {
  border-top: 8px solid #dcdfe6;
}

.solids-right::after {
  border-right: 8px solid #dcdfe6;
}

.solids-bottom::after {
  border-bottom: 8px solid #dcdfe6;
}

.solids-left::after {
  border-left: 8px solid #dcdfe6;
}

/* -- 虚线 -- */

.dashed::after {
  border: 1px dashed #ddd;
}

.dashed-top::after {
  border-top: 1px dashed #ddd;
}

.dashed-right::after {
  border-right: 1px dashed #ddd;
}

.dashed-bottom::after {
  border-bottom: 1px dashed #ddd;
}

.dashed-left::after {
  border-left: 1px dashed #ddd;
}

.shadow[class*="-red"] {
  box-shadow: 6px 6px 8px rgba(204, 69, 59, 0.2);
}

.shadow[class*="-orange"] {
  box-shadow: 6px 6px 8px rgba(217, 109, 26, 0.2);
}

.shadow[class*="-yellow"] {
  box-shadow: 6px 6px 8px rgba(224, 170, 7, 0.2);
}

.shadow[class*="-olive"] {
  box-shadow: 6px 6px 8px rgba(124, 173, 55, 0.2);
}

.shadow[class*="-green"] {
  box-shadow: 6px 6px 8px rgba(48, 156, 63, 0.2);
}

.shadow[class*="-cyan"] {
  box-shadow: 6px 6px 8px rgba(28, 187, 180, 0.2);
}

.shadow[class*="-blue"] {
  box-shadow: 6px 6px 8px rgba(0, 102, 204, 0.2);
}

.shadow[class*="-purple"] {
  box-shadow: 6px 6px 8px rgba(88, 48, 156, 0.2);
}

.shadow[class*="-mauve"] {
  box-shadow: 6px 6px 8px rgba(133, 33, 150, 0.2);
}

.shadow[class*="-pink"] {
  box-shadow: 6px 6px 8px rgba(199, 50, 134, 0.2);
}

.shadow[class*="-brown"] {
  box-shadow: 6px 6px 8px rgba(140, 88, 53, 0.2);
}

.shadow[class*="-grey"] {
  box-shadow: 6px 6px 8px rgba(114, 130, 138, 0.2);
}

.shadow[class*="-gray"] {
  box-shadow: 6px 6px 8px rgba(114, 130, 138, 0.2);
}

.shadow[class*="-black"] {
  box-shadow: 6px 6px 8px rgba(26, 26, 26, 0.2);
}

.shadow[class*="-white"] {
  box-shadow: 6px 6px 8px rgba(26, 26, 26, 0.2);
}

.text-shadow[class*="-red"] {
  text-shadow: 6px 6px 8px rgba(204, 69, 59, 0.2);
}

.text-shadow[class*="-orange"] {
  text-shadow: 6px 6px 8px rgba(217, 109, 26, 0.2);
}

.text-shadow[class*="-yellow"] {
  text-shadow: 6px 6px 8px rgba(224, 170, 7, 0.2);
}

.text-shadow[class*="-olive"] {
  text-shadow: 6px 6px 8px rgba(124, 173, 55, 0.2);
}

.text-shadow[class*="-green"] {
  text-shadow: 6px 6px 8px rgba(48, 156, 63, 0.2);
}

.text-shadow[class*="-cyan"] {
  text-shadow: 6px 6px 8px rgba(28, 187, 180, 0.2);
}

.text-shadow[class*="-blue"] {
  text-shadow: 6px 6px 8px rgba(0, 102, 204, 0.2);
}

.text-shadow[class*="-purple"] {
  text-shadow: 6px 6px 8px rgba(88, 48, 156, 0.2);
}

.text-shadow[class*="-mauve"] {
  text-shadow: 6px 6px 8px rgba(133, 33, 150, 0.2);
}

.text-shadow[class*="-pink"] {
  text-shadow: 6px 6px 8px rgba(199, 50, 134, 0.2);
}

.text-shadow[class*="-brown"] {
  text-shadow: 6px 6px 8px rgba(140, 88, 53, 0.2);
}

.text-shadow[class*="-grey"] {
  text-shadow: 6px 6px 8px rgba(114, 130, 138, 0.2);
}

.text-shadow[class*="-gray"] {
  text-shadow: 6px 6px 8px rgba(114, 130, 138, 0.2);
}

.text-shadow[class*="-black"] {
  text-shadow: 6px 6px 8px rgba(26, 26, 26, 0.2);
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: pingfang, Helvetica Neue, Helvetica, PingFang SC,
    Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  background: #f0f2f5;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.form-content-detail {
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;

  .el-form-item {
    margin-bottom: 0;
    padding: 10px 0;
    flex-basis: 50%;
    margin-right: 0;
  }
}

.form-data {
  .el-form-item {
    display: inline-flex;
    width: 49%;
    margin-right: 0;

    .el-input,
    .el-input--medium {
      width: 240px;
    }
  }
}

.hide .el-upload--picture-card {
  display: none;
}

.box-shadow {
  @include boxShadow();
}

// fieldset 的样式
.fieldset-box {
  border: 3px solid #a4bef2;
  border-radius: 6px;
  padding: 20px;

  legend {
    color: #303133;
    font-size: 16px;
    font-weight: bold;
  }
}

// 全局表格居中样式
.el-table {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        text-align: center !important;

        .cell {
          text-align: center !important;
        }
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      td {
        text-align: center !important;

        .cell {
          text-align: center !important;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          gap: 4px;

          // 操作按钮样式优化
          .el-button--text {
            margin: 0;
            padding: 4px 8px;
            font-size: 12px;
          }

          // 用户信息等复合内容保持灵活布局
          .user-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
          }

          // 统计信息样式
          .stats-info {
            text-align: center;
          }
        }
      }
    }
  }
}
