// cover some element-ui styles

.el-image-viewer__close {
  color: #fff;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-form--label-top {
  .el-form-item {
    margin-bottom: 10px;
    .el-form-item__label {
      padding: 0;
    }
  }
}
.el-dialog__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.el-dialog {
  margin-top: 0 !important;
  .el-dialog__body {
    max-height: 77vh;
    overflow-y: auto;
  }
}
// 动态弹窗样式
.el-dialog-dynamic {
  .el-dialog__body {
    padding: 0 !important;
    max-height: none !important;
    overflow-y: hidden !important;
    max-height: 65vh;
    overflow-y: auto;
  }
  .el-dialog-body {
    padding: 12px 16px;
    max-height: 60vh;
    overflow-y: auto;
  }
  .el-dialog-body_form {
    padding: 12px 16px 1px 16px;
    max-height: 60vh;
    overflow-y: auto;
  }
  .pagination-container {
    padding: 0 !important;
    .el-pagination {
      padding: 2px 16px;
    }
  }
}
