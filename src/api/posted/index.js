import request from '@/utils/request'

// 查询帖子列表
export function listPosted(query) {
    return request({
        url: '/posted/list',
        method: 'get',
        params: query
    })
}

// 修改帖子状态
export function authPosted(data) {
    return request({
        url: '/posted/auth',
        method: 'put',
        data: data
    })
}

// 查询评价列表
export function listPostedRemark(query) {
    return request({
        url: '/posted/remark/list',
        method: 'get',
        params: query
    })
}

// 修改评价状态
export function authPostedRemark(data) {
    return request({
        url: '/posted/remark/auth',
        method: 'put',
        data: data
    })
}



// 帖子话题类型

export function topicTypeList(params) {
    return request({
        url: '/type/topic/list',
        method: 'get',
        params
    })
}

// 新增帖子话题类型
export function topicTypeAdd(data) {
    return request({
        url: '/type/topic/save',
        method: 'post',
        data
    })
}

// 修改帖子话题类型
export function topicTypeEdit(data) {
    return request({
        url: '/type/topic/update',
        method: 'put',
        data
    })
}

// 启用、禁用帖子话题类型
export function topicTypeAuth(data) {
    return request({
        url: '/type/topic/auth',
        method: 'put',
        data
    })
}



// 话题管理

export function topicList(params) {
    return request({
        url: '/topic/list',
        method: 'get',
        params
    })
}

// 新增话题
export function topicAdd(data) {
    return request({
        url: '/topic/add',
        method: 'post',
        data
    })
}

// 修改话题
export function topicEdit(data) {
    return request({
        url: '/topic/edit',
        method: 'put',
        data
    })
}

// 删除话题
export function topicDelete(delId) {
    return request({
        url: '/topic/delete/' + delId,
        method: 'delete',
    })
}

// 启用、禁用话题
export function topicAuth(data) {
    return request({
        url: '/type/topic/auth',
        method: 'put',
        data
    })
}

// 帖子置顶功能
export function postedTopAuth(data) {
    return request({
        url: '/posted/top/auth',
        method: 'put',
        data: data
    })
}

// 删除帖子
export function deletePosted(id) {
    return request({
        url: '/posted/' + id,
        method: 'delete'
    })
}