import request from '@/utils/request'

// 查询用户等级列表
export function listUserLevels(query) {
  return request({
    url: '/user/level/list',
    method: 'get',
    params: {
      levelName: query.levelName,
      p: query.pageNum,
      l: query.pageSize,
      ...query
    }
  })
}

// 查询用户等级详细 (如果后端没有此接口，可以从列表中获取)
export function getUserLevel(id) {
  return request({
    url: '/user/level/' + id,
    method: 'get'
  })
}

// 新增用户等级
export function addUserLevel(data) {
  return request({
    url: '/user/level/add',
    method: 'post',
    data: data
  })
}

// 修改用户等级
export function updateUserLevel(data) {
  return request({
    url: '/user/level/edit',
    method: 'put',
    data: data
  })
}

// 删除用户等级
export function delUserLevel(id) {
  return request({
    url: '/user/level/delete/' + id,
    method: 'put'
  })
}

// 启用/禁用用户等级
export function changeUserLevelStatus(id, status) {
  return request({
    url: '/user/level/state',
    method: 'put',
    data: {
      id: id,
      status: status
    }
  })
}

// 查询用户等级区间列表
export function listUserLevelIntervals(query) {
  return request({
    url: '/user/level/interval/list',
    method: 'get',
    params: {
      levelName: query.levelName,
      p: query.pageNum,
      l: query.pageSize
    }
  })
}

// 新增用户等级区间
export function addUserLevelInterval(data) {
  return request({
    url: '/user/level/interval/add',
    method: 'post',
    data: data
  })
}

// 修改用户等级区间
export function updateUserLevelInterval(data) {
  return request({
    url: '/user/level/interval/edit',
    method: 'post',
    data: data
  })
}

// 删除用户等级区间
export function delUserLevelInterval(id) {
  return request({
    url: '/user/level/interval/delete/' + id,
    method: 'post'
  })
}

// 为等级区间设置任务
export function setLevelTasks(data) {
  return request({
    url: '/user/level/add/task',
    method: 'post',
    data: data
  })
}

// 修改等级区间任务
export function updateLevelTasks(data) {
  return request({
    url: '/user/level/edit/task',
    method: 'post',
    data: data
  })
}

// 获取等级区间已设置的任务
export function getLevelTasks(levelId) {
  return request({
    url: '/user/level/task/' + levelId,
    method: 'get'
  })
}
