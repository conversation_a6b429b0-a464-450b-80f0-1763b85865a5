import request from '@/utils/request'

// 获取任务列表
export function getTaskList(query) {
  return request({
    url: '/task/list',
    method: 'get',
    params: query
  })
}

// 创建任务
export function addTask(data) {
  return request({
    url: '/task/add',
    method: 'post',
    data: data
  })
}

// 修改任务
export function editTask(data) {
  return request({
    url: '/task/edit',
    method: 'post',
    data: data
  })
}

// 启用禁用任务
export function updateTaskStatus(data) {
  return request({
    url: '/task/auth',
    method: 'post',
    data: data
  })
}