import request from '@/utils/request'

// 签到管理相关API

/**
 * 查询基础天数规则
 */
export function getBaseInfo() {
  return request({
    url: '/check/base/info',
    method: 'get'
  })
}

/**
 * 修改基础天数规则
 * @param {Object} data - 基础规则数据
 */
export function editBase(data) {
  return request({
    url: '/check/edit/base',
    method: 'post',
    data
  })
}

/**
 * 查询特殊天数规则列表
 * @param {Object} query - 查询参数
 */
export function getBonusList(query) {
  return request({
    url: '/check/bonus/list',
    method: 'get',
    params: {
      p: query.page || 1,
      l: query.limit || 10
    }
  })
}

/**
 * 创建特殊天数规则
 * @param {Object} data - 特殊规则数据
 */
export function addBonus(data) {
  return request({
    url: '/check/add/bonus',
    method: 'post',
    data
  })
}

/**
 * 修改特殊天数规则
 * @param {Object} data - 特殊规则数据
 */
export function editBonus(data) {
  return request({
    url: '/check/edit/bonus',
    method: 'post',
    data
  })
}

/**
 * 删除特殊天数规则
 * @param {Array} ids - 要删除的ID数组
 */
export function deleteBonus(ids) {
  return request({
    url: '/check/delete/bonus',
    method: 'delete',
    data: ids
  })
}