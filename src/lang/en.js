export default {
  btn: {
    startUsing: "Enable",
    forbidden: "Disabled"
  },
  mapCoordinatePicker: {
    searchPlaceholder: 'Search for places...',
    resetMap: 'Reset Map',
    selectedCoordinate: 'Selected Coordinate',
    latitude: 'Latitude',
    longitude: 'Longitude',
    address: 'Address',
    mapLoadError: 'Failed to load map library, please check network connection',
    mapInitError: 'Failed to initialize map',
    invalidPlace: 'Invalid place information',
    placeNotFound: 'No related places found',
    addressFetchError: 'Failed to fetch address'
  },
  msg: {
    send: "Send"
  },
  logo: {
    title: ""
  },
  form: {
    select: "please select ",
    input: "Please input",
    upload: "Please upload",
    enableData: "Please select enabled data",
    disabledData: "Please select disabled data",
    to: "to"
  },
  navbar: {
    dashboard: "Dashboard",
    github: "Github",
    logOut: "Log Out",
    profile: "Profile",
    theme: "Theme",
    size: "Global Size"
  },
  login: {
    title: "AddMotor",
    logIn: "Sign In",
    username: "Userna<PERSON>",
    password: "Password",
    usernameVoid: "Username couldn't be empty",
    passwordVoid: "Password couldn't be empty",
    remember: "Forgot password",
    smsCode: "Verification code",
    smsCodeVoid: "Verification code couldn't be empty"
  },
  tagsView: {
    refresh: "Refresh",
    close: "Close",
    closeOthers: "Close Others",
    closeAll: "All Close",
    download: "download"
  },
  settings: {
    title: "System Layout Configuration",
    theme: "Theme Color",
    tagsView: "Tags-View",
    fixedHeader: "Fixed Header",
    sidebarLogo: "Siderbar Logo"
  },
  queryParams: {
    reset: "Reset",
    search: "Search",
    recover: "Recovery",
    delete: "Delete",
    add: "Add",
    update: "Modify",
    export: "Export",
    upload: "Upload",
    uploadIng: "Up Cross",
    uploadErr: "Upload Error"
  },
  fileUpload: {
    selectFile: "Select File",
    uploading: "Uploading...",
    uploadProgress: "Upload Progress",
    remaining: "remaining",
    limitReached: "Limit reached",
    uploadedFiles: "Uploaded Files",
    clearAll: "Clear All",
    preview: "Preview",
    download: "Download",
    delete: "Delete",
    filePreview: "File Preview",
    unsupportedPreview: "This file type is not supported for preview",
    confirmClearAll: "Are you sure to clear all files?",
    confirmClearAllTitle: "Confirm",
    confirm: "Confirm",
    cancel: "Cancel",
    clearAllSuccess: "All files cleared successfully",
    deleteSuccess: "File deleted successfully",
    uploadSuccess: "Upload successful",
    uploadFailed: "Upload failed",
    fileSizeExceeded: "File size cannot exceed",
    maxFilesReached: "Maximum files allowed",
    filesSelected: "files",
    currentlyHas: "currently has",
    chunkProgress: "Chunk Progress",
    fileSize: "File Size",
    fileMd5: "File MD5",
    uploadingFile: "Uploading",
    calculatingMd5: "Calculating file MD5 hash...",
    uploadingChunk: "Uploading chunk",
    allChunksCompleted: "All chunks uploaded successfully!",
    fileReadError: "File read error",
    chunkReadError: "Chunk read error",
    chunkUploadFailed: "Chunk upload failed",
    networkError: "Network error, retrying...",
    dragOrClick: "Drag files here or click to select",
    supportedFormats: "All file formats supported"
  },
  mointerObj: {
    host: "host",
    hostIP: "IP",
    SessionNumber: "Session number",
    loginName: "Login name",
    loginPlace: "Login location",
    browser: "Browser",
    OperatingSystem: "Operating system",
    loginTime: "Login time",
    StrongRetreat: "Strong retreat",
    correctVerionNumber: "Please enter the correct version number"
  },
  mqttErr: {
    Vltd: "Vltd",
    BrakeSteering: "Brake Steering",
    MotorHall: "Motor Hall",
    CommunicationDetection: "Communication Detection",
    OvertemperatureDetection: "Overtemperature Detection",
    Controller: "Controller",
    OvercurrentDetection: "Overcurrent Detection",
    Normal: "Normal",
    Failure: "Failure",
    Risk: "Risk"
  },
  dialog: {
    confirm: "Confirm",
    cancel: "Cancel",
    confirmStatus: "Are you sure to {action}?",
    addSuccess: "Add successfully",
    updateSuccess: "Modify successfully",
    deleteSuccess: "Delete successfully",
    cancelMedal: "Cancel Medal successfully"
  },
  equipmentModel: {
    lock: "lock",
    ElectronicVariableSpeed: "Electronic Variable Speed",
    TurnSignal: "Turn Signal"
  },
  agreement: {
    blueTooth: "Bluetooth binding help documentation",
    scan: "Scan code binding help file",
    privacy: "Privacy agreement",
    user: "User statement",
    language: "language",
    uploadFile: "Please Upload file"
  },
  upgradeLog: {
    documentType: "Document Type",
    upgradeType: "Upgrade Type",
    des: "Upgrade Description",
    newVersion: "New Version",
    newVersionNumber: "New Version Number",
    oldVersion: "Old Version",
    oldVersionNumber: "Old Version Number",
    result: "Upgrade Result",
    resultDesc: "Upgrade Result Description",
    addUpgradeLog: "Add Upgrade Log",
    resetUpgradeLog: "Reset Upgrade Log",
    advertiseContent: "AdvertiseMent Content",
    advertiseMentLink: "AdvertiseMent Link",
    longTermEffective: "long Term Effective",
    fixedDate: "Fixed Date"
  },
  route: {
    basis: "Compared with last week",
    faultCount: "Error code record",
    RegisterError: "Register exception management",
    GeneralEquipmentManage: "General Equipment Manage",
    Modelmanage: "Model Manage",
    RegisterManage: "logout management",
    Controls: "Control",
    home: "Home",
    BMenu: "B Menu",
    Agreement: "Document",
    CustomerCenter: "Customer Center",
    Acc: "User Management",
    MeterCenter: "Meter Center",
    ModelCenter: "Car Model Center",
    BackLight: "Backlight Management",
    ShopManage: "Shop Manage",
    OrderManage: "Order Manage",
    NaviagtorManage: "Navigator Manage",
    ImCenter: "IM Center",
    SmartHeadManage: "Smart Head Manage",
    EquipmentCenter: "Equipment Center",
    UpgradeLog: "Upgrade Log",
    UserManage: "User Management",
    CycleRecodle: "Model management of cycling recorder",
    User: "Users",
    Log: "User Log",
    Equipmentmanage: "Equipment Manage",
    HeadManage: "Smart helmet model management",
    Riding: "Record Management",
    UserDetail: "User Details",
    Bike: "Ebike Management",
    Customer: "Customer Management",
    Brand: "Brand Management",
    Computer: "Display Management",
    Model: "Ebike Type",
    Bikes: "Ebike Management",
    Base: "Customer Service",
    Feedback: "Feedback",
    Carexception: "Exception types",
    Exception: "Fault code",
    Store: "Dealers",
    Help: "User Guide",
    Fqa: "FAQ",
    Discover: "Discover Management",
    HdMedalManage: "Hd MedalManage",
    Posted: "Posted Management",
    Remark: "Comment Management",
    Medal: "Medal Management",
    Advertise: "Advertise Management",
    BaseMedal: "Medal Rules",
    HdBaseMedal: "HD Medal Rules",
    Medal: "Medal Distribution",
    AccMsg: "Messege Management",
    Push: "Push",
    Msg: "Messege",
    Monitor: "System Monitoring",
    Monitor: "System Monitoring",
    Online: "Online Users",
    Job: "Timed Task",
    Druid: "Data Monitoring",
    Server: "Server Monitoring",
    System: "System Management",
    User: "User Detail",
    Role: "Role Management",
    Menu: "Menu Management",
    Dept: "Department Management",
    Post: "Position Management",
    Dict: "Dictionary Management",
    Notice: "Announcement",
    Not: "Without Track",
    Have: "With Track",
    Update: "Update Management",
    Config: "Parameter Settings",
    Operlog: "Operation Log",
    RidingManage: "Riding Management",
    StrategyManage: "Strategy Management",
    Logininfor: "Sign in Log",
    Configure: "Ebike configuration",
    Info: "Ebike info",
    Gear: "Ebike gear",
    Battery: "Battery info",
    BatteryUpdate: "Battery upgrade",
    Bind: "Instrument binding",
    App: "App",
    IOT: "IOT Management",
    IotOrder: "IOT Order",
    IOTAlarmRecord: "Alarm record",
    IOTCharges: "IOT tariff management",
    RemoteRecord: "Remote diagnostic record",
    IOTRenewal: "IOT Renewal",
    IotDeviceLog: "IOT Device Log",
    IotDevicePage: "IOT Device List",
    OwnEquipment: "Owning Equipment",
    ModelManage: "Model Manage",
    RadarManage: "Radar Manage",
    SportCamearManage: "Sport Camear Manage",
    KeyManage: "Key Manage",
    ElectronicLock: "Electronic Lock",
    DeviceManage: "Device Manage",
    IotManage: "Iot Manage",
    DeviceUpdate: "Device Update",
    IOTEquity: "IOT Equity",
    Tool: "System Tool",
    Build: "Form Build",
    Gen: "Code Generation",
    Profile: "Profile",
    AppSwiper: "App Swiper",
    TeamActivity: "Team Activity",
    RideSwiper: "Ride Swiper",
    RideNewBike: "Ride New Bike",
    RideNewBikeList: "Ride New Bike List",
    PhotographyGame: "Photograpyhy Game",
    AwardWorks: "Award Works",
    UnbindState: "Unbind State",
    TopicType: "Topic Type",
    RidingMenu: "Riding Menu",
    RidingManage: "Team Management",
    StrategyManage: "Strategy Manage",
    H5version: 'H5 Version Manage',
    'Identity-auth': 'Identity Auth',
    'Appeal': 'Appeal',
    Guide: 'Guide',
    'Device-ad': 'Device Ad',
    RidingApp: 'Riding Manage',
    News: 'News Manage',
    Bouns: 'Check-in management',
    UserTask: 'User Task Manage',
    Data: 'Dictionary type'
  },

  unbindState: {

  },
  iot: {
    reportType: "Report Type",
    sendOnDevice: "Send On Device",
    serviceDelivery: "Service Delivery",
    topicHead: "Topic Head",
    deviceStatus: "Device Status",
    onLine: "online",
    offLine: "offline",
    iotName: "IOT Name",
    ipRegion: "IP Region",
    iotSecret: "IOT Secret",
    iotProductKey: "IOT Product Key",
    isLog: "Log Record",
    notOpen: "Not Open",
    open: "Open",
    certificateFile: "Certificate File",
    privateFile: "Private File",
    rootFile: "Root File",
    markAffiliation: "Mark Affiliaction",
    homonychidae: "Homonychidae",
    notHomonychidae: "Not Homonychidae"

  },
  team: {
    headerLeader: "Header Leader",
    member: "Member",
    codeImg: "Add Code",
    seeMember: "See Member",
    teamName: "Team Name",
    disslove: "disslove",
    nickName: "nickName",
    headImg: "headImg",
    type: "type",
    delete: "delete",
    teamLeader: "Team Leader",
    teamMember: "Team Member"
  },
  shopManage: {
    searchTxt: {
      order: "Order Number",
      userIdOrName: "User ID/User Nickname",
      goodName: "Product Name",
      status: "Status",
      orderInfo: "Order Information",
      goodsSnapshot: "Commodity snapshot"
    },
    tableTxt: {
      userId: "User Id",
      payChannel: "Payment Channel",
      amount: "Payment Amount",
      orderTime: "Order Time",
      payTime: "Time to complete payment",
      detail: "Detail",
      price: "price",
      name: "name",
      productNo: "Product No",
      isRenew: "Is Renew",
      FirstFree: "First Free",
      Renew: "Renew"
    },
    stateList: {
      waitPay: "Pending payment",
      payed: "Paid",
      payError: "Transaction failed",
      cancelPay: "Cancel payment"
    }
  },
  navigatorManage: {
    optionTxt: {
      freeze: "Freeze",
      recover: "Recover",
      data: "data"
    },
    tableTxt: {
      userName: "User Name",
      payChannel: "Pay Channel",
      startTime: "Start Time",
      residueDate: "Residue Date",
      num: "Usage count",
      opt: "Option",
      ProductSnapshot: "Product Snapshot",
      IOTName: "IOT Name"
    },
    stateList: {
      inEffect: "InEffect",
      expired: "Expired",
      frozen: "Frozen"
    },
    typeList: {
      navigation: "Navigation",
      skin: "Skin",
      other: "Other"
    }
  },
  home: {
    ProportionVehicles: "Proportion of model numbers",
    InteractiveHeatRatio: "Interactive heat ratio",
    ErrorCodeRatio: "Error code ratio",
    ProportionBrandUsers: "Proportion of model users",
    sports: "Record",
    essay: "Posts/review",
    posted: {
      comment: "Review statistics",
      essay: "Posts statistics"
    },
    basis: "Compared with last week",
    faultCount: "Error code record",
    modelId: "Ebike Type",
    code: "Error Code",
    errChart: "Error Statistics",
    carChart: "Ebike Statistics",
    day: "Day",
    week: "Week",
    month: "Month",
    tatal: "Total",
    percentage: "km",
    Rank: "Rank",
    user: "User",
    activeUserNum: "Active User",
    sumUserNum: "All Users",
    deliverNum: "Delivered Ebike",
    activateNum: "Paired Ebike",
    sumMileage: "Odometer",
    sumEconomyTree: "Trees Saved",
    sumCalorie: "Sum Calorie",
    userChart: "Users Statistics",
    motionChart: "Sports Statistics",
    totalUsers: "Overall users",
    totalBike: "Total amount of car",
    bindBike: "Bound vehicle",
    faultRecordQuantity: "Fault log",
    increaseDay: "Growth",
    increaseWeek: "The increasing",
    increaseMonth: "Month growth",
    sports: "Record",
    essay: "Posts/review",
    posted: {
      growthStatistics: "User/vehicle growth statistics",
      comment: "Review statistics",
      essay: "Posts statistics",
      addUserNum: "New users",
      addBikeNum: "Number of new vehicles",
      day: "Day",
      week: "Week",
      month: "Month",
      total: "Total",
      mIncrement: " Monthly Add",
      up: "Up",
      totalVehicle: "Total vehicle",
      totalDegree: "Total Degree",
      monthDegree: "Month Degree",
      averageDegree: "Average Degree",
      tracklessNumber: "Trackless Number",
      trakesNumber: "Trakes Number"
    },
    riding: {
      totalUploadedMiles: "Total uploaded miles",
      haveRiding: "Track riding",
      noRiding: "Trackless riding"
    },
    totalRidingTime: "Total Cycling time",
    basis: "Compared with last week",
    faultCount: "Error code record",
    modelId: "Ebike Type",
    code: "Error Code",
    errChart: "Error Statistics",
    carChart: "Ebike Statistics",
    tatal: "Total",
    percentage: "km",
    Rank: "Rank",
    user: "User",
    activeUserNum: "Active User",
    sumUserNum: "All Users",
    deliverNum: "Delivered Ebike",
    activateNum: "Paired Ebike",
    sumMileage: "Odometer",
    sumEconomyTree: "Trees Saved",
    sumCalorie: "Sum Calorie",
    userChart: "Users Statistics",
    motionChart: "Sports Statistics"
  },
  acc: {
    user: {
      phone: "Phone",
      phoneInput: "Please input phone number"
    },
    identityAuth: {
      title: "Identity Authentication Management",
      userId: "User ID",
      userName: "User Name",
      realName: "Real Name",
      idNumber: "ID Number",
      frontImage: "ID Card Front",
      backImage: "ID Card Back",
      selfieImage: "Selfie with ID Card",
      status: "Audit Status",
      statusOptions: {
        pending: "Pending",
        approved: "Approved",
        rejected: "Rejected"
      },
      submitTime: "Submit Time",
      auditTime: "Audit Time",
      auditReason: "Audit Reason",
      auditBy: "Auditor",
      operation: "Operation",
      addTitle: "Add Identity Authentication",
      editTitle: "Edit Identity Authentication",
      viewTitle: "View Identity Authentication",
      approve: "Approve",
      reject: "Reject",
      reasonInput: "Please input audit reason",
      statusChangeSuccess: "Status changed successfully",
      submitReason: "Submit Reason",
      imageUploadTip: "Only jpg/png files are allowed, and the size should not exceed 2MB",
      isAdmin: "Is Admin",
      adminYes: "Yes",
      adminNo: "No"
    },
    userLevel: {
      title: "User Score Level Management",
      id: "ID",
      level: "Level",
      levelInput: "Please enter level",
      point: "Points Required for Level",
      pointInput: "Please enter points required for level",
      createTime: "Create Time",
      status: "Status",
      enabled: "Enabled",
      disabled: "Disabled",
      index: "Index",
      addTitle: "Add User Score Level",
      updateTitle: "Update User Score Level",
      deleteConfirm: 'Are you sure to delete the data?',
      deleteSuccess: "Delete successful",
      addSuccess: "Add successful",
      updateSuccess: "Update successful",
      enableSuccess: "Enable successful",
      disableSuccess: "Disable successful",
      levelRequired: "Level is required",
      pointRequired: "Points required for level is required",
      // User Level Interval
      interval: {
        title: "User Level Interval Management",
        intervalName: "Interval Name",
        intervalNameInput: "Please enter interval name",
        minLevel: "Min Level",
        maxLevel: "Max Level",
        minPoint: "Min Points",
        maxPoint: "Max Points",
        grayIcon: "Gray Icon",
        lightenIcon: "Lighten Icon",
        medalList: "Level Medals",
        addInterval: "Add Level Interval",
        editInterval: "Edit Level Interval",
        deleteInterval: "Delete Level Interval",
        setTasks: "Set Tasks",
        taskSetSuccess: "Task setting successful",
        taskManagement: "Task Management",
        taskName: "Task Name",
        taskNameInput: "Please enter task name",
        taskEnName: "English Name",
        taskEnNameInput: "Please enter english name",
        taskCode: "Task Code",
        taskCodeInput: "Please enter task code",
        taskDescription: "Description",
        taskCoins: "Coins",
        taskPoint: "Points",
        taskCondition: "Condition",
        taskCycle: "Cycle",
        taskType: "Type",
        taskModel: "Module",
        taskStatus: "Status",
        // Task type enums
        taskTypeBasic: "Basic Task",
        taskTypeAdvanced: "Advanced Task",
        // Task model enums
        taskModelGeneral: "General",
        taskModelSpecial: "Special",
        // Task type search
        taskTypeSelect: "Please select type",
        selectTasks: "Select Tasks",
        selectedTasks: "Selected Tasks",
        noTasksSelected: "Please select at least one task",
        taskSelectionTitle: "Set Tasks for Level Interval",
        intervalNameRequired: "Interval name is required",
        minLevelRequired: "Min level is required",
        maxLevelRequired: "Max level is required",
        minPointRequired: "Min points is required",
        maxPointRequired: "Max points is required",
        // Medal types
        medalTypes: {
          totalMileage: "Total Mileage",
          discoveryComment: "Discovery Comment",
          userLevel: "User Level",
          discoveryPost: "Discovery Post",
          fans: "Fans",
          ridingGuidePublish: "Riding Guide Publish",
          ridingGuideCollect: "Riding Guide Collect",
          fitnessExercise: "Fitness Exercise",
          createTeam: "Create Team",
          joinTeam: "Join Team",
          photographyPublish: "Photography Publish",
          bikeCircleAdmin: "Bike Circle Admin",
          shoppingExpert: "Shopping Expert"
        }
      }
    },
    log: {
      nickName: "Nickname",
      nickNameInput: "Please input nickname",
      email: "Email",
      emailInput: "Please input email",
      userId: "User ID",
      url: "Requested path",
      method: "Requested method",
      param: "Requested parameter",
      result: "Return Value",
      os: "Operating System",
      ip: "IP",
      registerStatus: "register status",
      logoutStatus: "Logout Status",
      confirmLogout: "Confirm logout",
      processed: "processed",
      notProcessed: "Not processed",
      registed: "registered",
      noRegisted: "unregistered",
      resetLogout: "Logout",
      loggedOut: "Logged Out",
      notLoggedOut: "Not Logged Out",
      equipmentType: "Equipment type",
      equipmentSn: "Equipment SN",
      deviceModel: "Device Model",
      smartCarLock: "Smart Car Lock",
      electronicShif: "Electronic Shifting",
      smartTurnSignal: "Smart Turn Signal"
    },
    medal: {
      userKey: "Email/Password",
      importNameOrCodingOrSynopsis:
        "Please input designation/code/brief introduction",
      timeOfRelease: "Distribution time",
      beginTime: "Statred time",
      endTime: "Ended time",
      search: "Search",
      reset: "Reset",
      serialNumber: "No.",
      email: "Email",
      nickName: "Nickname",
      name: "Medal name",
      provideCause: "Distribution Reason"
    },
    msg: {
      msg: {
        messageType: "Message Type",
        notifyType: "Notify Type",
        notifyOption: {
          homePage: "Link To Home Page",
          discoveryPage: "Link To Discovery Page",
          H5Page: "H5 Link",
          messageListPage: "Link To Message List"
        },
        search: "Search",
        reset: "Reset",
        newAdd: "Add",
        startUsing: "Enable",
        forbidden: "Disabled",
        serialNumber: "No.",
        how_much_coverage: "How much coverage",
        how_much_read: "Read",
        describe: "Description",
        messageBody: "Message body",
        system: "System",
        oneMan: "Personal",
        choose: "Choose",
        all: "All",
        activatedState: "Activated Status",
        operation: "Modified",
        update: "Modified",
        title: "Title",
        pleaseInput: "Please input",
        yes: "Yes",
        no: "No",
        confirm: "Confirm",
        cancel: "Cancel",
        whetherToPush: "Push",
        push: "Push",
        messageRange: "Message Range",
        messageSystem: "System message",
        linkAddress: "Link Address",
        selectUser: "Select The User",
        messageTypeNotBeNull: "Message type cannot be empty ",
        describeNotBeNull: "Description cannot be empty",
        messageBodyNotBeNull: "Message body cannot be empty",
        titleNotBeNull: "Title cannot be empty",
        pleaseChoosewhetherToPush: "Please choose push or not",
        linkAddressNotBeNull: "Link address cannot be empty",
        pleaseChooseNotifyType: "Please choose notify type",
        pleaseChooseUser: "Please choose user",
        pleaseChoosePushRange: "Please choose push range",
        blockUp: "Stop",
        warn: "Warning",
        sure: "Confirm",
        theUser: "?",
        confirm: "Confirm",
        cancel: "Cancel",
        Warning: "Warning",
        succeed: "Successful",
        addMessage: "Add message",
        updateMessage: "Modified message",
        what_: "?",
        addSucceed: "Add successfully",
        updateSucceed: "Modified successfully",
        _confirm: "Confirm",
        _cancel: "Cancel"
      },
      push: {
        title: "Title",
        pleaseInputTitle: "Please input title",
        describe: "Description",
        pleaseInputDescribe: "Please input description",
        pushData: "Other content",
        pleaseInputPushData: "Please input other  content",
        whetherToNotifyEveryone: "Would you like to notify everyone",
        yes: "Yes",
        no: "No",
        immediatelyCreate: "Add right now",
        reset: "Reset",
        titleNotBeNull: "Title cannot be empty",
        describeNotBeNull: "Description cannot be empty",
        pushDatabeNotBeNull: "Other content cannot be empty",
        pleaseChooseToNotifyEveryone: "Please select to notify everyone",
        sendSuccess: "Send successfully"
      }
    },
    riding: {
      have: {
        cyclingTime: "Ride time",
        beginDate: "Started date",
        endDate: "Ended time",
        search: "Search",
        reset: "Reset",
        serialNumber: "No.",
        nickName: "User Name",
        ridingTime: "Cycling Time",
        email: "Email",
        mileage: "mile",
        syncTime: "Sync Time",
        operation: "Operate",
        particulars: "Details",
        altitudeHeight: "Altitude",
        altitudeUp: "Altitude increase",
        ridingTime: "Cycling time",
        startTime: "Cycling started time",
        calorie: "Calorie",
        carbonEmission: "CO₂ Reduced",
        mileage: "Trip",
        maxSpeed: "Max Speed",
        economyTree: "Trees Saved"
      },
      not: {
        cyclingTime: "Ride time",
        beginDate: "Started date",
        endDate: "Ended time",
        search: "Search",
        reset: "Reset",
        serialNumber: "No.",
        nickName: "User Name",
        ridingTime: "Cycling time",
        email: "Email",
        mileage: "mile",
        syncTime: "Sync Time",
        operation: "Operate",
        particulars: "Details",
        altitudeHeight: "Altitude",
        altitudeUp: "Altitude increase",
        ridingTime: "Ride time",
        startTime: "Cycling started time",
        calorie: "Calorie",
        carbonEmission: "CO₂ Reduced",
        mileage: "Trip",
        maxSpeed: "Max Speed",
        economyTree: "Trees Saved"
      }
    },
    user: {
      country: "Country",
      bikeName: "Bike Name",
      AnnualCard: "Annual card",
      orderDetail: "Order details",
      addIotCharges: "New IOT charges",
      resetIotChares: "Modify the IOT tariff",
      alarmType: "Alarm type",
      alarmTime: "Alarm time",
      DisplacementAlarm: "Displacement Alarm",
      SOSAlarm: "SOS Alarm",
      FenceAlarm: "Fence Alarm",
      VibrationAlarm: "Vibration Alarm",
      LocationInfo: "Location Info",
      diagnosticMessage: "Diagnostic Info",
      diagnosticTime: "Diagnostic Time",
      RenewalRecord: "Renewal Record",
      TripartitePlatform: "Tripartite Platform",
      Normal: "Normal",
      arrears: "Arrears",
      isRead: "IS Read",
      read: "read",
      unread: "unread",
      activatedState: "Activated status",
      nameOremail: "Nickname/Email",
      pleaseInputNameOremail: "Please confirm Nickname/Email",
      sex: "Gender",
      lastActivityTime: "Last activity time",
      registrationTime: "Registration Time",
      beginDate: "Started date",
      endDate: "Ended time",
      search: "Search",
      reset: "Reset",
      startUsing: "Enable",
      forbidden: "Disable",
      nickName: "Nickname",
      phone: "Phone",
      email: "Email",
      man: "Male",
      woman: "Female",
      unknown: "Unknow",
      city: "City",
      birthday: "Date of birth",
      operation: "Operate",
      particulars: "Details",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      theUser: "?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      succeed: "Successful",
      what_: "?",
      detail: {
        basicInformation: "Basic info",
        nickName: "User Name：",
        sex: "Gender：",
        man: "Male",
        woman: "Female",
        unknown: "Unknow",
        birthday: "Date of birth：",
        city: "City：",
        registerTime: "Registration time:",
        status: "Activated status：",
        sumMileage: "Odometer：",
        lastTime: "Last activity time：",
        subordinateToTheVehicle: "Owned Ebike",
        carName: "Name",
        bindTime: "Paired time",
        activated: "Activated",
        nonactivated: "Inactivated"
      }
    }
  },
  base: {
    carexception: {
      code: "Error Code",
      codeInput: "Please input error code",
      modelId: "Ebike type",
      modelIdInput: "Please input ebike type",
      customer: "Customer",
      customerKeyInput: "Please input customer",
      brandId: "Brand",
      brandIdInput: "Please input brand",
      queryTime: "Reporting time",
      beginDate: "Started date",
      endDate: "Ended time",
      reset: "Reset",
      search: "Search",
      customerName: "No.",
      email: "Email",
      nickName: "Nickname",
      city: "City",
      createTime: "Reporting time",
      id: "Error ID",
      startUsing: "Enable",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      theUser: "User吗?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      succeed: "Successful",
      what_: "?",
      addInstrument: "Add display",
      BikeDetail: "Ebike details",
      updateSucceed: "Changed successfully"
    },
    exception: {
      codeOrName: "Error Code/Name",
      showCode: "Show Code",
      codeOrNameInput: "Please inputError Code/Name",
      belongToCarModel: "Ebike type ",
      belongToCarModelInput: "Please choose ebike type",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      serialNumber: "No.",
      code: "Error Code",
      customerName: "Owned customer",
      brandName: "Brand",
      name: "Error name (zh)",
      nameEn: "Error name  (en）",
      desc: "Error description (zh)",
      descEn: "Error description （en）",
      createBy: "Creator",
      createTime: "Created time",
      state: "Status",
      solve: "Solution",
      lookOver: "View",
      operation: "Operate",
      update: "Modify",
      processingScheme: "Solution",
      codeInput: "Please inputError Code",
      nameInput: "Please input error name",
      descInput: "Please input erro description",
      solveInput: "Please inputSolution",
      _confirm: "Confirm",
      _cancel: "Cancel",
      nameNotNull: "Error name cannot be empty",
      codeNotNull: "Error code cannot be empty",
      descNotNull: "Error description cannot be empty",
      belongToCarModelNotNull: "Ebike type cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      theUser: "",
      what_: "?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      succeed: "Successful",
      addCode: "Add error Code",
      updateCode: "Modify error Code",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful"
    },
    topicManage: {
      topicName: 'Topic Name',
      typeName: 'Type Name',
      topicSort: 'Topic Sort',
      status: 'Status',
      creator: 'Creator',
      createTime: 'Create Time',
      operation: 'Operation',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      enable: 'Enable',
      disable: 'Disable',
      batchEnable: 'Batch Enable',
      batchDisable: 'Batch Disable',
      addTopicType: 'Add Topic Type',
      editTopicType: 'Edit Topic Type',
      pleaseEnterTopicName: 'Please enter topic name',
      pleaseEnterTypeName: 'Please enter type name',
      pleaseSelectTypeName: 'Please select type name',
      pleaseEnterTopicSort: 'Please enter topic sort',
      pleaseSelectStatus: 'Please select status',
      confirmDelete: 'Are you sure to delete?',
      deleteSuccess: 'Delete successfully',
      deleteFailed: 'Delete failed',
      editSuccess: 'Edit successfully',
      addSuccess: 'Add successfully',
      operationSuccess: 'Operation successfully',
      operationFailed: 'Operation failed',
      pleaseSelectData: 'Please select data to operate',
      batchOperationConfirm: 'Are you sure to perform this operation on selected data?'
    },
    feedback: {
      type: "Feedbeck type",
      pleaseChoose: "Please choose",
      LastReply: "Recoil state",
      reset: "Reset",
      search: "Search",
      serialNumber: "No.",
      customerName: "Owned customer",
      feedbackType: "Feedbeck type",
      desc: "Description",
      nickName: "Nickname",
      email: "Email",
      LastReplyDate: "Last replied time",
      user: "No reply",
      system: "Reply message",
      operation: "Operate",
      lookOver: "View",
      respondFeedback: "Replied feedback",
      wordsReply: "Text replied",
      imageReply: "Picture replied",
      clickUpload: "Upload",
      _confirm: "Confirm",
      _cancel: "Cancel",
      sort: "Order：",
      invertedOrder: "Reverse",
      positiveSequence: "Positive",
      user_: "User：",
      reply: "Reply",
      problemDescription_: "Problem description:",
      problemDescription_: "Problem description:",
      platform: "Platform",
      complaint: "Complain",
      repairs: "Repair",
      errorCorrection: "Error correction",
      feedback: "Feedback",
      suggest: "Suggestion",
      pleaseUploadPictures: "Please upload picture",
      pleaseEnterARply: "Please input reply",
      sendSuccess: "Send successful"
    },
    fqa: {
      nameOrCodeOrBrief: "Name/Code/Brief introduction",
      issueOrDescribeInput: "Please input questions/description",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      serialNumber: "No.",
      questionName: "Questions",
      customerName: "Owned customer",
      sort: "Order",
      reply: "Reply",
      lookOver: "View",
      activatedState: "Activated status",
      operation: "Operate",
      update: "Modify",
      particulars: "Details",
      customerNameInput: "Please input owned customer",
      questionNameInput: "Please input questions",
      sortInput: "Please inputOrder",
      replyInput: "Please inputReply",
      _confirm: "Confirm",
      _cancel: "Cancel",
      questionName_: "Questions:",
      activatedState_: "Activated status:",
      activated: "Activated:",
      activatedNot: "Inactivated:",
      sort_: "Order:",
      reply_: "Reply",
      questionNameNotNull: "Questions cannot be empty",
      replyNotNull: "Reply cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      theUser: "?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "?",
      succeed: "Successful",
      addFQA: "Add FQA",
      updateFQA: "Modify FQA",
      addSucceed: "Add successful",
      updateSucceed: "ModifySuccessful"
    },
    help: {
      carNameOrCode: "questions/Description",
      questionInput: "Please input questions",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      serialNumber: "No.",
      customerName: "Owned customer",
      customerNameInput: "Please input owned customer",
      title: "Name",
      desc: "Description",
      content: "Content",
      createBy: "Creator",
      createTime: "Created time",
      state: "Status",
      solve: "Solution",
      lookOver: "View",
      operation: "Operate",
      update: "Modify",
      customerId: "Owned customer",
      titleInput: "Please input dealer name",
      descInput: "Please input dealer description",
      contentInput: "Please inputContent",
      _confirm: "Confirm",
      _cancel: "Cancel",
      titleNotNull: "Name cannot be empty",
      descNotNull: "Description cannot be empty",
      contentNotNull: "Content cannot be empty",
      customerIdNotNull: "Owned customer cannot be empty",
      startUsing: "Enable",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "?",
      succeed: "Successful",
      addSucceed: "AddSuccessful",
      updateSucceed: "ModifySuccessful",
      updateHelp: "Modify guide",
      addInstrument: "Add guide"
    },
    medal: {
      cancelSend: "Cancel Send",
      medalName: "Medal Name",
      enName: "English name",
      enDesc: "English brief introduction",
      medalNameInput: "Please input medal name",
      AchievementsPostVal: "Achievements meet post values",
      AchievementCommentVal: "The achievement satisfies the comment value",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      name: "Name",
      desc: "Brief introduction",
      illumeIcon: "Light icon",
      notIllumeIcon: "Turn off icon",
      type: "Rule type",
      activatedState: "Activated status",
      operation: "Operate",
      createBy: "Creator",
      createTime: "Created time",
      update: "Modify",
      descInput: "Please input medal brief introduction",
      clickUpload: "Upload",
      rule: "Distribute rules",
      ruleInput: "Please input distribute rules",
      selectMedalDesc: "Please choose medal brief introduction",
      _confirm: "Confirm",
      _cancel: "Cancel",
      nameNotNull: "User name cannot be empty",
      codeNotNull: "User code cannot be empty",
      typeNotNull: "Rule type cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      succeed: "Successful",
      addSucceed: "Add successfully",
      updateSucceed: "Modify successfully",
      addMedal: "Add medal",
      updateMedal: "Modify medal",
      astrict1: "Currently limits the number of files",
      astrict2: "",
      astrict3: "",
      oldVersion: "old version",
      newVersion: "new version",
      unitType: "Unit",
      DeviceKind: "Device Kind",
      DeviceAttribute: "Device Attribute",
      BasicParamerter: "Basic Paramerter",
      SaveTime: "Save Time",
      DeviceIcon: "DeviceIcon",
      Meter: "Meter",
      GPS: "GPS",
      crcCode: "CRC Value"
    },
    store: {
      website: "Website",
      shopName: "Dealer name",
      shopNameInput: "Please input dealer name",
      createTime: "Created time",
      beginDate: "Started date",
      endDate: "Ended time",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      serialNumber: "No.",
      customerName: "Owned customer",
      desc: "Dealer description",
      imgs: "Dealer picture",
      elseImgs: "Other pictures(Click View for more)",
      address: "Dealer address",
      longitude: "Longitude",
      latitude: "Latitude",
      longitudeAndLatitude: "longitude And Latitude",
      longitudeAndLatitudeErr: "The latitude and longitude format is incorrect",
      longitudeAndLatitudeScheme: "Latitude and longitude scheme",
      positionPick: "Position picking",
      GoogleMap: "Google Map",
      TencentMap: "Tencent Map",
      contact: "Contact person",
      contactPhone: "Phone number",
      state: "Status",
      createBy: "Creator",
      createTime: "Created time",
      operation: "Operate",
      update: "Modify",
      descInput: "Please inputDealerDescription",
      endDate: "Business hours",
      openTime: "Open Time",
      closeTime: "Close Time",
      labels: "Label",
      labelsInput: "Please input label",
      manager: "店长", //店长不需要
      managerInput: "店Please input 店长长", //店长不需要
      weight: "Weighted",
      weightInput: "Please input weighted",
      addressInput: "Please input dealer address",
      latitudeInput: "Please input latitude",
      longitudeInput: "Please input longitude",
      contact: "Contact person",
      contactInput: "Please input contact person",
      contactPhone: "Phone number",
      contactPhoneInput: "Please input phone number",
      contactEmail: "Email",
      contactEmailInput: "Please input Email",
      clickUpload: "Upload",
      elseImg: "Other pictures",
      _confirm: "Confirm",
      _cancel: "Cancel",
      shopNameNotNull: "Dealer name cannot be empty",
      descNotNull: "Dealer description cannot be empty",
      addressNotNull: "Dealer description cannot be empty",
      contactNotNull: "Contact person cannot be empty",
      contactPhoneNotNull: "Phone number cannot be empty",
      endDateNotNull: "Business hours  cannot be empty",
      labelsNotNull: "Label cannot be empty",
      longitudeNotNull: "Latitude cannot be empty",
      latitudeNotNull: "Longitude cannot be empty",
      managerNotNull: "cannot be empty", //店长不需要
      contactEmailNotNull: "Email cannot be empty",
      firstAidCallNotNull: "Rescue call cannot be empty",
      scoreNotNull: '"Rate cannot be empty"',
      weightNotNull: '"Weighted cannot be empty"',
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "吗?",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successfully",
      addShop: "AddDealer",
      updateShop: "Modify dealer",
      astrict1: "The number of uploads is currently limited",
      astrict2: "",
      astrict3: "",
      businessHours: "Please enter business hours (e.g. 9:00 ~ 17:30)",
      storeBasicInfo: "Store Basic Info",
      storeOpenHours: "Store Opening Hours"
    }
  },
  bike: {
    advertise: {
      title: "title",
      ValidityPeriod: "Validity Period"
    },
    bike: {
      isIot: "Support Iot or not",
      isAutoLight: "Automatic backlight",
      backLightSet: "Backlight brightness configuration",
      clientName: "Customer Name",
      category: "category",
      untie: "Untie",
      carNameOrCode: "Ebike type name",
      carNameOrCodeInput: "Please input ebike type/code",
      brandKey: "Brand name/code",
      brandKeyInput: "Please input brand name/code",
      computerKey: "Display name/model type",
      computerKeyInput: "Please input display name/model type",
      customerKey: "Owned customer",
      customerKeyInput: "Please inputOwned customer",
      bindState: "Pair status",
      reset: "Reset",
      search: "Search",
      customerName: "Owned customer",
      brandName: "Brand",
      computerName: "Display name",
      modelName: "Ebike type",
      nickName: "Owned user",
      registerTime: "Registration time",
      bluetooth: "Bluetooth address",
      bind: "Paired",
      notBind: "UnPair",
      operation: "Operate",
      particulars: "Details",
      batteryCapacity: "Battery Capacity ",
      bindTime: "Paired time",
      carName: "Ebike Name",
      hardwaresn: "Hardware SN",
      hardwareVersion: "Hardware Version",

      motorPower: "Motor",

      softwareVersion: "Software Version",
      voltage: "Voltage",

      bandNameNotNull: "Brand name cannot be empty",
      bandCodeNotNull: "Brand code cannot be empty",
      belongsCustomer: "Owned customer cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "?",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      startUsing: "Enable"
    },
    brand: {
      clientInformation: "User information(Name/Code/Brief introduction)",
      clientInformationInput: "Please input user name/code/brief introduction",
      brandInformation: "Brand information(Name/Code/Brief introduction)",
      brandInformationInput: "Please input brand name/code/brief introduction",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      name: "Name",
      code: "Code",
      desc: "Brief introduction",
      customerName: "Owned customer",
      activatedState: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      operation: "Operate",
      update: "Modify",
      brandNameInput: "Please input brand name",
      brandCodeInput: "Please input brand code",
      brandDescInput: "Please input brand brief introduction",
      _confirm: "Confirm",
      _cancel: "Cancel",
      brandNameNotNull: "Brand name cannot be empty",
      brandCodeNotNull: "Brand code cannot be empty",
      brandDescNotNull: "Brand brief introduction cannot be empty",
      customerNameNotNull: "Owned customer cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "?",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      theUser: "",
      addInstrument: "Add brand",
      updateInstrument: "Modify brand"
    },
    computer: {
      name: "Name",
      nameInput: "Please input name",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      model: "Model Number",
      equipmentName: "Equipment Name",
      wifiName: "Wifi Name",
      wifiPass: "Wifi Pass",
      modelName: "Model Name",
      instructionBook: "Instruction Book",
      instructionBookName: "Description name",
      instructionLink: "Instruction link",
      country: "Country",
      viewingScreen: "Display",
      have: "Have",
      notHave: "Don't have", // Don't have  注意小撇号只有一个
      locateFunction: "GPS function",
      activatedState: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      updateTime: "Update time",
      audit: "Audit",
      review: "Review",
      auditStatus: "Approval Status",
      toAudit: "To Audit",
      toReview: "To Review",
      done: "Done",
      operation: "Operate",
      update: "Modify",
      gearSet: "Gear configuration",
      meterNameInput: "Please input display name",
      modelInput: "Please input display model number",
      update: "Modify",
      _confirm: "Confirm",
      _cancel: "Cancel",
      nameNotNull: "Customer name cannot be empty",
      codeNotNull: "Customer code cannot be empty",
      viewingScreenNotNull: "Have display or not cannot be empty",
      locateFunctionNotNull: "Have GPS or not cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      theUser: "?",
      addInstrument: "Add display",
      updateInstrument: "Modify display",
      // 提示
      placeholder: {
        productClass: "Please input display category",
        productModel: "Please input display Model",
        code: "Please input display Code"
      },
      // queryTable
      queryTable: {
        productClass: "Category",
        productModel: "Model",
        code: "Code",
        desc: "Describe"
      },
      // 校验
      rules: {
        productClass: "Category cannot be empty",
        productModel: "Model cannot be empty",
        code: "Code cannot be empty"
      }
    },
    customer: {
      nameOrCodeOrBrief: "Name/Code/Brief introduction",
      importNameOrCodingOrSynopsis: "Please input Name/Code/Brief introduction",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      name: "Name",
      code: "Code",
      adminName: "Administrator user name",
      adminPassword: "Administrator password",
      desc: "Brief introduction",
      activatedState: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      operation: "Operate",
      update: "Modify",
      particulars: "Details",
      nameInput: "Please input customer name",
      codeInput: "Please input customer code",
      adminNameInput: "Please input administrator user name",
      adminPasswordInput: "Please input administrator password",
      descInput: "Please input customer brief introduction",
      customerName: "Customer name already exists",
      customerNumber: "Customer code already exists",
      adminNameExist: "Administrator user name already exists",
      _confirm: "Confirm",
      _cancel: "Cancel",
      nameNotNull: "CustomerName cannot be empty",
      codeNotNull: "CustomerCode cannot be empty",
      adminNameNotNull: "Administrator user name cannot be empty",
      adminPasswordNotNull: "Administrator password cannot be empty",
      descNotNull: "Customer brief introduction cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      theUser: "",
      addClient: "Add customer",
      updateClient: "Modify customer",
      detailedInformation: "Detailed information",
      userInformation: "User information"
    },
    model: {
      emailName: "Code/Name/user nickname/mailbox",
      addHelete: "Add a smart helmet model",
      addModel: "Add Model",
      resetHelete: "Reset Helete",
      addRecl: "add Recl",
      resetRecl: "reset Recl",
      resetModel: "Reset Modal",
      gearGroupId: "Gear configuration",
      isNavi: "Support navigation",
      OtherSet: "Other Set",
      carNameOrCode: "Ebike type name/code",
      carNameOrCodeInput: "Please input ebike type name/code",
      brandNameOrCode: "Brand name/code",
      brandNameOrCodeInput: "Please input brand name/code",
      meterNameOrModelInput: "Please input display name/model number",
      belongsCustomer: "Owned customer",
      belongsCustomerInput: "Please input owned customer",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      name: "Name",
      code: "Code",
      desc: "Brief introduction",
      carTopImage: "Ebike type TOP picture",
      TopImage: "Top Image",
      carImages: "Ebike type picture(Click View for more)",
      customerName: "Owned customer",
      brandName: "Brand",
      activatedState: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      operation: "Operate",
      update: "Modify",
      particulars: "Details",
      carName: "Ebike type name",
      carNameInput: "Please input Ebike type name",
      battery: "Battery",
      batteryInput: "Please input battery",
      code: "Ebike typeCode",
      codeInput: "Please inputEbike type code",
      customerId: "Owned customer",
      brandId: "Brand",
      wheelSizeId: "Wheel Size",
      motor: "Motor",
      motorInput: "Please inputMotor",
      minSpeed: "Min Speed",
      minSpeedInput: "Please input min Speed",
      maxSpeed: "Max Speed",
      maxSpeedInput: "Please input max Speed",
      carDesc: "Ebike type brief introduction",
      carDescInput: "Please input ebike type code",
      clickUpload: "Upload",
      _confirm: "Confirm",
      _cancel: "Cancel",
      elseImg: "Other pictures",
      nameNotNull: "Name cannot be empty",
      batteryNotNull: "Battery cannot be empty",
      codeNotNull: "Ebike type code cannot be empty",
      descNotNull: "Description cannot be empty",
      motorNotNull: "Motor cannot be empty",
      customerIdNotNull: "Owned customer cannot be empty",
      wheelSizeIdNotNull: "Wheel size cannot be empty",
      computerIdNotNull: "Display cannot be empty",
      brandIdNotNull: "Brand cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      addCarModel: "Add ebike type",
      updateCarModel: "Modify ebike type",
      astrict1: "Currently limits the number of files", //此处及其以下三行代码需要连城一句话翻译
      astrict2: "",
      astrict3: "",
      DeviceKind: "Device Kind",
    },
    configure: {
      add: "Add the configuration",
      update: "Modify the configuration",
      name: "configuration name",
      nameInput: "Please enter a configuration name",
      code: "code"
    },
    info: {
      cnKey: "Chinese key",
      cnValue: "Chinese value ",
      enKey: "English key",
      enValue: "English value",
      input: "please input",
      modelId: "Ebike model",
      select: "please select ",
      del: "remove",
      deldesc: "Are you sure you want to remove this option?"
    },
    gear: {
      groupName: "The name of the gear group",
      computerGear: "Instrument gear",
      def: "IsDefault",
      modelId: "Ebike model ",
      viewGear: "According to the gear",
      yes: "yes",
      no: "no"
    },
    backLight: {
      lightSet: "Brightness configuration",
      backLightName: "Name of the backlight group",
      computerLight: "Instrument brightness value",
      viewLight: "Display brightness value"
    },
    battery: {
      capacity: "Design Capacity",
      nickName: "nickname",
      residueCapacity: "Surplus Capacity",
      sn: "sn ",
      useNum: "charge-discharge num ",
      useRate: "attrition rate",
      customerId: "client",
      bikeId: "bike"
    },
    bind: {
      bindType: "Binding model",
      bluetootCh: "Bluetooth search",
      ScanCode: "scan a QR code"
    },
    computer: {
      name: "Name",
      nameInput: "Please input name",
      reset: "Reset",
      search: "Search",
      newAdd: "Add",
      startUsing: "Enable",
      forbidden: "Disable",
      model: "Model Number",
      equipmentName: "Equipment Name",
      wifiName: "Wifi Name",
      wifiPass: "Wifi Pass",
      modelName: "Model Name",
      instructionBook: "Instruction Book",
      instructionBookName: "Description name",
      instructionLink: "Instruction link",
      country: "Country",
      viewingScreen: "Display",
      have: "Have",
      notHave: "Don't have",
      locateFunction: "GPS function",
      activatedState: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      updateTime: "Update time",
      audit: "Audit",
      review: "Review",
      auditStatus: "Approval Status",
      toAudit: "To Audit",
      toReview: "To Review",
      done: "Done",
      operation: "Operate",
      update: "Modify",
      gearSet: "Gear configuration",
      meterNameInput: "Please input display name",
      modelInput: "Please input display model number",
      _confirm: "Confirm",
      _cancel: "Cancel",
      nameNotNull: "Customer name cannot be empty",
      codeNotNull: "Customer code cannot be empty",
      viewingScreenNotNull: "Have display or not cannot be empty",
      locateFunctionNotNull: "Have GPS or not cannot be empty",
      blockUp: "Disable",
      warn: "Warning",
      sure: "Are you sure you want to do this?",
      confirm: "Confirm",
      cancel: "Cancel",
      Warning: "Warning",
      what_: "",
      succeed: "Successful",
      addSucceed: "Add successful",
      updateSucceed: "Modify successful",
      theUser: "?",
      addInstrument: "Add display",
      updateInstrument: "Modify display",
      // 提示
      placeholder: {
        productClass: "Please input display category",
        productModel: "Please input display Model",
        code: "Please input display Code"
      },
      // queryTable
      queryTable: {
        productClass: "Category",
        productModel: "Model",
        code: "Code",
        desc: "Describe"
      },
      // 校验
      rules: {
        productClass: "Category cannot be empty",
        productModel: "Model cannot be empty",
        code: "Code cannot be empty"
      }
    },
    ridingManage: {
      title: 'Riding Management',
      teamActivity: 'Team Activity Management',
      announcement_title: 'Riding Statement Announcement',
      activityRank: 'Activity Ranking',
      // Common operations
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      export: 'Export',
      search: 'Search',
      reset: 'Reset',
      confirm: 'Confirm',
      cancel: 'Cancel',
      operation: 'Operation',
      status: 'Status',
      index: 'Index',
      enable: 'Enable',
      disable: 'Disable',
      manageWaypoints: 'Manage Waypoints',
      waypoints: 'Waypoints',
      // Team activity related
      activityName: 'Activity Name',
      creator: 'Creator',
      organizer: 'Organizer',
      description: 'Activity Description',
      detail: 'Activity Detail',
      cover: 'Activity Cover',
      noCover: 'No Cover',
      startTime: 'Start Time',
      endTime: 'End Time',
      activityTime: 'Activity Time',
      timeRange: 'Time Range',
      to: 'to',
      participants: 'Participants',

      // Time shortcuts
      timeShortcuts: {
        today: 'Today',
        yesterday: 'Yesterday',
        lastWeek: 'Last Week',
        lastMonth: 'Last Month',
        lastThreeMonths: 'Last 3 Months'
      },
      maxParticipants: 'Max Participants',
      distance: 'Distance',
      type: 'Activity Type',
      cyclingType: 'Cycling Type',
      purview: 'Permission Setting',
      expandTime: 'Extend Time',
      firstPrize: 'First Prize',
      secondPrize: 'Second Prize',
      thirdPrize: 'Third Prize',
      activityStatusLabel: 'Activity Status',

      // Activity type
      activityType: {
        official: 'Official',
        personal: 'Personal'
      },

      // Cycling type
      cyclingTypes: {
        distance: 'Distance',
        route: 'Route',
        leisure: 'Leisure',
        sport: 'Sport',
        competition: 'Competition'
      },

      // Permission type
      purviewType: {
        public: 'Public',
        foryou: 'For You',
        friends: 'Friends'
      },

      // Activity status
      activityStatus: {
        registration: 'Registration',
        registered: 'Registered',
        ongoing: 'Ongoing',
        pending: 'Pending',
        ended: 'Ended'
      },

      // Status type
      statusLabel: 'Status',
      statusType: {
        normal: 'Normal',
        disabled: 'Disabled'
      },

      // Announcement related
      announcement: {
        title: 'Announcement Title',
        content: 'Announcement Content',
        createTime: 'Create Time',
        add: 'Add Riding Statement Announcement',
        edit: 'Edit Riding Statement Announcement',
        deleteConfirm: 'Are you sure to delete this riding statement announcement?',

        // Placeholders
        placeholders: {
          title: 'Please enter announcement title',
          content: 'Please enter announcement content',
          searchTitle: 'Please enter title to search'
        },

        // Validation messages
        validations: {
          titleRequired: 'Announcement title cannot be empty',
          contentRequired: 'Announcement content cannot be empty'
        }
      },

      // Placeholders
      placeholders: {
        activityName: 'Please enter activity name',
        organizer: 'Please enter organizer',
        description: 'Please enter activity description',
        cover: 'Please upload activity cover',
        startTime: 'Please select start time',
        endTime: 'Please select end time'
      },

      // Validation messages
      validations: {
        activityNameRequired: 'Activity name cannot be empty',
        organizerRequired: 'Organizer cannot be empty',
        detailRequired: 'Activity detail cannot be empty',
        startTimeRequired: 'Start time cannot be empty',
        endTimeRequired: 'End time cannot be empty',
        activityTimeRequired: 'Activity time cannot be empty',
        activityNameLength: 'Activity name length should be between 2-50 characters',
        detailLength: 'Activity detail length should be between 10-500 characters',
        cyclingTypeRequired: 'Cycling type cannot be empty',
        distanceRequired: 'Distance cannot be empty',
        distanceMax: 'Distance cannot exceed 1000 km',
        prizeMin: 'Prize cannot be less than 0',
        prizeMax: 'Prize cannot exceed 100000'
      },

      // Upload related
      upload: {
        dragText: 'Drop files here, or ',
        clickText: 'click to upload',
        uploading: 'Uploading...',
        selectFile: 'Select File'
      },

      // Operation messages
      addTeamActivity: 'Add Team Activity',
      editTeamActivity: 'Edit Team Activity',
      addAnnouncement: 'Add Riding Statement Announcement',
      editAnnouncement: 'Edit Riding Statement Announcement',
      deleteActivityConfirm: 'Are you sure to delete this team activity?',
      confirmDeleteBatch: 'Are you sure to delete the selected {count} activities?',
      selectItemsToDelete: 'Please select activities to delete',
      totalPrize: 'Total Prize',
      duration: 'Duration',
      days: ' days ',
      hours: ' hours ',
      minutes: ' minutes',
      justNow: 'Just now',
      minutesAgo: ' minutes ago',
      hoursAgo: ' hours ago', 
      daysAgo: ' days ago',
      weeksAgo: ' weeks ago',
      monthsAgo: ' months ago',
      minutesLater: ' minutes later',
      hoursLater: ' hours later',
      daysLater: ' days later',
      weeksLater: ' weeks later',
      monthsLater: ' months later',
      timeInfo: 'Time Information',
      deleteAnnouncementConfirm: 'Are you sure to delete this announcement?',
      warning: 'Warning',
      deleteSuccess: 'Deleted successfully',
      addSuccess: 'Added successfully',
      updateSuccess: 'Updated successfully',
      exportSuccess: 'Exported successfully',
      enableSuccess: 'Enabled successfully',
      disableSuccess: 'Disabled successfully',
      getActivityInfoFailed: 'Failed to get activity information',
      rankFeatureUnavailable: 'Ranking feature is temporarily unavailable, please try again later',
      confirmDelete: 'Are you sure to delete?',
      viewDetail: 'View Detail',
      exportConfirm: 'Are you sure to export current data?',

      // Others
      expandTimePlaceholder: 'Extend time (hours)',
      prizePlaceholder: 'Please enter prize amount',

      // Activity detail preview related
      activityDetail: 'Activity Detail',
      basicInfo: 'Basic Information',
      timeInfo: 'Time Information',
      activityInfo: 'Activity Information',
      prizeInfo: 'Prize Information',
      routeInfo: 'Route Information',
      creatorInfo: 'Creator Information',
      imageInfo: 'Image Information',
      title: 'Title',
      cyclingType: 'Cycling Type',
      activityStatus: 'Activity Status',
      createTime: 'Create Time',
      countNum: 'Participants',
      people: 'people',
      km: 'km',
      hours: 'hours',
      official: 'Official',
      personal: 'Personal',
      mileage: 'Mileage',
      route: 'Route',
      registrationOpen: 'Registration Open',
      registered: 'Registered',
      inProgress: 'In Progress',
      ended: 'Ended',
      normal: 'Normal',
      disabled: 'Disabled',
      public: 'Public',
      forYou: 'For You',
      friends: 'Friends',
      firstPrize: '1st Place',
      secondPrize: '2nd Place',
      thirdPrize: '3rd Place',
      waypoint: 'Waypoint',
      creator: 'Creator',
      groupId: 'Group ID',
      cover: 'Cover Image',
      adImg: 'Advertisement Image',
      pointImg: 'Track Photo',
      adUrl: 'Advertisement URL',

      // Form section titles
      basicInfo: 'Basic Information',
      timeSettings: 'Time Settings',  
      cyclingSettings: 'Cycling Settings',
      routeManagement: 'Route Management',
      prizeSettings: 'Prize Settings',
      statusSettings: 'Status Settings',
      
      // Tips
      coverTip: 'Recommended 16:9 ratio image, supports JPG, PNG formats',
      distanceTip: 'Unit: kilometers',
      waypointsCount: 'waypoints',

      // Ranking related
      rank: {
        title: 'Activity Ranking',
        ranking: 'Ranking',
        userInfo: 'User Info',
        spendTime: 'Time Spent',
        performance: 'Performance',
        totalParticipants: 'Total Participants',
        avgTime: 'Average Time',
        fastestTime: 'Fastest Time',
        slowestTime: 'Slowest Time',
        excellent: 'Excellent',
        good: 'Good',
        normal: 'Normal',
        average: 'Average',
        statisticsNote: 'Note: Statistics are based on all participants, not affected by search conditions',
        userDetail: 'User Detail',
        basicInfo: 'Basic Information',
        performanceInfo: 'Performance Information',
        nickName: 'Nickname',
        userId: 'User ID',
        activityId: 'Activity ID',
        consumerImg: 'Consumer Avatar',
        headImg: 'User Avatar'
      }
    },
    strategyManage: {
      title: 'Strategy Management',
      strategyTitle: 'Strategy Title',
      category: 'Strategy Category',
      author: 'Author',
      summary: 'Strategy Summary',
      content: 'Strategy Content',
      tags: 'Tags',
      readCount: 'Read Count',
      likeCount: 'Like Count',
      status: 'Publish Status',
      published: 'Published',
      draft: 'Draft',
      createTime: 'Create Time',
      updateTime: 'Update Time',
      operation: 'Operation',
      detail: 'Detail',
      sort: 'Sort',
      remark: 'Remark',
      routeStrategy: 'Route Strategy',
      equipmentStrategy: 'Equipment Strategy',
      skillStrategy: 'Skill Strategy',
      otherStrategy: 'Other Strategy',
      addStrategy: 'Add Strategy',
      editStrategy: 'Edit Strategy',
      viewDetail: 'View Detail',
      confirmDelete: 'Are you sure to delete?',
      warning: 'Warning',
      search: 'Search',
      reset: 'Reset',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      export: 'Export',
      confirm: 'Confirm',
      cancel: 'Cancel',
      index: 'Index',
      placeholders: {
        strategyTitle: 'Please enter strategy title',
        category: 'Please select strategy category',
        author: 'Please enter author',
        summary: 'Please enter strategy summary',
        content: 'Please enter strategy content',
        tags: 'Please enter tags'
      },
      validations: {
        titleRequired: 'Strategy title cannot be empty',
        categoryRequired: 'Strategy category cannot be empty',
        authorRequired: 'Author cannot be empty',
        contentRequired: 'Strategy content cannot be empty'
      },
      deleteConfirm: 'Are you sure to delete this strategy?',
      exportConfirm: 'Are you sure to export current data?',
      addSuccess: 'Added successfully',
      updateSuccess: 'Updated successfully',
      deleteSuccess: 'Deleted successfully'
    }
  },
  discover: {
    posted: {
      source: {
        "0": "official",
        "1": "Car owner"
      },
      brandLabel: "BrandLabel",
      video: "Video",
      dialog: "Details",
      content: "Posts content",
      contentPal: "Please input posts content",
      userKey: "Nickname",
      userPal: "Please input nickname",
      time: "Time range",
      beginTime: "Started date",
      endTime: "Ended date",
      status: "Posts status",
      slelePal: "Please choose",
      search: "Search",
      reset: "Reset",
      index: "No.",
      headName: "Profile photo",
      backName: "Background image",
      nickName: "Nickname",
      content: "Content",
      commentNum: "Comments",
      likeNum: "Liked",
      imgs: "Pictures",
      userType: "Source",
      likeNum: "Liked",
      status: "Posts status",
      handle: "Operate",
      look: "View comments",
      detail: "Details",
      top: "Pin to top",
      cancelTop: "Cancel top",
      delete: "Delete",
      topStatus: "Top Status",
      topped: "Pinned",
      normal: "Normal",
      setTop: "Set Top"
    },
    remark: {
      comments: "Comment",
      email: "Email",
      key: "Nickname/Email",
      keyPla: "Please input user nickname/email",
      content: "Comments content",
      contentPla: "Please input comments content",
      using: "Enable",
      disabled: "Disable",
      index: "No.",
      nickName: "Nickname",
      postedId: "Post ID",
      content: "Content",
      createTime: "Created time",
      status: "Activated status"
    }
  },
  system: {
    // Add app object
    app: {
      versionNum: "Version Number",
      versionSn: "Version Serial Number"
    },
    // Add computer object
    computer: {
      desc: "Description (Chinese)",
      descEn: "Description (English)",
      status: "Status",
      createBy: "Creator",
      createTime: "Created Time",
      handle: "Actions",
      handleUpdate: "Modify Software Upgrade",
      handleAdd: "Add Software Upgrade",
      history: "Upgrade History"
    },
    user: {
      resetting: "Reset",
      deptName: "Please input department name",
      userName: "User name",
      userNamePla: "Please input user name",
      phonenumber: "Phone number",
      phonenumberPla: "Please inputPhonenumber",
      status: "Status",
      statusPla: "UserStatus",
      dateRange: "Created time",
      add: "Add",
      edit: "Modify",
      delete: "Delete",
      leading: "Import",
      export: "Export",
      userId: "User number",
      userName: "UserName",
      character: "character",
      nickName: "User nickname",
      dept: "Department",
      deptId: "Owned department",
      status: "Status",
      createTime: "Created time",
      handle: "Operate",
      userId: "User number",
      email: "Email",
      input: "Please input",
      select: "Please choose",
      password: "User password",
      sex: "User gender",
      status: "Status",
      postIds: "Position",
      roleIds: "Role",
      remark: "Remark",
      userNameRules: "User name cannot be empty",
      nickNameRules: "User nickname cannot be empty",
      deptIdRules: "Owned department cannot be empty",
      passwordRules: "User password cannot be empty",
      emailRules: "Email address cannot be empty",
      emailRulesProper: "Email address incorrect",
      phonenumberRules: "Phone number cannot be empty",
      phonenumberRulesProper: "Please input correct phone number",
      handleExport: "Are you sure to export all user's data?", // all user's  这里一个撇号报错
      handleImport: "User import",
      handleFileSuccess: "Import result",
      input: "Please input",
      handleResetPwd1: "New password",
      handleResetPwd2: "New password",
      handleUpdate: "Modify user",
      handleAdd: "Add user",
      statusChange1: "Enable",
      statusChange2: "Disable",
      statusChange3: "Are you sure to do this?",
      statusChange4: "" // 和上一句一起，需要一整句中文
    },
    role: {
      roleName: "Role name",
      roleKey: "Permission character",
      status: "Status",
      dateRange: "Created time",
      roleId: "Role number",
      roleSort: "Order",
      handle: "Operate",
      roleSort: "Order",
      roleSort: "Order",
      authority: "Data permission",
      menu: "Menu permission",
      loading: "Loading, please wait",
      remark: "Remark",
      dataScope: "Permission scope",
      dataScopeOptions: {
        a: "All data permission",
        b: "Customized data permission",
        c: "Department data permission",
        d: "Department or lower data permission",
        e: "Himself/herself data permission"
      },
      rules: {
        a: "RoleName cannot be empty",
        b: "Permission character cannot be empty",
        c: "Role order cannot be empty"
      },
      deletePoint: "Are you sure to delete this？",
      title2: "Distribute data permission",
      updateTitle: "Modify role",
      addTitle: "Add role",
      handleResetPwd2: "Reset password",
      handleResetPwd1: "password",
      input: "Change"
    },
    menu: {
      menuName: "Menu name",
      input: "Please input",
      status: "Status",
      select: "Please choose",
      icon: "Icon",
      orderNum: "Order",
      perms: "Permission Identifier",
      component: "Component path",
      createTime: "Created time",
      handle: "Operate",
      parentId: "Previous menu",
      selectParentId: "Choose previous menu",
      menuType: "Menu type",
      menuTypeM: "Content",
      menuTypeC: "Menu",
      menuTypeF: "Button",
      menuIcon: "MenuIcon",
      clickMenuIcon: "Choose icon",
      isFrame: "External link？",
      path: "Router address",
      component: "Component path",
      perms: "Permission identifier",
      visible: "Display status",
      menuStatus: "Menu status",
      yes: "Yes",
      no: "No",
      rules: {
        menuName: "Menu name cannot be empty",
        orderNum: "Menu order cannot be empty",
        path: "RouterAddress cannot be empty"
      },
      getTreeselect: "The main categories",
      handleAdd: "AddMenu",
      handleUpdate: "Modify menu",
      handleDelete: {
        text1: "",
        text2: "" // 这一行和上面一行需要统一为一句翻译
      }
    },
    dept: {
      deptName: "Department name",
      status: "Status",
      orderNum: "Order",
      createTime: "Created time",
      handle: "Operate",
      parentId: "Previous department",
      leader: "Person in charge",
      phone: "Phone number",
      email: "Email",
      depStatus: "Department status",
      rules: {
        parentId: "Previous department cannot be empty",
        deptName: "Department name cannot be empty",
        orderNum: "Menu order cannot be empty",
        email: "Please input correct Email address",
        phone: "Please input correct phone number"
      },
      handleAdd: "Add department",
      handleUpdate: "Modify department"
    },
    post: {
      postCode: "Position code",
      postId: "Position number",
      input: "Please input",
      select: "Please input",
      postName: "Position name",
      statusInput: "Position status",
      postSort: "Position order",
      status: "Status",
      createTime: "Created time",
      remark: "Remark",
      handle: "Operate",
      rules: {
        postName: "Position name cannot be empty",
        postCode: "Position code cannot be empty",
        postSort: "Position order cannot be empty"
      },
      handleAdd: "Add position",
      handleUpdate: "Modify position",
      handleDelete: {
        text1: "",
        text2: "" // 这一行和上面一行需要统一为一句翻译
      },
      handleExport: {
        text: "Are you sure to export all position data?"
      }
    },
    dict: {
      dictName: "Dictionary name",
      input: "Please input",
      select: "Please choose",
      dictType: "Dictionary type",
      status: "Status",
      createTime: "Created time",
      clearCacheSuccess: "Clear successful",
      clearCache: "Clear cache",
      dictId: "Dictionary number",
      handle: "Operate",
      remark: "Remark",
      rules: {
        dictName: "Dictionary name cannot be empty",
        dictType: "Dictionary type cannot be empty"
      },
      handleAdd: "Add dictionary type",
      handleUpdate: "Modify dictionary type",
      handleExport: {
        text: "Are you sure to export all type data?"
      },
      handleDelete: {
        text1: "YesNo确认DeleteDictionarynumber为",
        text2: "" // 这一行和上面一行需要统一为一句翻译
      }
    },
    config: {
      configName: "Parameter name",
      input: "Please input",
      select: "Please choose",
      configKey: "Parameter key name",
      configType: "System build in",
      createTime: "Created time",
      configId: "Parameter primary key",
      configKey: "Parameter key name",
      configValue: "Parameter key value",
      configType: "System build in",
      remark: "Remark",
      handle: "Operate",
      rules: {
        configName: "ParameterName cannot be empty",
        configKey: "Parameter key name cannot be empty",
        configValue: "Parameter key value cannot be empty"
      },
      handleAdd: "Add parameter",
      handleUpdate: "Modify parameter",
      handleDelete: {
        text1: "",
        text2: "" // 这一行和上面一行需要统一为一句翻译
      },
      handleExport: {
        text: "Are you sure to export all parameter data?"
      }
    },
    notice: {
      input: "Please input",
      select: "Please choose",
      noticeTitle: "Announcement title",
      createBy: "Operator",
      noticeType: "Type",
      noticeId: "No.",
      noticeType2: "AnnouncementType",
      status: "Status",
      createBy: "Creator",
      handle: "Operate",
      noticeContent: "Content",
      createTime: "Created time",
      rules: {
        noticeTitle: "AnnouncementTitle cannot be empty",
        noticeType: "AnnouncementType cannot be empty"
      },
      handleAdd: "AddAnnouncement",
      handleUpdate: "ModifyAnnouncement",
      handleDelete: {
        text1: "",
        text2: "" // 这一行和上面一行需要统一为一句翻译
      }
    },
    computer: {
      computerModel: "Instrument code",
      stopwatch: "Display",
      input: "Please input",
      select: "Please choose",
      bikeModel: "bikeModel",
      auth: "Enable",
      disabled: "Disable",
      refresh: "Refresh",
      audit: "audit",
      computerId: "Display type",
      versionName: "Version Name",
      versionCode: "Version code",
      auditStatus: "auditStatus",
      updateTime: "UpdateTime",
      desc: "Description",
      descEn: "English description",
      downLink: "Download link",
      mdCode: "Version number",
      release: "release",
      type: "Do you need force refresh？",
      status: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      handle: "Operate",
      downLink2: "Update files",
      update: "Upload",
      yes: "Yes",
      no: "No",
      rules: {
        versionName: "Version Name cannot be empty",
        versionCode: "Version number cannot be empty",
        mdCode: "md5 check value cannot be empty",
        versionNameTrue: "The correct version number, such as 1.0.0"
      },
      handleStatusChange: {
        text1: "Confirm to",
        text2: "？"
      },
      success: "Successful",
      handleAdd: "Add",
      handleUpdate: "Modify",
      handleExceed: "Currently only one file can be uploaded",
      beforeRemove: "Are you sure to remove?",
      history: "historic version"
    },
    app: {
      input: "Please input",
      select: "Please choose",
      versionSn: "VersionNo.",
      versionNum: "Version number",
      desc: "Description",
      downLink: "Download link",
      mdCode: "file's MD5 check value", // file's 正确格式  只有一个小撇号
      forceUpdate: "Do you need force refresh",
      yes: "Yes",
      no: "No",
      status: "Activated status",
      createBy: "Creator",
      createTime: "Created time",
      handler: "Operate",
      desc: "Description",
      downLinkUp: "Update files",
      radar: "Radar",
      helmet: "Helmet",
      upload: "Upload",
      mdCode: "md5 check code",
      appSystem: "App System",
      rules: {
        versionSn: "Version No. cannot be empty",
        versionNum: "Version number cannot be empty",
        mdCode: "md5 check value cannot be empty",
        downLink: "Please Upload"
      },
      handleAdd: "Add",
      handleUpdate: "Modify"
    }
  },
  deviceType: {
    IOTModel: "IOT Model",
    helmetModel: "Helmet Model",
    radarModel: "Radar Model",
    sportsCameraModel: "Sports Camera Model",
    keyModel: "Key Model",
    electronicLockModel: "Electronic Lock Model"
  },
  app: {
    swiper: {
      mainTitle: "Main Title",
      subHead: "SubHead",
      addAppSwiper: "Add App Swiper",
      editAppSwiper: "Edit App Swiper"
    }
  },
  RideSwiperTag: "Ride Swiper Tag",
  RideNewBikeTag: "Ride New Bike Tag",
  noBikeInfo: "This vehicle has no vehicle location",
  "401": "Authentication failed, unable to access System resources",
  "403": "No permission",
  "404": "Resource does not exist",
  default: "Unknow error，Please feedback to the administrator",

  50101: "Add failed，parameter key name already existed",
  50102: "Modify failed，Parameter key name already existed",
  50103: "Add failed，department name already existed",
  50104: "Modify failed，superior department could not be yourself",
  50105: "This department contains sub-departments that are not deactivated",
  50106: "There are lower-level departments and they are not allowed to be deleted",
  50107: "Department has users, are not allowed to be deleted",
  50108: "Add failed，dictionary type already existed",
  50109: "Modify failed，Dictionary type already existed",
  50110: "Add failed，Menu name already existed",
  50111: "Add menu failed，address must start from http(s):", //开头
  50112: "Modify failed，menu name already existed",
  50113: "There is a sub-menu, delete is not allowed",
  50114: "Delete The menu is distributed and cannot be deleted",
  50115: "Add failed，position name already existed",
  50116: "Add failed，position code already existed",
  50117: "Modify personally details Error，please contact the administrator",
  50118: "Modify password failed，old password was wrong",
  50119: "New password should be different as old password",
  50120: " Modify password error，please contact the administrator",
  50121: "Upload pictures error，please contact the administrator",
  50122: "Add failed，role name already existed",
  50123: " Add failed，role permission already existed",
  50124: " Add failed，sign in password already existed",
  50125: " Add failed，phone number already existed",
  50126: "Add failed，email already existed",
  50128: "Modify failed，email already existed",
  50129: "Duplicate submission is not allowed, please try again later",
  map: {
    addWaypoint: 'Add Waypoint',
    clearAll: 'Clear All',
    reset: 'Reset Map',
    waypointsList: 'Waypoints List',
    waypoint: 'Waypoint',
    editWaypoint: 'Edit Waypoint',
    waypointName: 'Waypoint Name',
    waypointNamePlaceholder: 'Please enter waypoint name',
    latitude: 'Latitude',
    longitude: 'Longitude',
    sort: 'Sort',
    previewMode: 'Map Preview Mode',
    waypointsRequired: 'Waypoints cannot be less than 2',
    noWaypoints: 'No waypoints yet, please click "Manage Waypoints" to add',
    searchPlaceholder: 'Search places...',
    addWaypointConfirm: 'Add "{name}" as a waypoint?',
    addWaypointTitle: 'Add Waypoint',
    add: 'Add',
    locationFound: 'Located at: {name}',
    noResultsFound: 'No related places found',
    invalidLocation: 'Invalid location information',
    index: 'Index',
    notSet: 'Not set',
    created: 'Created',
    getPlaceNameForForm: 'Get place name by coordinates',
    waypointsSaved: 'Waypoints saved successfully'
  },
  validation: {
    waypointNameRequired: 'Waypoint name is required',
    latitudeRequired: 'Latitude is required',
    longitudeRequired: 'Longitude is required',
    waypointsRequired: 'Please add at least one waypoint',
    coverRequired: 'Please upload an activity cover',
    activityTimeTomorrow: 'Activity time must be from tomorrow onwards'
  },
  common: {
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel'
  }
};
