export default {
	btn: {
		startUsing: '启用',
		forbidden: '禁用',
	},
	mapCoordinatePicker: {
		searchPlaceholder: '搜索地点...',
		resetMap: '重置地图',
		selectedCoordinate: '选中坐标',
		latitude: '纬度',
		longitude: '经度',
		address: '地址',
		mapLoadError: '地图库加载失败，请检查网络连接',
		mapInitError: '地图初始化失败',
		invalidPlace: '无效的地点信息',
		placeNotFound: '未找到相关地点',
		addressFetchError: '获取地址失败'
	},
	msg: {
		send: "发送"
	},
	logo: {
		title: '',
	},
	form: {
		select: '请选择',
		input: '请输入',
		upload: "请上传",
		enableData: '请选择启用的数据',
		disabledData: '请选择禁用的数据',
		to: '至'
	},
	navbar: {
		dashboard: '首页',
		github: '项目地址',
		logOut: '退出登录',
		profile: '个人中心',
		theme: '换肤',
		size: '布局大小',
	},
	login: {
		title: 'AddMotor',
		logIn: '登录',
		username: '账号',
		password: '密码',
		usernameVoid: '账号不能为空',
		passwordVoid: '密码不能为空',
		remember: '记住密码',
		smsCode: '验证码',
		smsCodeVoid: '验证码不能为空',
	},
	tagsView: {
		refresh: '刷新',
		close: '关闭',
		closeOthers: '关闭其它',
		closeAll: '关闭所有',
		download: '下载'
	},
	settings: {
		title: '系统布局配置',
		theme: '主题色',
		tagsView: '开启 Tags-View',
		fixedHeader: '固定 Header',
		sidebarLogo: '侧边栏 Logo',
	},
	queryParams: {
		reset: '重置',
		search: '搜索',
		recover: '恢复',
		delete: '删除',
		add: '新增',
		update: '编辑',
		export: '导出',
		upload: '上传',
		uploadIng: '上传中...',
		uploadErr: '上传失败',
		clickOrDragUpload: '点击或拖拽文件到此处上传'
	},
	fileUpload: {
		selectFile: '选择文件',
		uploading: '上传中...',
		uploadProgress: '上传进度',
		remaining: '剩余',
		limitReached: '已达到限制',
		uploadedFiles: '已上传文件',
		clearAll: '清空所有',
		preview: '预览',
		download: '下载',
		delete: '删除',
		filePreview: '文件预览',
		unsupportedPreview: '此文件类型不支持预览',
		confirmClearAll: '确认清空所有文件吗？',
		confirmClearAllTitle: '提示',
		confirm: '确定',
		cancel: '取消',
		clearAllSuccess: '已清空所有文件',
		deleteSuccess: '文件删除成功',
		uploadSuccess: '上传成功',
		uploadFailed: '上传失败',
		fileSizeExceeded: '文件大小不能超过',
		maxFilesReached: '最多只能上传',
		filesSelected: '个文件',
		currentlyHas: '当前已有',
		chunkProgress: '分块进度',
		fileSize: '文件大小',
		fileMd5: '文件MD5',
		uploadingFile: '正在上传',
		calculatingMd5: '计算文件MD5哈希值...',
		uploadingChunk: '上传分块',
		allChunksCompleted: '所有分块上传完成!',
		fileReadError: '文件读取失败',
		chunkReadError: '分块读取失败',
		chunkUploadFailed: '分块上传失败',
		networkError: '网络错误，正在重试...',
		dragOrClick: '拖拽文件到此处或点击选择',
		supportedFormats: '支持所有格式文件'
	},
	mqttErr: {
		Vltd: "电压检测",
		BrakeSteering: "刹车指拨",
		MotorHall: "电机霍尔",
		CommunicationDetection: "通讯检测",
		OvertemperatureDetection: "过温检测",
		Controller: "控制器",
		OvercurrentDetection: "过流检测",
		Normal: "正常",
		Failure: "故障",
		Risk: "风险",
	},
	mointerObj: {
		host: '登录主机',
		hostIP: '主机',
		SessionNumber: "会话编号",
		loginName: "登录名称",
		loginPlace: "登录地点",
		browser: "浏览器",
		OperatingSystem: "操作系统",
		loginTime: "登录时间",
		StrongRetreat: "强退",
		correctVerionNumber: "请输入正确的版本号"
	},
	dialog: {
		confirm: '确定',
		cancel: '取消',
		confirmStatus: '确定要{action}吗？',
		addSuccess: '添加成功',
		updateSuccess: '修改成功',
		deleteSuccess: '删除成功',
		cancelMedal: '取消发放成功'
	},
	equipmentModel: {
		lock: '车锁',
		ElectronicVariableSpeed: '电子变速',
		TurnSignal: '转向灯'
	},
	upgradeLog: {
		documentType: '文档类型',
		upgradeType: '升级类型',
		des: '升级描述',
		newVersion: '新版本号',
		newVersionNumber: "新版本序号",
		oldVersion: '老版本号',
		oldVersionNumber: "老版本序号",
		result: '升级结果',
		resultDesc: '升级结果描述',
		addUpgradeLog: '新增文档说明书',
		resetUpgradeLog: '修改文档说明书',
		advertiseMentLink: '广告链接',
		advertiseContent: '广告内容',
		longTermEffective: '长期有效',
		fixedDate: '固定日期'
	},
	agreement: {
		blueTooth: '蓝牙绑定帮助文档',
		scan: '扫码绑定帮助文件',
		privacy: '隐私协议',
		user: '用户声明',
		language: '语言',
		uploadFile: '请上传文件'
	},
	route: {
		home: '首页',
		Acc: '用户管理',
		GeneralEquipmentManage: '通用装备管理',
		Modelmanage: '型号管理',
		RegisterError: '注册异常管理',
		RegisterManage: '注销管理',
		UserManage: '用户管理',
		UserLevel: '用户等级管理',
		CycleRecodle: '骑行记录仪型号管理',
		User: '用户',
		Team: '组队管理',
		BMenu: 'B端菜单管理',
		Controls: '控制器升级',
		ShopManage: '购物管理',
		OrderManage: '订单管理',
		NaviagtorManage: '导航权限',
		Basic: '基础数据',
		Agreement: '文档管理',
		UserCenter: '用户管理',
		Log: '用户日志',
		BackLight: "背光管理",
		EquipmentCenter: '设备中心',
		Riding: '运动数据',
		UpgradeLog: '升级日志',
		CustomerCenter: '客户中心',
		UserDetail: 'APP用户列表',
		BikeMenu: '车辆管理',
		SmartHeadManage: '智能头盔管理',
		Equipment: '装备管理',
		Bike: '运营管理',
		Customer: '客户管理',
		Brand: '品牌管理',
		Computer: '仪表管理',
		Model: '车型管理',
		Bikes: '运营管理',
		MeterCenter: '仪表中心',
		Base: '服务支持',
		Equipmentmanage: '装备中心',
		HeadManage: '头盔',
		ModelCenter: '车型中心',
		Feedback: '用户反馈',
		ImCenter: 'IM中心',
		Carexception: '异常记录',
		Exception: '故障代码',
		Store: '售后门店',
		Help: '车辆教程',
		Fqa: 'FAQ',
		Discover: '发现管理',
		Posted: '帖子列表',
		PostedManage: '帖子管理',
		Remark: '评论管理',
		MedalManage: '勋章管理',
		HdMedalManage: '互动勋章管理',
		Advertise: '广告管理',
		BaseMedal: '运动勋章规则',
		HdBaseMedal: '互动勋章规则',
		Medal: '勋章发放',
		AccMsg: '消息管理',
		Push: '极光推送',
		Msg: '消息',
		Monitor: '系统监控',
		Monitor: '系统监控',
		Online: '在线用户',
		Job: '定时任务',
		Druid: '数据监控',
		Server: '服务监控',
		System: '系统管理',
		User: '用户信息',
		Role: '角色管理',
		Menu: '菜单管理',
		Dept: '部门管理',
		Post: '岗位管理',
		Dict: '字典管理',
		Notice: '通知公告',
		Not: '无轨迹运动',
		Have: '有轨迹运动',
		Update: '升级中心',
		Config: '参数设置',
		Operlog: '操作日志',
		Logininfor: '登录日志',
		Configure: '设备功能配置',
		Info: '车型信息',
		Gear: '车辆档位',
		Battery: '电池信息',
		BatteryUpdate: "电池升级",
		Bind: '仪表绑定',
		App: 'App版本发布',
		IOT: "IOT管理",
		IotOrder: "IOT订单",
		IOTAlarmRecord: "报警记录",
		IOTCharges: "IOT资费管理",
		IotDeviceLog: "IOT日志记录",
		IotDevicePage: "IOT设备列表",
		RemoteRecord: "远程诊断记录",
		IOTRenewal: "IOT续费管理",
		OwnEquipment: "所属装备",
		ModelManage: "型号管理",
		RadarManage: "雷达",
		SportCamearManage: "运动相机",
		KeyManage: "按键",
		ElectronicLock: "电子锁",
		DeviceManage: "设备管理",
		IotManage: "IOT",
		DeviceUpdate: "设备升级管理",
		IOTEquity: "IOT 权益",
		Tool: "系统工具",
		Build: "表单构建",
		Gen: "代码生成",
		Profile: "个人中心",
		AppSwiper: "App首页轮播",
		TeamActivity: "组队活动管理",
		RideSwiper: "试骑轮播管理",
		RideNewBike: "试骑新车管理",
		RideNewBikeList: "试骑列表",
		PhotographyGame: "摄影比赛管理",
		AwardWorks: "获奖作品管理",
		UnbindState: "解绑申述",
		TopicType: "话题类型",
		TopicManage: "话题管理",
		RidingMenu: "骑行管理",
		RidingManage: "组队管理",
		StrategyManage: "攻略管理",
		H5version: "H5版本管理",
		'Identity-auth': '身份认证',
		'Appeal': '解绑申诉',
		Guide: '引导页管理',
		'Device-ad': '设备广告',
		RidingApp: '骑行与活动',
		News: '新闻管理',
		Bouns: '用户签到管理',
		UserTask: '用户任务管理',
		Data:'字典类型'
	},
	unbindState: {
	},
	iot: {
		reportType: "上报类型",
		sendOnDevice: "设备上发",
		serviceDelivery: "服务下发",
		topicHead: "topic头",
		deviceStatus: "设备状态",
		onLine: "在线",
		offLine: "离线",
		iotName: "IOT名称",
		ipRegion: "IP区域",
		iotSecret: "IOT密文",
		iotProductKey: "IOT产品键",
		isLog: "日志记录",
		notOpen: "不开启",
		open: "开启",
		certificateFile: "证书文件",
		privateFile: "私钥文件",
		rootFile: "根证书文件",
		markAffiliation: "卡片归属",
		homonychidae: "归属领科",
		notHomonychidae: "不归属领科",
		ridingMenu: "骑行管理"
	},
	team: {
		headerLeader: '队长昵称',
		member: '成员',
		codeImg: '加群二维码',
		seeMember: '查看成员',
		teamName: '群名称',
		disslove: '解散',
		nickName: '群内昵称',
		headImg: '头像',
		type: '群内角色',
		delete: '刪除',
		teamLeader: '队长',
		teamMember: '队员'
	},
	baseData: {
		cleverHeadHelm: '智能头盔型号管理'
	},
	shopManage: {
		searchTxt: {
			order: '订单号',
			userIdOrName: '用户ID/用户昵称',
			goodName: '商品名称',
			status: '状态',
			orderInfo: "订单信息",
			goodsSnapshot: "商品快照"
		},
		tableTxt: {
			userId: '用户ID',
			payChannel: '支付通道',
			amount: '支付金额',
			orderTime: '下单时间',
			payTime: '完成支付时间',
			detail: '详情',
			price: '价格',
			name: '名称',
			productNo: "商品编号",
			isRenew: "是否续费",
			FirstFree: "首次免费",
			Renew: "续费"
		},
		stateList: {
			waitPay: '待付款',
			payed: '已付款',
			payError: '交易失败',
			cancelPay: '取消付款'
		}
	},
	navigatorManage: {
		optionTxt: {
			freeze: '冻结',
			recover: '恢复',
			data: '数据项'
		},
		tableTxt: {
			userName: '用户名称',
			payChannel: '已购权益',
			startTime: '生效时间',
			residueDate: '到期时间',
			num: '使用次数',
			opt: '操作',
			ProductSnapshot: '商品快照',
			IOTName: "设备名称"
		},
		stateList: {
			inEffect: '生效中',
			expired: '已过期',
			frozen: '已冻结'
		},
		typeList: {
			navigation: '导航',
			skin: '皮肤',
			other: '其他'
		}
	},
	home: {
		ProportionVehicles: '车型数量占比',
		InteractiveHeatRatio: '互动热度占比',
		ErrorCodeRatio: '错误代码占比',
		ProportionBrandUsers: '车型用户占比',
		sports: '用户运动记录',
		essay: '文章量/评论',
		posted: {
			comment: '评论统计',
			essay: '文章统计',
		},
		basis: '同比上周',
		faultCount: '故障记录量',
		modelId: '所属车型',
		code: '故障代码',
		errChart: '故障统计',
		carChart: '车辆统计数据',
		day: '日榜',
		week: '周榜',
		month: '月榜',
		tatal: '总榜',
		percentage: '公里',
		Rank: '排名',
		user: '用户',
		activeUserNum: '活跃用户',
		sumUserNum: '总用户',
		deliverNum: '发货车辆',
		activateNum: '绑定车辆',
		sumMileage: '总里程',
		sumEconomyTree: '节约树',
		sumCalorie: '总卡路里消耗',
		userChart: '用户统计数据',
		motionChart: '运动数据统计',
		totalUsers: '总用户量',
		totalBike: '总车量',
		bindBike: '已绑定车辆',
		faultRecordQuantity: '故障记录量',
		increaseDay: '日增长',
		increaseWeek: '周增长',
		increaseMonth: '月增长',
		sports: '用户运动记录',
		essay: '文章量/评论',
		posted: {
			growthStatistics: '用户/车辆增长统计表',
			addUserNum: '新增用户数',
			addBikeNum: '新增车辆数',
			day: '日',
			week: '周',
			month: '月',
			comment: '评论统计',
			essay: '文章统计',
			total: '总数',
			mIncrement: '月增长',
			up: '同比增长',
			totalVehicle: '总车辆',
			totalDegree: '总次数',
			monthDegree: '月次数',
			averageDegree: '平均次数',
			tracklessNumber: '无轨迹运动',
			trakesNumber: '有轨迹次数'
		},
		riding: {
			totalUploadedMiles: '总上传里数',
			haveRiding: '有轨迹骑行',
			noRiding: '无轨迹骑行',
		},
		totalRidingTime: '骑行总时长',
		ridingTotalNum: '骑行总次数',
		basis: '同比上周',
		faultCount: '故障记录量',
		modelId: '所属车型',
		code: '故障代码',
		errChart: '故障统计',
		carChart: '车辆统计数据',
		day: '日榜',
		week: '周榜',
		month: '月榜',
		tatal: '总榜',
		percentage: '公里',
		Rank: '排名',
		user: '用户',
		activeUserNum: '活跃用户',
		sumUserNum: '总用户',
		deliverNum: '发货车辆',
		activateNum: '绑定车辆',
		sumMileage: '总里程',
		sumEconomyTree: '节约树',
		sumCalorie: '总卡路里消耗',
		userChart: '用户统计数据',
		motionChart: '运动数据统计',
	},
	acc: {
		user: {
			phone: '手机号',
			phoneInput: '请输入手机号'
		},
		identityAuth: {
			title: '身份认证管理',
			userId: '用户ID',
			userName: '用户名称',
			realName: '真实姓名',
			idNumber: '身份证号',
			frontImage: '身份证正面照',
			backImage: '身份证背面照',
			selfieImage: '手持身份证自拍',
			status: '审核状态',
			statusOptions: {
				pending: '待审核',
				approved: '已通过',
				rejected: '已拒绝'
			},
			submitTime: '提交时间',
			auditTime: '审核时间',
			auditReason: '审核原因',
			auditBy: '审核人员',
			operation: '操作',
			addTitle: '新增身份认证',
			editTitle: '编辑身份认证',
			viewTitle: '查看身份认证',
			approve: '通过',
			reject: '拒绝',
			reasonInput: '请输入审核原因',
			statusChangeSuccess: '状态修改成功',
			submitReason: '提交原因',
			imageUploadTip: '只能上传jpg/png文件，且不超过2MB',
			isAdmin: '是否管理员',
			adminYes: '是',
			adminNo: '否'
		},
		userLevel: {
			title: '用户积分等级管理',
			id: 'ID',
			level: '等级',
			levelInput: '请输入等级',
			point: '等级所需积分',
			pointInput: '请输入等级所需积分',
			createTime: '创建时间',
			status: '状态',
			enabled: '启用',
			disabled: '禁用',
			index: '序号',
			addTitle: '添加用户积分等级',
			updateTitle: '修改用户积分等级',
			deleteConfirm: '是否确认删除该数据项?',
			deleteSuccess: '删除成功',
			addSuccess: '新增成功',
			updateSuccess: '修改成功',
			enableSuccess: '启用成功',
			disableSuccess: '禁用成功',
			levelRequired: '等级不能为空',
			pointRequired: '等级所需积分不能为空',
			// 用户等级区间
			interval: {
				title: '用户等级区间管理',
				intervalName: '区间名称',
				intervalNameInput: '请输入区间名称',
				minLevel: '最小等级',
				maxLevel: '最大等级',
				minPoint: '最小积分',
				maxPoint: '最大积分',
				grayIcon: '未点亮图标',
				lightenIcon: '点亮图标',
				medalList: '等级勋章',
				addInterval: '添加等级区间',
				editInterval: '编辑等级区间',
				deleteInterval: '删除等级区间',
				setTasks: '设置任务',
				taskSetSuccess: '任务设置成功',
				taskManagement: '任务管理',
				taskName: '任务名称',
				taskNameInput: '请输入任务名称',
				taskEnName: '英文名称',
				taskEnNameInput: '请输入英文名称',
				taskCode: '任务Code',
				taskCodeInput: '请输入任务Code',
				taskDescription: '描述',
				taskCoins: '金币',
				taskPoint: '积分',
				taskCondition: '条件',
				taskCycle: '周期',
				taskType: '类型',
				taskModel: '模块',
				taskStatus: '状态',
				// 任务类型枚举
				taskTypeBasic: '基础任务',
				taskTypeAdvanced: '进阶任务',
				// 任务模型枚举
				taskModelGeneral: '通用',
				taskModelSpecial: '特殊',
				// 任务类型搜索
				taskTypeSelect: '请选择类型',
				selectTasks: '选择任务',
				selectedTasks: '已选任务',
				noTasksSelected: '请选择至少一个任务',
				taskSelectionTitle: '为等级区间设置任务',
				intervalNameRequired: '区间名称不能为空',
				minLevelRequired: '最小等级不能为空',
				maxLevelRequired: '最大等级不能为空',
				minPointRequired: '最小积分不能为空',
				maxPointRequired: '最大积分不能为空',
				// 勋章类型
				medalTypes: {
					totalMileage: '总里程',
					discoveryComment: '发现评论',
					userLevel: '用户等级',
					discoveryPost: '发现发帖',
					fans: '粉丝',
					ridingGuidePublish: '骑行攻略发布',
					ridingGuideCollect: '骑行攻略收藏',
					fitnessExercise: '健身运动',
					createTeam: '创建组队',
					joinTeam: '参加组队',
					photographyPublish: '摄影发布',
					bikeCircleAdmin: '车友圈管理员',
					shoppingExpert: '购物达人榜'
				}
			}
		},
		log: {
			nickName: '用户昵称',
			nickNameInput: '请输入用户昵称',
			email: '用户邮箱',
			emailInput: '请输入用户邮箱',
			userId: '用户id',

			url: '请求的路径',
			method: '请求方法',
			param: '请求参数',
			result: '返回值',
			os: '系统',
			ip: 'ip',
			registerStatus: '注册状态',
			logoutStatus: '注销状态',
			confirmLogout: '确认注销',
			processed: '已处理',
			notProcessed: '未处理',
			registed: '已注册',
			noRegisted: '未注册',
			resetLogout: '注销',
			loggedOut: '已注销',
			notLoggedOut: '未注销',
			equipmentType: '设备类型',
			equipmentSn: '设备SN',
			deviceModel: '设备型号',
			smartCarLock: '智能车锁',
			electronicShif: '电子变速',
			smartTurnSignal: '智能转向灯'
		},
		medal: {
			userKey: '用户邮箱/昵称',
			importNameOrCodingOrSynopsis: '请输入用户邮箱/昵称',
			timeOfRelease: '发放时间',
			beginTime: '开始时间',
			endTime: '结束时间',
			search: '搜索',
			reset: '重置',
			serialNumber: '序号',
			email: '用户邮箱',
			nickName: '用户昵称',
			name: '勋章名称',
			provideCause: '发放原因',
		},
		msg: {
			msg: {
				messageType: '消息类型',
				notifyType: '通知类型',
				notifyOption: {
					homePage: '首页',
					discoveryPage: '发现页',
					H5Page: 'H5页链接',
					messageListPage: '消息列表'
				},
				search: '搜索',
				reset: '重置',
				newAdd: '新增',
				startUsing: '启用',
				forbidden: '禁用',
				serialNumber: '序号',
				how_much_coverage: '覆盖多少',
				how_much_read: '已读',
				describe: '描述',
				messageBody: '消息体',
				system: '系统',
				oneMan: '个人',
				all: '所有',
				activatedState: '激活状态',
				operation: '操作',
				update: '修改',
				title: '标题',
				pleaseInput: '请输入',
				yes: '是',
				no: '否',
				confirm: '确 定',
				cancel: '取 消',
				choose: '选择',
				messageRange: '消息范围',
				push: '推送',
				selectUser: '选择用户',
				linkAddress: '链接地址',
				whetherToPush: '是否推送',
				messageSystem: '系统消息',
				messageTypeNotBeNull: '消息类型不能为空',
				describeNotBeNull: '描述不能为空',
				messageBodyNotBeNull: '消息体不能为空',
				titleNotBeNull: '标题不能为空',
				pleaseChoosewhetherToPush: '请选择是否推送',
				linkAddressNotBeNull: '链接地址不能为空',
				pleaseChooseNotifyType: '请选择通知类型',
				pleaseChooseUser: '请选择用户',
				pleaseChoosePushRange: '请选择消息范围',
				blockUp: '停用',
				warn: '警告',
				sure: '确定要进行此操作？',
				theUser: '吗?',
				confirm: '确定',
				cancel: '取消',
				warning: 'warning',
				succeed: '成功',
				addMessage: '添加消息',
				updateMessage: '修改消息',
				what_: '吗?',
				addSucceed: '添加成功',
				updateSucceed: '修改成功',
				_confirm: '确定',
				_cancel: '取消',
			},
			push: {
				title: '标题',
				pleaseInputTitle: '请输入标题',
				describe: '描述',
				pleaseInputDescribe: '请输入描述',
				pushData: '其他内容',
				pleaseInputPushData: '请输入其他内容',
				whetherToNotifyEveryone: '是否通知所有人',
				yes: '是',
				no: '否',
				immediatelyCreate: '立即创建',
				reset: '重置',
				titleNotBeNull: '标题不能为空',
				describeNotBeNull: '描述不能为空',
				pushDatabeNotBeNull: '其他内容不能为空',
				pleaseChooseToNotifyEveryone: '请选择通知所有人',
				sendSuccess: '发送成功',
			},
		},
		riding: {
			have: {
				cyclingTime: '骑行时间',
				beginDate: '开始日期',
				endDate: '结束日期',
				search: '搜索',
				reset: '重置',
				serialNumber: '序号',
				nickName: '昵称',
				ridingTime: '行驶时间',
				email: '邮箱',
				mileage: '英里数',
				syncTime: '同步时间',
				operation: '操作',
				particulars: '详情',
				altitudeHeight: '海拔高度',
				altitudeUp: '海拔提升',
				ridingTime: '骑行时间',
				startTime: '开始骑行时间',
				calorie: '卡路里',
				carbonEmission: '碳排放',
				mileage: '骑行里程',
				maxSpeed: '最大速度',
				economyTree: '节约树',
			},
			not: {
				cyclingTime: '骑行时间',
				beginDate: '开始日期',
				endDate: '结束日期',
				search: '搜索',
				reset: '重置',
				serialNumber: '序号',
				nickName: '昵称',
				ridingTime: '行驶时间',
				email: '邮箱',
				mileage: '英里数',
				syncTime: '同步时间',
				operation: '操作',
				particulars: '详情',
				altitudeHeight: '海拔高度',
				altitudeUp: '海拔提升',
				ridingTime: '骑行时间',
				startTime: '开始骑行时间',
				calorie: '卡路里',
				carbonEmission: '碳排放',
				mileage: '骑行里程',
				maxSpeed: '最大速度',
				economyTree: '节约树',
			},
		},
		user: {
			country: '国家',
			bikeName: "车辆昵称",
			AnnualCard: "年卡",
			orderDetail: "订单详情",
			addIotCharges: "新增IOT资费",
			resetIotChares: "修改IOT资费",
			alarmType: "报警类型",
			alarmTime: "报警时间",
			DisplacementAlarm: "位移报警",
			SOSAlarm: "SOS报警",
			FenceAlarm: "围栏报警",
			VibrationAlarm: "震动报警",
			LocationInfo: "位置信息",
			diagnosticMessage: "诊断信息",
			diagnosticTime: "诊断时间",
			RenewalRecord: "续费记录",
			TripartitePlatform: "三方平台",
			Normal: "正常",
			arrears: "已欠费",
			isRead: "是否已读",
			read: "已读",
			unread: "未读",
			activatedState: '激活状态',
			nameOremail: '昵称/邮箱',
			pleaseInputNameOremail: '请输入昵称/邮箱',
			sex: '性别',
			lastActivityTime: '最后一次活动时间',
			registrationTime: '注册时间',
			beginDate: '开始日期',
			endDate: '结束日期',
			search: '搜索',
			reset: '重置',
			startUsing: '启用',
			forbidden: '禁用',
			nickName: '昵称',
			phone: '手机',
			email: '邮箱',
			man: '男',
			woman: '女',
			unknown: '未知',
			city: '城市',
			birthday: '出生日期',
			operation: '操作',
			particulars: '详情',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			theUser: '用户吗?',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			succeed: '成功',
			what_: '吗?',
			detail: {
				basicInformation: '基础信息',
				nickName: '用户昵称：',
				sex: '性别：',
				man: '男',
				woman: '女',
				unknown: '未知',
				birthday: '出生日期：',
				city: '所属城市：',
				registerTime: '注册时间：',
				status: '激活状态：',
				sumMileage: '总公里：',
				lastTime: '最近一次活动时间：',
				subordinateToTheVehicle: '所属车辆',
				carName: '名称',
				bindTime: '绑定时间',
				activated: '已激活',
				nonactivated: '未激活',
			},
		},
	},
	base: {
		carexception: {
			code: '异常代码',
			codeInput: '请输入车异常代码',
			modelId: '所属车型',
			modelIdInput: '请输入所属车型',
			customer: '所属客户',
			customerKeyInput: '请输入所属客户',
			brandId: '所属品牌',
			brandIdInput: '请输入所属品牌',
			queryTime: '上报时间',
			beginDate: '开始日期',
			endDate: '结束日期',
			reset: '重置',
			search: '搜索',
			customerName: '序号',
			email: '用户邮箱',
			nickName: '用户昵称',
			city: '用户城市',
			createTime: '上报时间',
			id: '异常ID',
			startUsing: '启用',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要',
			theUser: '用户吗?',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			succeed: '成功',
			what_: '吗?',
			addInstrument: '添加仪表',
			BikeDetail: 'BikeDetail',
			updateSucceed: '修改成功',
		},
		exception: {
			codeOrName: '异常代码/名称',
			showCode: "显示代码",
			codeOrNameInput: '请输入异常代码/名称',
			belongToCarModel: '所属车型 ',
			belongToCarModelInput: '请选择所属车型',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			serialNumber: '序号',
			code: '异常代码',
			customerName: '所属客户',
			brandName: '所属品牌',
			name: '异常名称(zh)',
			nameEn: '异常名称(en)',
			desc: '异常描述(zh)',
			descEn: '异常描述(en)',
			createBy: '创建人',
			createTime: '创建时间',
			state: '状态',
			solve: '处理方案',
			lookOver: '查看',
			operation: '操作',
			update: '修改',
			processingScheme: '处理方案',
			codeInput: '请输入异常代码',
			nameInput: '请输入异常名称',
			descInput: '请输入异常描述',
			solveInput: '请输入处理方案',
			_confirm: '确 定',
			_cancel: '取 消',
			nameNotNull: '异常名称不能为空',
			codeNotNull: '异常代码不能为空',
			descNotNull: '异常描述不能为空',
			belongToCarModelNotNull: '所属车型不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			theUser: '用户吗?',
			what_: '吗?',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			succeed: '成功',
			addCode: '添加异常代码',
			updateCode: '修改异常代码',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
		},
		topicManage: {
			topicName: '话题名称',
			typeName: '类型名称',
			topicSort: '话题排序',
			status: '状态',
			creator: '创建人',
			createTime: '创建时间',
			operation: '操作',
			add: '新增',
			edit: '编辑',
			delete: '删除',
			enable: '启用',
			disable: '禁用',
			batchEnable: '批量启用',
			batchDisable: '批量禁用',
			addTopicType: '添加话题类型',
			editTopicType: '编辑话题类型',
			pleaseEnterTopicName: '请输入话题名称',
			pleaseEnterTypeName: '请输入类型名称',
			pleaseSelectTypeName: '请选择类型名称',
			pleaseEnterTopicSort: '请输入话题排序',
			pleaseSelectStatus: '请选择状态',
			confirmDelete: '确定要删除吗？',
			deleteSuccess: '删除成功',
			deleteFailed: '删除失败',
			editSuccess: '编辑成功',
			addSuccess: '添加成功',
			operationSuccess: '操作成功',
			operationFailed: '操作失败',
			pleaseSelectData: '请选择要操作的数据',
			batchOperationConfirm: '确定要对选中的数据进行此操作吗？'
		},
		feedback: {
			type: '反馈类型',
			pleaseChoose: '请选择',
			LastReply: '回复状态',
			reset: '重置',
			search: '搜索',
			serialNumber: '序号',
			customerName: '所属客户',
			feedbackType: '反馈类型',
			desc: '描述',
			nickName: '昵称',
			email: '邮箱',
			LastReplyDate: '最后一次回复时间',
			user: '未回复',
			system: '已回复',
			operation: '操作',
			lookOver: '查看',
			respondFeedback: '回复反馈',
			wordsReply: '文字回复',
			imageReply: '图片回复',
			clickUpload: '点击上传',
			_confirm: '确 定',
			_cancel: '取 消',
			sort: '排序：',
			invertedOrder: '倒序',
			positiveSequence: '正序',
			user_: '用户：',
			reply: '回复',
			problemDescription_: '问题描述:',
			problemDescription_: '问题描述:',
			platform: '平台',
			complaint: '投诉',
			repairs: '报修',
			errorCorrection: '纠错',
			feedback: '反馈',
			suggest: '建议',
			pleaseUploadPictures: '请上传图片',
			pleaseEnterARply: '请输入回复内容',
			sendSuccess: '发送成功',
		},
		fqa: {
			nameOrCodeOrBrief: '名称/编码/简介',
			issueOrDescribeInput: '请输入问题/描述',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			serialNumber: '序号',
			questionName: '问题名',
			customerName: '所属客户',
			sort: '排序',
			reply: '答复',
			lookOver: '查看',
			activatedState: '激活状态',
			operation: '操作',
			update: '修改',
			particulars: '详情',
			customerNameInput: '请输入所属客户',
			questionNameInput: '请输入问题名',
			sortInput: '请输入排序',
			replyInput: '请输入答复',
			_confirm: '确 定',
			_cancel: '取 消',
			questionName_: '问题名:',
			activatedState_: '激活状态:',
			activated: '已激活:',
			activatedNot: '未激活:',
			sort_: '排序:',
			reply_: '答复',
			questionNameNotNull: '问题名不能为空',
			replyNotNull: '答复不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			theUser: '用户吗?',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addFQA: '添加FQA',
			updateFQA: '修改FQA',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
		},
		help: {
			carNameOrCode: '问题/描述',
			questionInput: '请输入问题',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			serialNumber: '序号',
			customerName: '所属客户',
			customerNameInput: '请输入所属客户',
			title: '名称',
			desc: '描述',
			content: '内容',
			createBy: '创建人',
			createTime: '创建时间',
			state: '状态',
			solve: '处理方案',
			lookOver: '查看',
			operation: '操作',
			update: '修改',
			customerId: '所属客户',
			titleInput: '请输入门店名称',
			descInput: '请输入门店描述',
			contentInput: '请输入内容',
			_confirm: '确 定',
			_cancel: '取 消',
			titleNotNull: '名称不能为空',
			descNotNull: '描述不能为空',
			contentNotNull: '内容不能为空',
			customerIdNotNull: '所属客户不能为空',
			startUsing: '启用',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			updateHelp: '修改帮助',
			addInstrument: '添加帮助',
		},
		medal: {
			cancelSend: '取消发放',
			medalName: '勋章名称',
			enName: '英文名称',
			enDesc: '英文简介',
			medalNameInput: '请输入勋章名称',
			AchievementsPostVal: '成就满足帖子值',
			AchievementCommentVal: '成就满足评论值',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			name: '名称',
			desc: '简介',
			illumeIcon: '点亮icon',
			notIllumeIcon: '未点亮icon',
			type: '规则类型',
			activatedState: '激活状态',
			operation: '操作',
			createBy: '创建人',
			createTime: '创建时间',
			update: '修改',
			descInput: '请输入勋章简介',
			clickUpload: '点击上传',
			rule: '发放规则',
			ruleInput: '请输入发放规则',
			selectMedalDesc: '请选择勋章简介',
			_confirm: '确 定',
			_cancel: '取 消',
			nameNotNull: '客户名称不能为空',
			codeNotNull: '客户编码不能为空',
			typeNotNull: '规则类型不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			addMedal: '添加勋章',
			addHdMedal: '添加互动勋章',
			updateMedal: '修改勋章',
			updateHdMedal: '修改互动勋章',
			astrict1: '当前限制选择 1 个文件，本次选择了',
			astrict2: '个文件，共选择了',
			astrict3: '个文件',
		},
		store: {
			website: '网址',
			shopName: '门店名称',
			shopNameInput: '请输入门店名称',
			createTime: '创建时间',
			beginDate: '开始日期',
			endDate: '结束日期',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			serialNumber: '序号',
			customerName: '所属客户',
			desc: '门店描述',
			imgs: '门店图片',
			elseImgs: '其他图片',
			address: '门店地址',
			longitude: '经度',
			latitude: '纬度',
			longitudeAndLatitude: "经纬度",
			longitudeAndLatitudeErr: "经纬度格式不正确",
			longitudeAndLatitudeScheme: "经纬度格式",
			positionPick: "位置拾取",
			GoogleMap: "Google 地图",
			TencentMap: "腾讯地图",
			contact: '联系人名称',
			contactPhone: '联系方式',
			state: '状态',
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			descInput: '请输入门店描述',
			endDate: '营业时间',
			openTime: '开门时间',
			closeTime: '关门时间',
			labels: '标签',
			labelsInput: '请输入标签',
			manager: '店长',
			managerInput: '店请输入店长名称',
			weight: '权重',
			weightInput: '请输入权重',
			addressInput: '请输入门店地址',
			latitudeInput: '请输入纬度',
			longitudeInput: '请输入经度',
			contact: '联系人名称',
			contactInput: '请输入联系人名称',
			contactPhone: '联系方式',
			contactPhoneInput: '请输入联系方式',
			contactEmail: '联系邮箱',
			contactEmailInput: '请输入联系邮箱',
			clickUpload: '点击上传',
			elseImg: '其他图片',
			_confirm: '确 定',
			_cancel: '取 消',
			shopNameNotNull: '门店名称不能为空',
			descNotNull: '门店描述不能为空',
			addressNotNull: '门店描述不能为空',
			contactNotNull: '联系人不能为空',
			contactPhoneNotNull: '联系方式不能为空',
			endDateNotNull: '营业时间不能为空',
			labelsNotNull: '标签不能为空',
			longitudeNotNull: '纬度不能为空',
			latitudeNotNull: '经度不能为空',
			managerNotNull: '店长不能为空',
			contactEmailNotNull: '联系邮箱不能为空',
			firstAidCallNotNull: '救援电话不能为空',
			scoreNotNull: '"评分不能为空"',
			weightNotNull: '"权重不能为空"',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			addShop: '添加门店',
			updateShop: '修改门店',
			astrict1: '当前限制选择 1 个文件，本次选择了',
			astrict2: '个文件，共选择了',
			astrict3: '个文件',
			businessHours: "请输入营业时间（如：9:00 ~ 17:30）",
			storeBasicInfo: "门店基础信息",
			storeOpenHours: "门店营业时间"
		},
	},
	bike: {
		advertise: {
			title: '标题',
			ValidityPeriod: '有效期'
		},
		bike: {
			isIot: '支持IOT',
			isAutoLight: "自动背光",
			backLightSet: "背光亮度配置",
			clientName: '客户名称',
			category: '类别',
			untie: '解绑',
			carNameOrCode: '车型名称',
			carNameOrCodeInput: '请输入车型名称/编码',
			brandKey: '型号名称/编码',
			brandKeyInput: '请输入型号名称/编码',
			computerKey: '仪表名称/型号',
			computerKeyInput: '请输入仪表名称/型号',
			customerKey: '所属客户',
			customerKeyInput: '请输入所属客户',
			bindState: '绑定状态',
			reset: '重置',
			search: '搜索',
			customerName: '所属客户',
			brandName: '所属品牌',
			computerName: '仪表品类',
			modelName: '车辆型号',
			nickName: '所属用户',
			registerTime: '注册时间',
			bluetooth: '蓝牙地址',
			bind: '已绑定',
			notBind: '未绑定',
			operation: '操作',
			particulars: '详情',
			batteryCapacity: '电池容量',
			bindTime: '绑定时间',
			carName: '车名',
			hardwaresn: '硬件sn',
			hardwareVersion: '硬件版本',
			leve: '等级',
			motorPower: '电机功率',
			variableSpeed: '变速',
			softwareVersion: '软件版本',
			voltage: '电压',
			volvehicleWeighttage: '车身重量',
			bandNameNotNull: '品牌名称不能为空',
			bandCodeNotNull: '品牌编码不能为空',
			belongsCustomer: '所属客户不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			startUsing: '启用',
			add: '新增车辆',
			update: '修改车辆'
		},
		brand: {
			clientInformation: '客户信息(名称/编码/简介)',
			clientInformationInput: '请输入客户名称/编码/简介',
			brandInformation: '品牌信息(名称/编码/简介)',
			brandInformationInput: '请输入品牌名称/编码/简介',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			name: '名称',
			code: '编码',
			desc: '简介',
			customerName: '所属客户',
			activatedState: '激活状态',
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			brandNameInput: '请输入品牌名称',
			brandCodeInput: '请输入品牌编码',
			brandDescInput: '请输入品牌简介',
			_confirm: '确 定',
			_cancel: '取 消',
			brandNameNotNull: '品牌名称不能为空',
			brandCodeNotNull: '品牌编码不能为空',
			brandDescNotNull: '品牌简介不能为空',
			customerNameNotNull: '所属客户不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			theUser: '用户吗?',
			addInstrument: '添加品牌',
			updateInstrument: '修改品牌',
		},
		computer: {
			name: '名称',
			nameInput: '请输入名称',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			model: '型号编码',
			modelName: '型号名称',
			instructionBook: '说明书',
			instructionBookName: '说明书名字',
			instructionLink: "说明书链接",
			country: '国家',
			viewingScreen: '显示屏',
			equipmentName: "装备名称",
			wifiName: "wifi名称",
			wifiPass: "wifi密码",
			have: '有',
			notHave: '无',
			locateFunction: '定位功能',
			activatedState: '激活状态',
			gearSet: "档位配置",
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			meterNameInput: '请输入仪表名称',
			modelInput: '请输入仪表型号',
			update: '修改',
			_confirm: '确 定',
			_cancel: '取 消',
			nameNotNull: '客户名称不能为空',
			codeNotNull: '客户编码不能为空',
			viewingScreenNotNull: '是否有显示屏能不能为空',
			locateFunctionNotNull: '是否有定位功能不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			theUser: '用户吗?',
			addInstrument: '添加仪表',
			updateInstrument: '修改仪表',
			// 提示
			placeholder: {
				productClass: '请输入品类',
				productModel: '请输入型号',
				code: '请输入编码'
			},
			// 表单
			queryTable: {
				productClass: '品类',
				productModel: '型号',
				code: '编码',
				desc: '描述'
			},
			// 校验
			rules: {
				productClass: '品类不能为空',
				productModel: '型号不能为空',
				code: '编码不能为空',
			}
		},
		customer: {
			nameOrCodeOrBrief: '名称/编码/简介',
			importNameOrCodingOrSynopsis: '请输入名称/编码/简介',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			name: '名称',
			code: '编码',
			adminName: '超级管理员用户名',
			adminPassword: '超级管理员密码',
			desc: '简介',
			activatedState: '激活状态',
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			particulars: '详情',
			nameInput: '请输入客户名称',
			codeInput: '请输入客户编码',
			adminNameInput: '请输入超级管理员用户名',
			adminPasswordInput: '请输入超级管理员密码',
			descInput: '请输入客户简介',
			customerName: '客户名称已存在',
			customerNumber: '客户编号已存在',
			adminNameExist: '超级管理员用户名已存在',
			_confirm: '确 定',
			_cancel: '取 消',
			nameNotNull: '客户名称不能为空',
			codeNotNull: '客户编码不能为空',
			adminNameNotNull: '超级管理员用户名不能为空',
			adminPasswordNotNull: '超级管理员密码不能为空',
			descNotNull: '客户简介不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			theUser: '用户吗?',
			addClient: '添加客户',
			updateClient: '修改客户',
			detailedInformation: '详细信息',
			userInformation: '用户信息',
		},
		model: {
			emailName: '编码/名称/用户昵称/邮箱',
			addHelete: '添加智能头盔型号',
			addModel: '添加型号',
			resetModel: '修改型号',
			resetHelete: '修改智能头盔型号',
			addRecl: '添加骑行记录仪型号',
			resetRecl: '修改骑行记录仪型号',
			gearGroupId: '档位配置',
			isNavi: '支持导航',
			OtherSet: "其他配置",

			carNameOrCode: '车型名称/编码',
			carNameOrCodeInput: '请输入车型名称/编码',
			brandNameOrCode: '品牌名称',
			brandNameOrCodeInput: '请输入品牌名称/编码',
			meterNameOrModelInput: '请输入仪表名称/型号',
			belongsCustomer: '所属客户',
			belongsCustomerInput: '请输入所属客户',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			name: '名称',
			code: '编码',
			desc: '简介',
			carTopImage: '车型TOP图片',
			TopImage: 'TOP图片',
			carImages: '车型展示图片(点击查看更多)',
			customerName: '所属客户',
			brandName: '所属品牌',
			activatedState: '激活状态',
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			particulars: '详情',
			lookInfo: '查看明细',
			carName: '车型名称',
			carNameInput: '请输入车型名称',
			battery: '电池标称',
			batteryInput: '请输入电池标称',
			code: '车型编码',
			codeInput: '请输入车型编码',
			customerId: '所属客户',
			brandId: '品牌',
			wheelSizeId: '轮径',
			motor: '电机功率',
			motorInput: '请输入电机功率',
			minSpeed: '最小速度',
			minSpeedInput: '请输入最小速度',
			maxSpeed: '最大速度',
			maxSpeedInput: '请输入最大速度',
			carDesc: '车型简介',
			carDescInput: '请输入车型简介',
			clickUpload: '点击上传',
			_confirm: '确 定',
			_cancel: '取 消',
			elseImg: '其他图片',
			nameNotNull: '名称不能为空',
			batteryNotNull: '电池标称不能为空',
			codeNotNull: '车型编码不能为空',
			descNotNull: '描述不能为空',
			motorNotNull: '电机功率不能为空',
			customerIdNotNull: '所属客户不能为空',
			wheelSizeIdNotNull: '轮径不能为空',
			computerIdNotNull: '仪表不能为空',
			brandIdNotNull: '品牌不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			addCarModel: '添加车型',
			updateCarModel: '修改车型',
			astrict1: '当前限制选择 1 个文件，本次选择了',
			astrict2: '个文件，共选择了',
			astrict3: '个图片',
			protocolVersion: '协议版本',
			oldVersion: '老版本',
			newVersion: '新版本',
			unitType: '单位',
			DeviceKind: "设备种类",
			DeviceAttribute: "设备属性",
			BasicParamerter: "基本参数",
			SaveTime: "保修时长",
			DeviceIcon: "设备图标",
			Meter: "仪表",
			GPS: "GPS",
			crcCode: "升级文件CRC校验值",
		},
		configure: {
			add: '添加配置',
			update: '修改配置',
			name: '配置名',
			nameInput: '请输入配置名',
			nameInput: '请输入配置名',
			code: '编码',
		},
		info: {
			cnKey: '中文键',
			cnValue: '中文值 ',
			enKey: '英文键',
			enValue: '英文值',
			input: '请输入',
			modelId: '车型',
			select: '请选择',
			del: '删除',
		},
		gear: {
			groupName: '档位组名称',
			computerGear: '仪表档位',
			def: '是否默认',
			modelId: '车型 ',
			viewGear: '显示档位',
			yes: '是',
			no: '否',
		},
		backLight: {
			lightSet: "亮度配置",
			backLightName: "背光组名称",
			computerLight: "仪表亮度值",
			viewLight: "显示亮度值",
		},
		battery: {
			capacity: '设计容量',
			nickName: '用户昵称',
			residueCapacity: '剩余容量',
			sn: '电池sn ',
			useNum: '电池充电放电次数 ',
			useRate: ' 损耗率 ',
			customerId: '客户',
			bikeId: '车辆',
		},
		bind: {
			bindType: '绑定方式',
			bluetootCh: '蓝牙',
			ScanCode: '扫码',
		},
		computer: {
			name: '名称',
			nameInput: '请输入名称',
			reset: '重置',
			search: '搜索',
			newAdd: '新增',
			startUsing: '启用',
			forbidden: '禁用',
			model: '型号编码',
			modelName: '型号名称',
			instructionBook: '说明书',
			instructionBookName: '说明书名字',
			instructionLink: "说明书链接",
			country: '国家',
			viewingScreen: '显示屏',
			equipmentName: "装备名称",
			wifiName: "wifi名称",
			wifiPass: "wifi密码",
			have: '有',
			notHave: '无',
			locateFunction: '定位功能',
			activatedState: '激活状态',
			gearSet: "档位配置",
			createBy: '创建人',
			createTime: '创建时间',
			operation: '操作',
			update: '修改',
			meterNameInput: '请输入仪表名称',
			modelInput: '请输入仪表型号',
			_confirm: '确 定',
			_cancel: '取 消',
			nameNotNull: '客户名称不能为空',
			codeNotNull: '客户编码不能为空',
			viewingScreenNotNull: '是否有显示屏能不能为空',
			locateFunctionNotNull: '是否有定位功能不能为空',
			blockUp: '停用',
			warn: '警告',
			sure: '确定要进行此操作？',
			confirm: '确定',
			cancel: '取消',
			warning: 'warning',
			what_: '吗?',
			succeed: '成功',
			addSucceed: '添加成功',
			updateSucceed: '修改成功',
			theUser: '用户吗?',
			addInstrument: '添加仪表',
			updateInstrument: '修改仪表',
			// 提示
			placeholder: {
				productClass: '请输入品类',
				productModel: '请输入型号',
				code: '请输入编码'
			},
			// 表单
			queryTable: {
				productClass: '品类',
				productModel: '型号',
				code: '编码',
				desc: '描述'
			},
			// 校验
			rules: {
				productClass: '品类不能为空',
				productModel: '型号不能为空',
				code: '编码不能为空',
			}
		},
		ridingManage: {
			title: '骑行管理',
			teamActivity: '组队活动管理',
			announcement_title: '骑行声明公告',
			activityRank: '活动排名',
			// 通用操作
			add: '新增',
			edit: '编辑',
			delete: '删除',
			export: '导出',
			search: '搜索',
			reset: '重置',
			confirm: '确定',
			cancel: '取消',
			operation: '操作',
			status: '状态',
			index: '序号',
			enable: '启用',
			disable: '禁用',
			manageWaypoints: '途径点管理',
			waypoints: '途径点',
			// 组队活动相关
			activityName: '活动名称',
			creator: '创建者',
			organizer: '组织者',
			description: '活动描述',
			detail: '活动详情',
			cover: '活动封面',
			noCover: '暂无封面',
			startTime: '开始时间',
			endTime: '结束时间',
			activityTime: '活动时间',
			timeRange: '时间范围',
			to: '至',
			participants: '参与人数',

			// 时间快捷选项
			timeShortcuts: {
				today: '今天',
				yesterday: '昨天',
				lastWeek: '最近一周',
				lastMonth: '最近一个月',
				lastThreeMonths: '最近三个月'
			},
			maxParticipants: '最大参与人数',
			distance: '距离',
			type: '活动类型',
			cyclingType: '骑行类型',
			purview: '权限设置',
			expandTime: '延长时长',
			firstPrize: '第一名奖金',
			secondPrize: '第二名奖金',
			thirdPrize: '第三名奖金',
			activityStatusLabel: '活动状态',

			// 活动类型
			activityType: {
				official: '官方',
				personal: '个人'
			},

			// 骑行类型
			cyclingTypes: {
				distance: '里程',
				route: '路线',
				leisure: '休闲',
				sport: '运动',
				competition: '竞赛'
			},

			// 权限类型
			purviewType: {
				public: '公开',
				foryou: '仅你',
				friends: '朋友'
			},

			// 活动状态
			activityStatus: {
				registration: '报名中',
				registered: '已报名',
				ongoing: '活动进行中',
				pending: '待开始',
				ended: '已结束'
			},

			// 状态类型
			statusLabel: '状态',
			statusType: {
				normal: '正常',
				disabled: '禁用'
			},

			// 公告相关
			announcement: {
				title: '公告标题',
				content: '公告内容',
				createTime: '创建时间',
				add: '新增骑行声明公告',
				edit: '修改骑行声明公告',
				deleteConfirm: '确定删除该骑行声明公告吗？',

				// 占位符
				placeholders: {
					title: '请输入公告标题',
					content: '请输入公告内容',
					searchTitle: '请输入标题搜索'
				},

				// 验证消息
				validations: {
					titleRequired: '公告标题不能为空',
					contentRequired: '公告内容不能为空'
				}
			},

			// 占位符
			placeholders: {
				activityName: '请输入活动名称',
				organizer: '请输入组织者',
				description: '请输入活动描述',
				cover: '请上传活动封面',
				startTime: '选择开始时间',
				endTime: '选择结束时间'
			},

			// 验证消息
			validations: {
				activityNameRequired: '活动名称不能为空',
				organizerRequired: '组织者不能为空',
				detailRequired: '活动详情不能为空',
				startTimeRequired: '开始时间不能为空',
				endTimeRequired: '结束时间不能为空',
				activityTimeRequired: '活动时间不能为空',
				activityNameLength: '活动名称长度应在2-50字符之间',
				detailLength: '活动详情长度应在10-500字符之间',
				cyclingTypeRequired: '骑行类型不能为空',
				distanceRequired: '距离不能为空',
				distanceMax: '距离不能超过1000公里',
				prizeMin: '奖金不能少于0元',
				prizeMax: '奖金不能超过100000元'
			},

			// 操作消息
			addTeamActivity: '新增组队活动',
			editTeamActivity: '修改组队活动',
			deleteActivityConfirm: '确定删除该组队活动吗？',
			confirmDeleteBatch: '确定要删除选中的 {count} 个活动吗？',
			selectItemsToDelete: '请选择要删除的活动',
			totalPrize: '奖金总额',
			duration: '活动时长',
			days: '天',
			hours: '小时',
			minutes: '分钟',
			justNow: '刚刚',
			minutesAgo: '分钟前',
			hoursAgo: '小时前',
			daysAgo: '天前',
			weeksAgo: '周前',
			monthsAgo: '个月前',
			minutesLater: '分钟后',
			hoursLater: '小时后',
			daysLater: '天后',
			weeksLater: '周后',
			monthsLater: '个月后',
			timeInfo: '时间信息',
			warning: '提示',
			confirmDelete: '确定删除吗？',
			viewDetail: '查看详情',
			enableSuccess: '启用成功',
			disableSuccess: '禁用成功',
			getActivityInfoFailed: '获取活动信息失败',
			rankFeatureUnavailable: '排名功能暂时不可用，请稍后重试',

			// 上传相关
			upload: {
				dragText: '将文件拖到此处，或',
				clickText: '点击上传',
				uploading: '上传中...',
				selectFile: '选择文件'
			},

			// 其他
			expandTimePlaceholder: '延长时长(小时)',
			prizePlaceholder: '请输入奖金金额',

			// 详情预览相关
			activityDetail: '活动详情',
			basicInfo: '基本信息',
			timeInfo: '时间信息',
			activityInfo: '活动信息',
			prizeInfo: '奖金信息',
			routeInfo: '路线信息',
			creatorInfo: '创建者信息',
			imageInfo: '图片信息',
			title: '标题',
			cyclingType: '骑行类型',
			activityStatus: '活动状态',
			createTime: '创建时间',
			countNum: '参与人数',
			people: '人',
			km: '公里',
			hours: '小时',
			official: '官方',
			personal: '个人',
			mileage: '里程',
			route: '路线',
			registrationOpen: '报名中',
			registered: '已报名',
			inProgress: '活动进行中',
			ended: '已结束',
			normal: '正常',
			disabled: '禁用',
			public: '公开',
			forYou: '仅你',
			friends: '朋友',
			firstPrize: '第一名',
			secondPrize: '第二名',
			thirdPrize: '第三名',
			waypoint: '途径点',
			creator: '创建者',
			groupId: '群组ID',
			cover: '封面图片',
			adImg: '广告图片',
			pointImg: '轨迹照片',
			adUrl: '广告链接',

			// 表单分组标题
			basicInfo: '基本信息',
			timeSettings: '时间设置',
			cyclingSettings: '骑行设置',
			routeManagement: '路线管理',
			prizeSettings: '奖金设置',
			statusSettings: '状态设置',
			
			// 提示信息
			coverTip: '建议上传 16:9 比例的图片，支持 JPG、PNG 格式',
			distanceTip: '单位：公里',
			waypointsCount: '个途径点',

			// 排名相关
			rank: {
				title: '活动排名',
				ranking: '排名',
				userInfo: '用户信息',
				spendTime: '用时',
				performance: '表现',
				totalParticipants: '总参与人数',
				avgTime: '平均用时',
				fastestTime: '最快用时',
				slowestTime: '最慢用时',
				excellent: '优秀',
				good: '良好',
				normal: '一般',
				average: '平均',
				statisticsNote: '注意：统计数据基于所有参与者，不受搜索条件影响',
				userDetail: '用户详情',
				basicInfo: '基本信息',
				performanceInfo: '表现信息',
				nickName: '用户昵称',
				userId: '用户ID',
				activityId: '活动ID',
				consumerImg: '消费者头像',
				headImg: '用户头像'
			}
		},
		strategyManage: {
			title: '攻略管理',
			strategyTitle: '攻略标题',
			category: '攻略分类',
			author: '作者',
			summary: '攻略摘要',
			content: '攻略内容',
			tags: '标签',
			readCount: '阅读量',
			likeCount: '点赞数',
			status: '发布状态',
			published: '已发布',
			draft: '草稿',
			createTime: '创建时间',
			updateTime: '更新时间',
			operation: '操作',
			detail: '详情',
			sort: '排序',
			remark: '备注',
			routeStrategy: '路线攻略',
			equipmentStrategy: '装备攻略',
			skillStrategy: '技巧攻略',
			otherStrategy: '其他攻略',
			addStrategy: '新增攻略',
			editStrategy: '修改攻略',
			viewDetail: '查看详情',
			confirmDelete: '确定删除吗？',
			warning: '提示',
			titleRequired: '攻略标题不能为空',
			categoryRequired: '攻略分类不能为空',
			authorRequired: '作者不能为空',
			contentRequired: '攻略内容不能为空'
		}
	},
	discover: {
		posted: {
			source: {
				"0": "官方",
				"1": "车主"
			},
			brandLabel: "品牌标签",
			video: "视频",
			dialog: '详情',
			content: '帖子内容',
			contentPal: '请输入帖子内容',
			userKey: '昵称',
			userPal: '请输入昵称',
			time: '时间范围',
			beginTime: '开始日期',
			endTime: '结束日期',
			status: '帖子状态',
			slelePal: '请选择',
			search: '搜索',
			reset: '重置',
			index: '序号',
			headName: '头像',
			backName: "背景图",
			nickName: '昵称',
			commentNum: '评论数',
			likeNum: '点赞数',
			imgs: '图片',
			userType: '来源',
			likeNum: '点赞数',
			status: '帖子状态',
			handle: '操作',
			look: '查看评论',
			detail: '详情',
			top: '置顶',
			cancelTop: '取消置顶',
			delete: '删除',
			topStatus: '置顶状态',
			topped: '已置顶',
			normal: '普通',
			setTop: '设置置顶',
		},
		remark: {
			comments: '评论',
			email: '邮箱',
			key: '昵称/邮箱',
			keyPla: '请输入用户昵称/邮箱',
			content: '评价内容',
			contentPla: '请输入评价内容',
			using: '启用',
			disabled: '禁用',
			index: '序号',
			nickName: '昵称',
			postedId: '贴子ID',
			content: '评论内容',
			createTime: '创建时间',
			status: '激活状态',
		},
	},
	system: {
		// 新增 app 对象
		app: {
			versionNum: '版本号',
			versionSn: '版本序号'
		},
		// 新增 computer 对象
		computer: {
			desc: '描述(中文)',
			descEn: '描述(英文)',
			status: '状态',
			createBy: '创建人',
			createTime: '创建时间',
			handle: '操作',
			handleUpdate: '修改软件升级',
			handleAdd: '新增软件升级',
			history: '升级历史'
		},
		user: {
			resetting: '重置',
			deptName: '请输入部门名称',
			userName: '用户名称',
			userNamePla: '请输入用户名称',
			phonenumber: '手机号码',
			phonenumberPla: '请输入手机号码',
			status: '状态',
			statusPla: '用户状态',
			dateRange: '创建时间',
			add: '新增',
			edit: '修改',
			delete: '删除',
			leading: '导入',
			export: '导出',
			userId: '用户编号',
			userName: '用户名称',
			nickName: '用户昵称',
			character: '角色',
			dept: '部门',
			deptId: '所属部门',
			status: '状态',
			createTime: '创建时间',
			handle: '操作',
			userId: '用户编号',
			email: '邮箱',
			input: '请输入',
			select: '请选择',
			password: '用户密码',
			sex: '用户性别',
			status: '状态',
			postIds: '岗位',
			roleIds: '角色',
			remark: '备注',
			userNameRules: '用户名称不能为空',
			nickNameRules: '用户昵称不能为空',
			deptIdRules: '归属部门不能为空',
			passwordRules: '用户密码不能为空',
			emailRules: '邮箱地址不能为空',
			emailRulesProper: '邮箱地址不正确',
			phonenumberRules: '手机号码不能为空',
			phonenumberRulesProper: '请输入正确的手机号码',
			handleExport: '是否确认导出所有用户数据项?',
			handleImport: '用户导入',
			handleFileSuccess: '导入结果',
			input: '请输入',
			handleResetPwd1: '的新密码',
			handleResetPwd2: '的新密码',
			handleUpdate: '修改用户',
			handleAdd: '添加用户',
			statusChange1: '启用',
			statusChange2: '停用',
			statusChange3: '确认要',
			statusChange4: '用户吗？',
		},
		role: {
			roleName: '角色名称',
			roleKey: '权限字符',
			status: '状态',
			dateRange: '创建时间',
			roleId: '角色编号',
			roleSort: '显示顺序',
			handle: '操作',
			roleSort: '显示顺序',
			roleSort: '显示顺序',
			authority: '数据权限',
			menu: '菜单权限',
			loading: '加载中，请稍后',
			remark: '备注',
			dataScope: '权限范围',
			dataScopeOptions: {
				a: '全部数据权限',
				b: '自定数据权限',
				c: '本部门数据权限',
				d: '本部门及以下数据权限',
				e: '仅本人数据权限',
			},
			rules: {
				a: '角色名称不能为空',
				b: '权限字符不能为空',
				c: '角色顺序不能为空',
			},
			deletePoint: '确定要删除此选项吗？',
			title2: '分配数据权限',
			updateTitle: '修改角色',
			addTitle: '添加角色',
			handleResetPwd2: '重置密码',
			handleResetPwd1: '密码',
			input: '修改'
		},
		menu: {
			menuName: '菜单名称',
			input: '请输入',
			status: '状态',
			select: '请选择',
			icon: '图标',
			orderNum: '排序',
			perms: '权限标识',
			component: '组件路径',
			createTime: '创建时间',
			handle: '操作',
			parentId: '上级菜单',
			selectParentId: '选择上级菜单',
			menuType: '菜单类型',
			menuTypeM: '目录',
			menuTypeC: '菜单',
			menuTypeF: '按钮',
			menuIcon: '菜单图标',
			clickMenuIcon: '点击选择图标',
			isFrame: '是否外链',
			path: '路由地址',
			component: '组件路径',
			perms: '权限标识',
			visible: '显示状态',
			menuStatus: '菜单状态',
			yes: '是',
			no: '否',
			rules: {
				menuName: '菜单名称不能为空',
				orderNum: '菜单顺序不能为空',
				path: '路由地址不能为空',
			},
			getTreeselect: '主类目',
			handleAdd: '添加菜单',
			handleUpdate: '修改菜单',
			handleDelete: {
				text1: '是否确认删除名称为',
				text2: '的数据项?',
			},
		},
		dept: {
			deptName: '部门名称',
			status: '状态',
			orderNum: '排序',
			createTime: '创建时间',
			handle: '操作',
			parentId: '上级部门',
			leader: '负责人',
			phone: '联系电话',
			email: '邮箱',
			depStatus: '部门状态',
			rules: {
				parentId: '上级部门不能为空',
				deptName: '部门名称不能为空',
				orderNum: '菜单顺序不能为空',
				email: '请输入正确的邮箱地址',
				phone: '请输入正确的手机号码',
			},
			handleAdd: '添加部门',
			handleUpdate: '修改部门',
		},
		post: {
			postCode: '岗位编码',
			postId: '岗位编号',
			input: '请输入',
			select: '请输入',
			postName: '岗位名称',
			statusInput: '岗位状态',
			postSort: '岗位排序',
			status: '状态',
			createTime: '创建时间',
			remark: '备注',
			handle: '操作',
			rules: {
				postName: '岗位名称不能为空',
				postCode: '岗位编码不能为空',
				postSort: '岗位顺序不能为空',
			},
			handleAdd: '添加岗位',
			handleUpdate: '修改岗位',
			handleDelete: {
				text1: '是否确认删除岗位编号为',
				text2: '的数据项?',
			},
			handleExport: {
				text: '是否确认导出所有岗位数据项?',
			},
		},
		dict: {
			dictName: '字典名称',
			input: '请输入',
			select: '请选择',
			dictType: '字典类型',
			status: '状态',
			createTime: '创建时间',
			clearCacheSuccess: '清理成功',
			clearCache: '清理缓存',
			dictId: '字典编号',
			handle: '操作',
			remark: '备注',
			rules: {
				dictName: '字典名称不能为空',
				dictType: '字典类型不能为空',
			},
			handleAdd: '添加字典类型',
			handleUpdate: '修改字典类型',
			handleExport: {
				text: '是否确认导出所有类型数据项?',
			},
			handleDelete: {
				text1: '是否确认删除字典编号为',
				text2: '的数据项?',
			},
		},
		config: {
			configName: '参数名称',
			input: '请输入',
			select: '请选择',
			configKey: '参数键名',
			configType: '系统内置',
			createTime: '创建时间',
			configId: '参数主键',
			configKey: '参数键名',
			configValue: '参数键值',
			configType: '系统内置',
			remark: '备注',
			handle: '操作',
			rules: {
				configName: '参数名称不能为空',
				configKey: '参数键名不能为空',
				configValue: '参数键值不能为空',
			},
			handleAdd: '添加参数',
			handleUpdate: '修改参数',
			handleDelete: {
				text1: '是否确认删除参数编号为',
				text2: '的数据项?',
			},
			handleExport: {
				text: '是否确认导出所有参数数据项',
			},
		},
		notice: {
			input: '请输入',
			select: '请选择',
			noticeTitle: '公告标题',
			createBy: '操作人员',
			noticeType: '类型',
			noticeId: '序号',
			noticeType2: '公告类型',
			status: '状态',
			createBy: '创建者',
			handle: '操作',
			noticeContent: '内容',
			createTime: '创建时间',
			rules: {
				noticeTitle: '公告标题不能为空',
				noticeType: '公告类型不能为空',
			},
			handleAdd: '添加公告',
			handleUpdate: '修改公告',
			handleDelete: {
				text1: '是否确认删除公告编号为',
				text2: '的数据项?',
			},
		},
		computer: {
			computerModel: '仪表编码',
			stopwatch: '码表',
			bikeModel: '车型',
			input: '请输入',
			select: '请选择',
			auth: '启用',
			disabled: '禁用',
			refresh: '刷新',
			audit: '审核',
			computerId: '码表类型',
			versionName: '版本名',
			versionCode: '版本code',
			desc: '描述',
			descEn: '英文描述',
			downLink: '下载链接',
			mdCode: '版本号',
			release: '发布',
			type: '是否强制更新',
			status: '启用状态',
			createBy: '创建人',
			createTime: '创建时间',
			updateTime: '更新时间',
			audit: '审核',
			review: '复核',
			auditStatus: '审核状态',
			toAudit: '待审核',
			toReview: '待复核',
			done: '已完成',
			handle: '操作',
			downLink2: '升级文件',
			update: '点击上传',
			yes: '是',
			no: '否',
			rules: {
				versionName: '版本名不能为空',
				versionCode: '版本号不能为空',
				mdCode: 'md5校验值不能为空',
				versionNameTrue: '正确的版本号，如：1.0.0'
			},
			handleStatusChange: {
				text1: '确认要',
				text2: '吗？',
			},
			success: '成功',
			handleAdd: '添加',
			handleUpdate: '修改',
			handleExceed: '当前限制选择 1 个文件',
			beforeRemove: '确定移除',
			history: '历史版本'
		},
		app: {
			input: '请输入',
			select: '请选择',
			versionSn: '版本序号',
			versionNum: '版本号',
			desc: '描述',
			downLink: '下载链接',
			mdCode: '文件的MD5校验值',
			forceUpdate: '是否强制更新',
			yes: '是',
			no: '否',
			status: '激活状态',
			createBy: '创建人',
			createTime: '创建时间',
			handler: '操作',
			desc: '描述',
			downLinkUp: '升级文件',
			upload: '点击上传',
			mdCode: 'md5校验码',
			appSystem: 'app系统',
			radar: "雷达",
			helmet: "头盔",
			rules: {
				versionSn: '版本序号不能为空',
				versionNum: '版本号不能为空',
				mdCode: 'md5校验值不能为空',
				downLink: '请上传',
			},
			handleAdd: '添加',
			handleUpdate: '修改',

		},
	},
	deviceType: {
		IOTModel: "IOT型号",
		helmetModel: "头盔型号",
		radarModel: "雷达型号",
		sportsCameraModel: "运动相机型号",
		keyModel: "按键型号",
		electronicLockModel: "电子锁型号"
	},
	checkConfigData: {
		illegal: "不合法",
	},
	app: {
		swiper: {
			mainTitle: "主标题",
			subHead: "副标题",
			addAppSwiper: "新增App首页轮播图",
			editAppSwiper: "编辑App首页轮播图",
		}
	},
	TeamActivity: {
		activityName: '活动名称'
	},
	RideSwiperTag: "试骑轮播",
	RideNewBikeTag: "试骑新车管理",
	"noBikeInfo": "该车辆暂无车辆位置",
	'401': '认证失败，无法访问系统资源',
	'403': '当前操作没有权限',
	'404': '访问资源不存在',
	default: '系统未知错误，请反馈给管理员',

	"50101": '新增失败，参数键名已存在',
	"50102": '修改失败，参数键名已存在',
	"50103": '新增失败，部门名称已存在',
	"50104": '修改失败，上级部门不能是自己',
	"50105": '该部门包含未停用的子部门！',
	"50106": '存在下级部门,不允许删除',
	"50107": '部门存在用户,不允许删除',
	50108: '新增失败，字典类型已存在',
	50109: '修改失败，字典类型已存在',
	50110: '新增失败，菜单名称已存在',
	50111: ' 新增菜单失败，地址必须以http(s):', //开头
	50112: '修改失败，菜单名称已存在',
	50113: '存在子菜单,不允许删除',
	50114: '菜单已分配,不允许删除',
	50115: '新增失败，岗位名称已存在',
	50116: '新增失败，岗位编码已存在',
	50117: '修改个人信息异常，请联系管理员',
	50118: '修改密码失败，旧密码错误',
	50119: '新密码不能与旧密码相同',
	50120: ' 修改密码异常，请联系管理员',
	50121: '上传图片异常，请联系管理员',
	50122: '新增失败，角色名称已存在',
	50123: ' 新增失败，角色权限已存在',
	50124: ' 新增失败，登录账号已存在',
	50125: ' 新增失败，手机号码已存在',
	50126: '新增失败，邮箱账号已存在',
	50128: '修改失败，邮箱账号已存在',
	50129: '不允许重复提交，请稍后再试',
	map: {
		addWaypoint: '添加途径点',
		clearAll: '清空所有',
		reset: '重置地图',
		waypointsList: '途径点列表',
		waypoint: '途径点',
		editWaypoint: '编辑途径点',
		waypointName: '途径点名称',
		waypointNamePlaceholder: '请输入途径点名称',
		latitude: '纬度',
		longitude: '经度',
		sort: '排序',
		previewMode: '地图预览模式',
		waypointsRequired: '途径点不能少于2个',
		noWaypoints: '暂无途径点，请点击"管理途径点"添加',
		searchPlaceholder: '搜索地点...',
		addWaypointConfirm: '是否将"{name}"添加为途径点？',
		addWaypointTitle: '添加途径点',
		add: '添加',
		locationFound: '已定位到: {name}',
		noResultsFound: '未找到相关地点',
		invalidLocation: '无效的地点信息',
		index: '序号',
		notSet: '未设置',
		created: '创建时间',
		getPlaceNameForForm: '根据坐标获取地名',
		waypointsSaved: '途径点保存成功'
	},
	validation: {
		waypointNameRequired: '途径点名称不能为空',
		latitudeRequired: '纬度不能为空',
		longitudeRequired: '经度不能为空',
		waypointsRequired: '请添加至少一个途径点',
		coverRequired: '请上传活动封面',
		activityTimeTomorrow: '活动时间必须是明天及以后'
	},
	common: {
		edit: '编辑',
		delete: '删除',
		save: '保存',
		cancel: '取消'
	}
};
