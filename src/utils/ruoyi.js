import store from "../store";
import axios from "axios";
import moment from "moment";
import { Loading, Message } from "element-ui";

/**
 * 深拷贝
 * @param {Object} source 拷贝对象
 * @returns {Object}
 */
export function extend(source) {
  let target;
  if (typeof source === "object") {
    target = Array.isArray(source) ? [] : {};
    for (let key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] !== "object") {
          target[key] = source[key];
        } else {
          target[key] = extend(source[key]);
        }
      }
    }
  } else {
    target = source;
  }
  return target;
}

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */

const baseURL = process.env.VUE_APP_BASE_API;

// 日期格式化
export function parseTime(time, pattern) {
  try {
    // 处理时间戳转换
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time.replace(new RegExp(/-/gm), "/");
    } else if (!Number.isInteger(time) && typeof time !== "object") {
      time = +new Date(time);
    }
    
    // 处理10位时间戳（秒级）转换为13位时间戳（毫秒级）
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    
    const format = pattern || "{y}-{m}-{d} {h}:{i}";
    if (store.state.app.language == "zh") {
      if (arguments.length === 0 || !time) {
        return null;
      }

      let date;
      if (typeof time === "object") {
        date = time;
      } else {
        date = new Date(time);
      }
      const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
      };
      const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key];
        // Note: getDay() returns 0 on Sunday
        if (key === "a") {
          return ["日", "一", "二", "三", "四", "五", "六"][value];
        }
        if (result.length > 0 && value < 10) {
          value = "0" + value;
        }
        return value || 0;
      });
      return time_str;
    } else {
      // 确保英文模式下也处理时间戳
      if (arguments.length === 0 || !time) {
        return null;
      }
      let dt = new Date(time);
      let m = new Array(
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Spt",
        "Oct",
        "Nov",
        "Dec"
      );
      let w = new Array(
        "Monday",
        "Tuseday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday"
      );
      let d = new Array("st", "nd", "rd", "th");
      let mn = dt.getMonth();

      let wn = dt.getDay();
      let dn = dt.getDate();
      let dns;
      if (dn % 10 < 1 || dn % 10 > 3) {
        dns = d[3];
      } else {
        dns = d[(dn % 10) - 1];
        if (dn == 11 || dn == 12) {
          dns = d[3];
        }
      }
      if (format === "{m}") {
        return m[mn];
      } else {
        return `${dn}-${mn + 1}-${dt.getFullYear()}`;
      }
    }
  } catch (error) {
    console.error(error);
  }
}

export function formattedDurationTime(
  time,
  measure = "seconds",
  formatType = "HH:mm:ss"
) {
  const duration = moment.duration(time, measure);
  const formattedDuration = moment
    .utc(duration.asMilliseconds())
    .format(formatType);

  return formattedDuration;
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 去除表单项校验
export function clearValidateItem(refName, formItemName) {
  if (this.$refs[refName]) {
    this.$refs[refName].clearValidate(formItemName);
  }
}

// 表单title宽度
export function formlabelW(W = 100) {
  if (store.getters.language === "en") {
    return `${W + 20}px`;
  } else {
    return `${W}px`;
  }
}

// 添加日期范围
export function addDateRange(
  params,
  dateRange,
  { beginTime = "beginTime", endTime = "endTime", timestampLen = 13 } = {}
) {
  if (
    Array.isArray(dateRange) &&
    dateRange.length > 0 &&
    (timestampLen === 13 || timestampLen === 10)
  ) {
    params[beginTime] =
      timestampLen === 13 ? dateRange[0] : dateRange[0] / 1000;
    params[endTime] = timestampLen === 13 ? dateRange[1] : dateRange[1] / 1000;
  } else {
    dateRange === null && (dateRange = []);
    params[beginTime] = dateRange[0] || "";
    params[endTime] = dateRange[1] || "";
  }
  return params;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  var actions = [];
  Object.keys(datas).map(key => {
    if (datas[key].dictValue == "" + value) {
      actions.push(datas[key].dictLabel);
      return false;
    }
  });
  return actions.join("");
}

// 通用下载方法
export function download(fileName) {
  window.location.href =
    baseURL +
    "/common/download?fileName=" +
    encodeURI(fileName) +
    "&delete=" +
    true;
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments,
    flag = true,
    i = 1;
  str = str.replace(/%s/g, function () {
    var arg = args[i++];
    if (typeof arg === "undefined") {
      flag = false;
      return "";
    }
    return arg;
  });
  return flag ? str : "";
}

// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
  id = id || "id";
  parentId = parentId || "parentId";
  children = children || "children";
  rootId = rootId || 0;
  //对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data));
  //循环所有项
  const treeData = cloneData.filter(father => {
    let branchArr = cloneData.filter(child => {
      //返回每一项的子级数组
      return father[id] === child[parentId];
    });
    branchArr.length > 0 ? (father.children = branchArr) : "";
    //返回第一层
    return father[parentId] === rootId;
  });
  return treeData != "" ? treeData : data;
}

/**
 *
 * @param {*} obj 数据源
 * @returns Bool
 */
export function is_Empty(obj) {
  if (
    typeof obj == "undefined" ||
    obj == null ||
    (typeof obj == "string" && obj.trim() == "") ||
    (typeof obj == "object" && Object.keys(obj).length === 0)
  ) {
    return true;
  } else {
    return false;
  }
}

/**
 *
 * @param {*} longitude 经度
 * @param {*} latitude 纬度
 *
 */
export function toBDMap({
  longitude,
  latitude,
  title = "车辆位置",
  content = ""
} = {}) {
  window.open(
    `http://api.map.baidu.com/marker?location=${latitude},${longitude}&title=${title}&content=${content}&output=html&src=webapp.baidu.openAPIdemo`
  );
}

/**
 *
 * @param {*} path 页面地址
 * @param {*} query 页面跳转参数
 */
export function toJumpPagePath({ path, query = {} } = {}) {
  this.$router.push({
    path,
    query
  });
}

// TABLE 通用高度
export function tableHeight(num) {
  num = num ? num : 0;
  return document.documentElement.clientHeight - 330 + num;
}

// 图片地址格式化
export function transFileUrl(url) {
  if (!url) {
    throw new Error("转化的图片地址不能为空");
  }
  return url.slice(url.lastIndexOf("/") + 1);
}

/**
 * 链接下载方法
 * @param {*} url
 */

const loadingFn = () => {
  return new Promise((resolve, reject) => {
    const downloadLoadingInstance = Loading.service({
      lock: true,
      text: "正在下载数据，请稍候",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)"
    });

    setTimeout(() => {
      resolve(downloadLoadingInstance);
    }, 1000);
  });
};

export async function urlDownload(url) {
  console.log("🚀 ~ file: ruoyi.js:348 ~ url:", url)
  let downloadLoadingInstance = await loadingFn();
  axios({
    url,
    method: "get",
    responseType: "arraybuffer"
  }).then(res => {
    const { data, headers } = res;
    const fileName = transFileUrl(url);
    saveImage(data, headers, fileName);
  });

  const saveImage = (data, headers, fileName) => {
    const blob = new Blob([data], { type: headers["content-type"] });
    saveAs(blob, fileName);
  };

  const saveAs = (blob, fileName) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
    downloadLoadingInstance.close();
  };
}

export const filterUrl = url => {
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url.replace(/^https?:\/\/[^\/]+/, "");
  }
  return url;
};
