import hasRole from "./permission/hasRole";
import hasPermi from "./permission/hasPermi";
import debounce from "./module/debounce";
import throttle from "./module/throttle";
import draggable from "./module/draggable";
import { NoData } from "./commonDirective";

const install = function (Vue) {
  Vue.directive("hasRole", hasRole);
  Vue.directive("hasPermi", hasPermi);
  Vue.directive("debounce-click", debounce);
  Vue.directive("throttle-click", throttle);
  Vue.directive("NoData", NoData);
  Vue.directive("draggable", draggable);
};

if (window.Vue) {
  window["hasRole"] = hasRole;
  window["hasPermi"] = hasPermi;
  Vue.use(install); // eslint-disable-line
}

export default install;
