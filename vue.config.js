"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
const TerserPlugin = require("terser-webpack-plugin");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || "后台管理系统"; // 标题

const port = process.env.port || process.env.npm_config_port || 80; // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: "dist",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: false,
  // process.env.NODE_ENV === 'development'
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // 开发环境 source map 设置
  css: {
    sourceMap: process.env.NODE_ENV === 'development',
    loaderOptions: {
      sass: {
        // 开发环境关闭 sass 缓存以避免缓存问题
        sassOptions: {
          outputStyle: process.env.NODE_ENV === 'development' ? 'expanded' : 'compressed'
        }
      }
    }
  },
  // webpack-dev-server 相关配置
  devServer: {
    host: "0.0.0.0",
    port: port,
    open: false,
    hot: true, // 启用热更新
    hotOnly: true, // 仅使用热更新，不自动刷新页面
    compress: true, // 启用gzip压缩
    overlay: {
      warnings: false,
      errors: true
    },
    // 提高开发服务器性能
    watchOptions: {
      poll: false, // 不使用轮询
      ignored: /node_modules/, // 忽略 node_modules 文件夹
      aggregateTimeout: 300, // 文件更改后延迟重新构建的时间
    },
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: ""
        }
      }
    },
    disableHostCheck: true
  },
  configureWebpack: config => {
    config.name = name;
    config.resolve.alias = {
      "@": resolve("src")
    };

    // 开发环境优化
    if (process.env.NODE_ENV === 'development') {
      // 开发环境不需要压缩，提高编译速度
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
        // 限制内存使用
        minimize: false,
        runtimeChunk: 'single'
      };
      
      // 启用缓存
      config.cache = {
        type: "filesystem",
        buildDependencies: {
          config: [__filename]
        }
      };
      
      // 提高构建性能
      config.output = {
        ...config.output,
        pathinfo: false
      };
      
      // 限制并行处理以减少内存使用
      config.parallelism = 1;
      
    } else {
      // 生产环境优化
      config.optimization = {
        ...config.optimization,
        minimize: true,
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: false,
                pure_funcs: ["console.log"]
              }
            }
          })
        ]
      };
    }
  },
  chainWebpack(config) {
    config.plugins.delete("preload"); // TODO: need test
    config.plugins.delete("prefetch"); // TODO: need test

    // 开发环境优化
    if (process.env.NODE_ENV === 'development') {
      // 启用缓存loader (仅在环境变量启用时)
      if (process.env.VUE_APP_ENABLE_CACHE === 'true') {
        config.module
          .rule('js')
          .test(/\.js$/)
          .exclude.add(/node_modules/)
          .end()
          .use('cache-loader')
          .loader('cache-loader')
          .before('babel-loader')
          .end();
          
        config.module
          .rule('vue')
          .use('cache-loader')
          .loader('cache-loader')
          .before('vue-loader')
          .end();
      }
        
      // 多线程编译 (仅在环境变量启用时)
      if (process.env.VUE_APP_ENABLE_THREAD_LOADER === 'true') {
        config.module
          .rule('js')
          .use('thread-loader')
          .loader('thread-loader')
          .options({
            workers: require('os').cpus().length - 1, // 使用CPU核心数-1
            workerParallelJobs: 50,
            workerNodeArgs: ['--max-old-space-size=2048'], // 增加工作进程内存
            poolRespawn: false,
            poolTimeout: 2000,
            poolParallelJobs: 50
          })
          .before('babel-loader')
          .end();
      }
    }

    // set svg-sprite-loader
    config.module
      .rule("svg")
      .exclude.add(resolve("src/assets/icons"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]"
      })
      .end();

    // set preserveWhitespace
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config.when(process.env.NODE_ENV !== "development", config => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/
          }
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial" // only package third parties that are initially dependent
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      });
      config.optimization.runtimeChunk("single"),
        {
          from: path.resolve(__dirname, "./public/robots.txt"), //防爬虫文件
          to: "./" //到根目录下
        };
    });
  }
};
