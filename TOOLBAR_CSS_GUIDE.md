# 🎨 全局 Toolbar CSS 类使用指南

## 📋 概述

提供了一套全局 CSS 类来实现统一的搜索工具栏布局，支持搜索表单居左、操作按钮居右的现代化布局。

## 🚀 快速使用

### 基础用法

```vue
<template>
  <div class="app-container">
    <!-- 使用全局CSS类 -->
    <div class="search-toolbar">
      <!-- 搜索表单区域 -->
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item :label="$t('acc.log.email')" prop="email">
            <el-input
              v-model="queryParams.email"
              :placeholder="$t('acc.log.emailInput')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('acc.user.phone')" prop="phone">
            <el-input
              v-model="queryParams.phone"
              :placeholder="$t('acc.user.phoneInput')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
          >新增</el-button
        >
        <el-button type="success" icon="el-icon-download" @click="handleExport"
          >导出</el-button
        >
      </div>
    </div>

    <!-- 表格等其他内容 -->
    <el-table :data="tableData">
      <!-- ... -->
    </el-table>
  </div>
</template>
```

### 兼容原有代码

如果您的项目已经使用了 `toolbar` 和 `right-button` 类名：

```vue
<template>
  <div class="app-container">
    <!-- 兼容原有类名 -->
    <div class="toolbar">
      <!-- 搜索表单 -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        class="search-form"
      >
        <el-form-item :label="$t('acc.log.email')" prop="email">
          <el-input
            v-model="queryParams.email"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 右侧操作按钮 -->
      <div class="right-button">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="success" @click="handleExport">导出</el-button>
      </div>
    </div>
  </div>
</template>
```

## 📚 CSS 类说明

### 主要类名

| 类名              | 说明         | 用途                           |
| ----------------- | ------------ | ------------------------------ |
| `.search-toolbar` | 主容器类     | 创建 flex 布局的工具栏容器     |
| `.search-form`    | 搜索表单区域 | 左侧搜索表单容器，占据剩余空间 |
| `.action-buttons` | 操作按钮区域 | 右侧操作按钮容器，固定宽度     |
| `.toolbar`        | 兼容类名     | 等同于 `.search-toolbar`       |
| `.right-button`   | 兼容类名     | 等同于 `.action-buttons`       |

### 辅助类名

| 类名                      | 说明           | 用途                 |
| ------------------------- | -------------- | -------------------- |
| `.search-toolbar.compact` | 紧凑模式       | 减少间距的紧凑布局   |
| `.flex-toolbar`           | 纯 flex 工具栏 | 只提供基础 flex 布局 |
| `.toolbar-left`           | 左侧区域       | 独立的左侧容器类     |
| `.toolbar-right`          | 右侧区域       | 独立的右侧容器类     |

## 🎯 实际应用示例

### 1. 注册管理页面改造

**改造前**:

```vue
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.log.email')" prop="email">
        <el-input v-model="queryParams.email" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>
  </div>
</template>
```

**改造后**:

```vue
<template>
  <div class="app-container">
    <div class="search-toolbar">
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item :label="$t('acc.log.email')" prop="email">
            <el-input
              v-model="queryParams.email"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('acc.user.phone')" prop="phone">
            <el-input
              v-model="queryParams.phone"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('acc.log.nickName')" prop="nikeName">
            <el-input
              v-model="queryParams.nikeName"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
          >新增</el-button
        >
        <el-button type="success" icon="el-icon-download" @click="handleExport"
          >导出</el-button
        >
      </div>
    </div>
  </div>
</template>
```

### 2. 话题管理页面改造

```vue
<template>
  <div class="app-container">
    <div class="search-toolbar">
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item
            :label="$t('base.topicManage.topicName')"
            prop="topicName"
          >
            <el-input
              v-model.trim="queryParams.topicName"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item :label="$t('base.topicManage.typeName')" prop="typeId">
            <el-select
              v-model="queryParams.typeId"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="item in topicTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
          >新增</el-button
        >
        <el-button
          type="success"
          icon="el-icon-check"
          @click="handleBatchAuth(0)"
          >批量启用</el-button
        >
        <el-button
          type="warning"
          icon="el-icon-close"
          @click="handleBatchAuth(1)"
          >批量禁用</el-button
        >
      </div>
    </div>
  </div>
</template>
```

### 3. 紧凑模式使用

```vue
<template>
  <div class="app-container">
    <!-- 紧凑模式，减少间距 -->
    <div class="search-toolbar compact">
      <div class="search-form">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          size="small"
        >
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              style="width: 180px"
              size="small"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="action-buttons">
        <el-button type="primary" size="small" @click="handleAdd"
          >新增</el-button
        >
      </div>
    </div>
  </div>
</template>
```

## 📱 响应式效果

### 桌面端 (屏幕宽度 > 1200px)

```
[搜索表单区域 - 左侧占据剩余空间]     [操作按钮区域 - 右侧固定]
[字段1] [字段2] [字段3] [搜索] [重置]     [新增] [导出] [删除]
```

### 移动端 (屏幕宽度 ≤ 1200px)

```
[搜索表单区域 - 占据全宽]
[字段1] [字段2] [字段3] [搜索] [重置]

                    [操作按钮区域 - 右对齐]
                    [新增] [导出] [删除]
```

## 🎨 样式定制

### CSS 变量支持

可以通过 CSS 变量自定义样式：

```scss
.search-toolbar {
  --toolbar-gap: 20px; // 搜索区域和操作区域间距
  --toolbar-margin-bottom: 20px; // 工具栏底部间距
  --button-gap: 10px; // 按钮间距
  --form-item-margin: 20px; // 表单项间距
}
```

### 自定义样式

```scss
// 自定义工具栏样式
.my-custom-toolbar {
  @extend .search-toolbar;

  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;

  .search-form {
    .el-input {
      border-radius: 6px;
    }
  }

  .action-buttons {
    .el-button {
      border-radius: 6px;
    }
  }
}
```

## 🔧 最佳实践

### 1. 输入框宽度建议

```vue
<!-- 统一设置输入框宽度 -->
<el-input style="width: 200px" />
<!-- 普通输入框 -->
<el-select style="width: 150px" />
<!-- 选择器 -->
<el-date-picker style="width: 240px" />
<!-- 日期选择器 -->
```

### 2. 按钮配置建议

```vue
<!-- 主要操作在前，次要操作在后 -->
<div class="action-buttons">
  <el-button type="primary">新增</el-button>    <!-- 主要操作 -->
  <el-button type="success">导出</el-button>    <!-- 次要操作 -->
  <el-button type="danger">删除</el-button>     <!-- 危险操作 -->
</div>
```

### 3. 表单项间距

```vue
<!-- 自动处理表单项间距，无需手动设置margin -->
<el-form :inline="true">
  <el-form-item>...</el-form-item>  <!-- 自动右边距16px -->
  <el-form-item>...</el-form-item>  <!-- 自动右边距16px -->
  <el-form-item>...</el-form-item>  <!-- 最后一项无右边距 -->
</el-form>
```

## 🚀 迁移步骤

### Step 1: 识别需要改造的页面

查找使用以下模式的页面：

- `el-form` + `el-row` + `el-col` 布局
- 搜索表单和操作按钮分离的页面

### Step 2: 应用 CSS 类

```vue
<!-- 原有代码 -->
<el-form>...</el-form>
<el-row><el-col>...</el-col></el-row>

<!-- 改造后 -->
<div class="search-toolbar">
  <div class="search-form">
    <el-form>...</el-form>
  </div>
  <div class="action-buttons">
    <!-- 原有按钮 -->
  </div>
</div>
```

### Step 3: 调整样式

- 为输入框添加固定宽度
- 移除原有的 `el-row`、`el-col`、`mb8` 等类
- 保持原有的事件处理逻辑不变

## 📊 项目收益

### 1. 统一的视觉体验

- 所有页面使用相同的布局模式
- 一致的间距和对齐方式
- 现代化的用户界面

### 2. 响应式支持

- 自动适配桌面端和移动端
- 无需额外的媒体查询代码
- 良好的用户体验

### 3. 维护便利

- 全局 CSS 类，统一管理
- 易于升级和修改
- 减少重复代码

现在您可以在整个项目中使用这些全局 CSS 类，实现统一、美观、响应式的搜索工具栏布局！🎨
