{"name": "Addmotor", "version": "2.3.0", "description": "Addmotor管理系统", "author": "digitch", "scripts": {"dev": "node --max-old-space-size=4096 scripts/dev.js", "dev:fast": "node --max-old-space-size=4096 node_modules/.bin/vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint", "start": "npm run dev", "build:prod": "node --max-old-space-size=4096 scripts/build.js", "build:stage": "node --max-old-space-size=4096 scripts/build.js --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop", "create:vue": "node scripts/createVueModule.js", "extract:i18n": "node scripts/extractI18n.js", "extract:i18n:batch": "node scripts/batchExtractI18n.js", "fix:identity-auth": "node scripts/fixIdentityAuth.js"}, "husky": {"hooks": {}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@riophae/vue-treeselect": "0.4.0", "aws-sdk": "^2.1692.0", "axios": "0.18.1", "clipboard": "2.0.4", "echarts": "4.2.1", "element-ui": "^2.15.14", "file-saver": "2.0.1", "fuse.js": "3.4.4", "highlight.js": "^10.5.0", "js-beautify": "^1.10.2", "js-cookie": "2.2.0", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.21", "moment": "^2.30.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "4.2.0", "sortablejs": "1.8.4", "v-viewer": "^1.7.4", "video.js": "^8.23.3", "viewerjs": "^1.11.6", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-cropper": "0.4.9", "vue-emoji-picker": "^1.0.3", "vue-i18n": "7.3.2", "vue-qr": "^3.2.4", "vue-quill-editor": "3.0.6", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vue-video-player": "^6.0.0", "vuedraggable": "2.20.0", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/parser": "^7.7.4", "@babel/plugin-transform-runtime": "^7.15.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "cache-loader": "^4.1.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "http-proxy-middleware": "^0.19.1", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "plop": "2.3.0", "runjs": "^4.3.2", "sass": "^1.89.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "terser-webpack-plugin": "^4.2.3", "thread-loader": "^3.0.4", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}