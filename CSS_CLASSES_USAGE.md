# 📋 全局 Toolbar CSS 类使用说明

## 🎯 核心 CSS 类

已创建的全局 CSS 类，可直接在任何页面中使用：

### 主要类名

- `.search-toolbar` - 主容器类
- `.search-form` - 搜索表单区域（左侧）
- `.action-buttons` - 操作按钮区域（右侧）

### 兼容类名

- `.toolbar` - 等同于 `.search-toolbar`
- `.right-button` - 等同于 `.action-buttons`

## 🚀 基础使用模板

```vue
<template>
  <div class="app-container">
    <div class="search-toolbar">
      <!-- 搜索表单区域 -->
      <div class="search-form">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="字段1">
            <el-input v-model="queryParams.field1" style="width: 200px" />
          </el-form-item>
          <el-form-item label="字段2">
            <el-input v-model="queryParams.field2" style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="primary" icon="el-icon-plus">新增</el-button>
        <el-button type="success" icon="el-icon-download">导出</el-button>
      </div>
    </div>

    <!-- 其他内容 -->
    <el-table>...</el-table>
  </div>
</template>
```

## 🎨 布局效果

**桌面端**:

```
[搜索字段1] [搜索字段2] [搜索] [重置]     [新增] [导出] [删除]
```

**移动端**:

```
[搜索字段1] [搜索字段2] [搜索] [重置]
                                [新增] [导出] [删除]
```

## ✅ 改造步骤

### 1. 找到需要改造的页面

查找使用 `el-row` + `el-col` 布局的搜索页面

### 2. 替换布局结构

```vue
<!-- 改造前 -->
<el-form>...</el-form>
<el-row>
  <el-col><el-button>新增</el-button></el-col>
  <el-col><el-button>导出</el-button></el-col>
</el-row>

<!-- 改造后 -->
<div class="search-toolbar">
  <div class="search-form">
    <el-form>...</el-form>
  </div>
  <div class="action-buttons">
    <el-button>新增</el-button>
    <el-button>导出</el-button>
  </div>
</div>
```

### 3. 添加输入框宽度

```vue
<el-input style="width: 200px" />
<!-- 普通输入框 -->
<el-select style="width: 150px" />
<!-- 选择器 -->
<el-date-picker style="width: 240px" />
<!-- 日期选择器 -->
```

## 🎯 实际示例

### 注册管理页面（已改造）

```vue
<div class="search-toolbar">
  <div class="search-form">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('acc.log.email')">
        <el-input v-model="queryParams.email" style="width: 200px" />
      </el-form-item>
      <el-form-item :label="$t('acc.user.phone')">
        <el-input v-model="queryParams.phone" style="width: 200px" />
      </el-form-item>
      <el-form-item :label="$t('acc.log.nickName')">
        <el-input v-model="queryParams.nikeName" style="width: 200px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
  
  <div class="action-buttons">
    <el-button type="primary" icon="el-icon-plus">新增</el-button>
    <el-button type="success" icon="el-icon-download">导出</el-button>
  </div>
</div>
```

现在您可以在整个项目中使用这些全局 CSS 类，快速实现统一的 toolbar 布局！🎨
